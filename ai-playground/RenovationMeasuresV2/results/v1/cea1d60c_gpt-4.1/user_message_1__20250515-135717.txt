{"renovationCostGenerationResultItem": [{"renovationMeasureCategory": "FACADE", "renovationMeasureType": "FACADE_EXTERNAL_WALL_INSULATION", "renovationMeasureValue": "AEROGEL_4_CM_2025", "minCost": 137550.0, "maxCost": 173900.0, "mostProbableValue": 155000.0}, {"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_NEW_COVERAGE", "renovationMeasureValue": "CONCRETE_TILES", "minCost": 35470.0, "maxCost": 44340.0, "mostProbableValue": 39500.0}, {"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_NEW_COVERAGE", "renovationMeasureValue": "METAL_TILES", "minCost": 44340.0, "maxCost": 57500.0, "mostProbableValue": 51000.0}, {"renovationMeasureCategory": "FACADE", "renovationMeasureType": "FACADE_EXTERNAL_WALL_INSULATION", "renovationMeasureValue": "VACUUM_INSULATED_PANEL_5_CM_2025", "minCost": 104000.0, "maxCost": 134000.0, "mostProbableValue": 119000.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "SOLAR_PANELS", "renovationMeasureValue": "DEFAULT", "minCost": 22000.0, "maxCost": 29500.0, "mostProbableValue": 25500.0}, {"renovationMeasureCategory": "RADIATOR_UPGRADE", "renovationMeasureType": "HIGH_PERFORMANCE", "renovationMeasureValue": "DEFAULT", "minCost": 23800.0, "maxCost": 35700.0, "mostProbableValue": 29500.0}, {"renovationMeasureCategory": "BASEMENT", "renovationMeasureType": "BASEMENT_MOISTURE_PROTECTION", "renovationMeasureValue": "DEFAULT", "minCost": 35700.0, "maxCost": 47600.0, "mostProbableValue": 41000.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "DISTRICT_HEATING_2025", "minCost": 47600.0, "maxCost": 59500.0, "mostProbableValue": 54000.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "GROUND_TO_WATER_HEAT_PUMP_2025", "minCost": 71400.0, "maxCost": 95200.0, "mostProbableValue": 83000.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "BIOMASS_HEATING_2025", "minCost": 59500.0, "maxCost": 83300.0, "mostProbableValue": 71000.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "AIR_TO_WATER_HEAT_PUMP_2025", "minCost": 47600.0, "maxCost": 71400.0, "mostProbableValue": 59500.0}, {"renovationMeasureCategory": "AGE_APPROPRIATE_LIVING", "renovationMeasureType": "BARRIER_FREE_BATHROOM", "renovationMeasureValue": "DEFAULT", "minCost": 23800.0, "maxCost": 35700.0, "mostProbableValue": 29500.0}]}