{"renovationCostGenerationResultItem": [{"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_RE_ENFORCEMENT", "renovationMeasureValue": "DEFAULT", "minCost": 39000.0, "maxCost": 57000.0, "mostProbableValue": 48000.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "MANUAL_SHUTTERS", "minCost": 12400.0, "maxCost": 18600.0, "mostProbableValue": 15500.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "ELECTRIC_SHUTTERS", "minCost": 18600.0, "maxCost": 27900.0, "mostProbableValue": 23200.0}, {"renovationMeasureCategory": "RADIATOR_UPGRADE", "renovationMeasureType": "BASIC_EFFICIENCY", "renovationMeasureValue": "DEFAULT", "minCost": 14280.0, "maxCost": 21420.0, "mostProbableValue": 17800.0}, {"renovationMeasureCategory": "ELECTRIC_VEHICLE_CHARGING", "renovationMeasureType": "GRID_CONNECTION_UPGRADE", "renovationMeasureValue": "DEFAULT", "minCost": 6500.0, "maxCost": 11000.0, "mostProbableValue": 8500.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITH_BUFFER", "minCost": 5200.0, "maxCost": 8200.0, "mostProbableValue": 6700.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITHOUT_BUFFER", "minCost": 2600.0, "maxCost": 4200.0, "mostProbableValue": 3400.0}]}