{"renovationCostGenerationResultItem": [{"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_RE_ENFORCEMENT", "renovationMeasureValue": "DEFAULT", "minCost": 39000.0, "maxCost": 57000.0, "mostProbableValue": 48000.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "MANUAL_SHUTTERS", "minCost": 15500.0, "maxCost": 21000.0, "mostProbableValue": 18000.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "ELECTRIC_SHUTTERS", "minCost": 22500.0, "maxCost": 32000.0, "mostProbableValue": 27000.0}, {"renovationMeasureCategory": "RADIATOR_UPGRADE", "renovationMeasureType": "BASIC_EFFICIENCY", "renovationMeasureValue": "DEFAULT", "minCost": 14200.0, "maxCost": 21000.0, "mostProbableValue": 17500.0}, {"renovationMeasureCategory": "ELECTRIC_VEHICLE_CHARGING", "renovationMeasureType": "GRID_CONNECTION_UPGRADE", "renovationMeasureValue": "DEFAULT", "minCost": 6500.0, "maxCost": 11000.0, "mostProbableValue": 8500.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITH_BUFFER", "minCost": 6500.0, "maxCost": 9500.0, "mostProbableValue": 8000.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITHOUT_BUFFER", "minCost": 3500.0, "maxCost": 6000.0, "mostProbableValue": 4800.0}]}