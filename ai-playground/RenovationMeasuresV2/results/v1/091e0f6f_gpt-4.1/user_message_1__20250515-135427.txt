{"renovationCostGenerationResultItem": [{"renovationMeasureCategory": "FACADE", "renovationMeasureType": "FACADE_EXTERNAL_WALL_INSULATION", "renovationMeasureValue": "AEROGEL_4_CM_2025", "minCost": 101500.0, "maxCost": 137000.0, "mostProbableValue": 119000.0}, {"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_NEW_COVERAGE", "renovationMeasureValue": "CONCRETE_TILES", "minCost": 35400.0, "maxCost": 49600.0, "mostProbableValue": 42000.0}, {"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_NEW_COVERAGE", "renovationMeasureValue": "METAL_TILES", "minCost": 42500.0, "maxCost": 59500.0, "mostProbableValue": 51000.0}, {"renovationMeasureCategory": "FACADE", "renovationMeasureType": "FACADE_EXTERNAL_WALL_INSULATION", "renovationMeasureValue": "VACUUM_INSULATED_PANEL_5_CM_2025", "minCost": 86800.0, "maxCost": 116000.0, "mostProbableValue": 101000.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "SOLAR_PANELS", "renovationMeasureValue": "DEFAULT", "minCost": 22500.0, "maxCost": 29500.0, "mostProbableValue": 26000.0}, {"renovationMeasureCategory": "RADIATOR_UPGRADE", "renovationMeasureType": "HIGH_PERFORMANCE", "renovationMeasureValue": "DEFAULT", "minCost": 23800.0, "maxCost": 31200.0, "mostProbableValue": 27500.0}, {"renovationMeasureCategory": "BASEMENT", "renovationMeasureType": "BASEMENT_MOISTURE_PROTECTION", "renovationMeasureValue": "DEFAULT", "minCost": 34500.0, "maxCost": 49500.0, "mostProbableValue": 42000.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "DISTRICT_HEATING_2025", "minCost": 38500.0, "maxCost": 52500.0, "mostProbableValue": 45500.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "GROUND_TO_WATER_HEAT_PUMP_2025", "minCost": 59500.0, "maxCost": 82500.0, "mostProbableValue": 71000.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "BIOMASS_HEATING_2025", "minCost": 49500.0, "maxCost": 69500.0, "mostProbableValue": 59500.0}, {"renovationMeasureCategory": "HEATING_SYSTEM", "renovationMeasureType": "HEATING_PRIMARY_SOURCE", "renovationMeasureValue": "AIR_TO_WATER_HEAT_PUMP_2025", "minCost": 38500.0, "maxCost": 54500.0, "mostProbableValue": 46500.0}, {"renovationMeasureCategory": "AGE_APPROPRIATE_LIVING", "renovationMeasureType": "BARRIER_FREE_BATHROOM", "renovationMeasureValue": "DEFAULT", "minCost": 18500.0, "maxCost": 26500.0, "mostProbableValue": 22500.0}]}