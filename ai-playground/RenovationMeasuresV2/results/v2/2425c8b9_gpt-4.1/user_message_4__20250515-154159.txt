{"renovationCostGenerationResultItem": [{"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_RE_ENFORCEMENT", "renovationMeasureValue": "DEFAULT", "minCost": 39000.0, "maxCost": 57000.0, "mostProbableValue": 48000.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "MANUAL_SHUTTERS", "minCost": 12400.0, "maxCost": 18600.0, "mostProbableValue": 15500.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "ELECTRIC_SHUTTERS", "minCost": 18600.0, "maxCost": 26000.0, "mostProbableValue": 22000.0}, {"renovationMeasureCategory": "RADIATOR_UPGRADE", "renovationMeasureType": "BASIC_EFFICIENCY", "renovationMeasureValue": "DEFAULT", "minCost": 14280.0, "maxCost": 23800.0, "mostProbableValue": 18500.0}, {"renovationMeasureCategory": "ELECTRIC_VEHICLE_CHARGING", "renovationMeasureType": "GRID_CONNECTION_UPGRADE", "renovationMeasureValue": "DEFAULT", "minCost": 6500.0, "maxCost": 11000.0, "mostProbableValue": 8500.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITH_BUFFER", "minCost": 4200.0, "maxCost": 6700.0, "mostProbableValue": 5400.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITHOUT_BUFFER", "minCost": 2100.0, "maxCost": 3500.0, "mostProbableValue": 2800.0}]}