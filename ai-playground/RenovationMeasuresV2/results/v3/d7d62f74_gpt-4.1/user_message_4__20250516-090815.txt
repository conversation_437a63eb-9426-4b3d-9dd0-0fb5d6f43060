{"renovationCostGenerationResultItem": [{"renovationMeasureCategory": "ROOF", "renovationMeasureType": "ROOF_RE_ENFORCEMENT", "renovationMeasureValue": "DEFAULT", "minCost": 39000.0, "maxCost": 57000.0, "mostProbableValue": 48000.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "MANUAL_SHUTTERS", "minCost": 18600.0, "maxCost": 24800.0, "mostProbableValue": 21700.0}, {"renovationMeasureCategory": "WINDOWS", "renovationMeasureType": "WINDOWS_SHUTTERS", "renovationMeasureValue": "ELECTRIC_SHUTTERS", "minCost": 24800.0, "maxCost": 34100.0, "mostProbableValue": 29500.0}, {"renovationMeasureCategory": "RADIATOR_UPGRADE", "renovationMeasureType": "BASIC_EFFICIENCY", "renovationMeasureValue": "DEFAULT", "minCost": 14280.0, "maxCost": 21420.0, "mostProbableValue": 17800.0}, {"renovationMeasureCategory": "ELECTRIC_VEHICLE_CHARGING", "renovationMeasureType": "GRID_CONNECTION_UPGRADE", "renovationMeasureValue": "DEFAULT", "minCost": 4200.0, "maxCost": 7800.0, "mostProbableValue": 6000.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITH_BUFFER", "minCost": 4200.0, "maxCost": 6700.0, "mostProbableValue": 5400.0}, {"renovationMeasureCategory": "SOLAR_PANELS", "renovationMeasureType": "IMMERSION_HEATER", "renovationMeasureValue": "WITHOUT_BUFFER", "minCost": 2100.0, "maxCost": 3400.0, "mostProbableValue": 2700.0}]}