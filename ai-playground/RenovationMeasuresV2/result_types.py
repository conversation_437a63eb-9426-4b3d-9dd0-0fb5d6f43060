# types.py

from typing import List
from enum import Enum
from pydantic import BaseModel


class RenovationMeasureValue(str, Enum):
    # Default value
    DEFAULT = "DEFAULT"

    # Surface heating
    WET_SYSTEM_WITH_SCREED = "WET_SYSTEM_WITH_SCREED"
    DRY_SYSTEM_WITH_FLOORING = "DRY_SYSTEM_WITH_FLOORING"
    DRY_SYSTEM_WITHOUT_FLOORING = "DRY_SYSTEM_WITHOUT_FLOORING"
    WET_SYSTEM_WITH_RECONSTRUCTION = "WET_SYSTEM_WITH_RECONSTRUCTION"
    DRY_SYSTEM_WITH_RECONSTRUCTION = "DRY_SYSTEM_WITH_RECONSTRUCTION"

    # Heating system values
    DISTRICT_HEATING_2025 = "DISTRICT_HEATING_2025"
    GROUND_TO_WATER_HEAT_PUMP_2025 = "GROUND_TO_WATER_HEAT_PUMP_2025"
    BIOMASS_HEATING_2025 = "BIOMASS_HEATING_2025"
    AIR_TO_WATER_HEAT_PUMP_2025 = "AIR_TO_WATER_HEAT_PUMP_2025"
    HEAT_EXCHANGER = "HEAT_EXCHANGER"

    # Solar panels battery storage values
    LITHIUM_ION_BATTERY = "LITHIUM_ION_BATTERY"

    # Solar panels immersion heater values
    WITH_BUFFER = "WITH_BUFFER"
    WITHOUT_BUFFER = "WITHOUT_BUFFER"

    # Windows glazing
    TRIPLE_GLAZING = "TRIPLE_GLAZING"
    DOUBLE_GLAZING = "DOUBLE_GLAZING"

    # Windows shutters
    MANUAL_SHUTTERS = "MANUAL_SHUTTERS"
    ELECTRIC_SHUTTERS = "ELECTRIC_SHUTTERS"

    # Facade, ground floor, basement
    MINERAL_WOOL_10_CM_2025 = "MINERAL_WOOL_10_CM_2025"
    WOOD_FIBER_15_CM_2025 = "WOOD_FIBER_15_CM_2025"
    EXTRUDED_POLYSTYRENE_10_CM_2025 = "EXTRUDED_POLYSTYRENE_10_CM_2025"
    VACUUM_INSULATED_PANEL_5_CM_2025 = "VACUUM_INSULATED_PANEL_5_CM_2025"
    AEROGEL_4_CM_2025 = "AEROGEL_4_CM_2025"

    # Ceiling insulation
    FIBERGLASS_INSULATION_30_CM_2025 = "FIBERGLASS_INSULATION_30_CM_2025"

    # Roof coverage
    CLAY_TILES = "CLAY_TILES"
    NATURAL_SLATE = "NATURAL_SLATE"
    CONCRETE_TILES = "CONCRETE_TILES"
    METAL_TILES = "METAL_TILES"
    BITUMEN_MEMBRANE = "BITUMEN_MEMBRANE"
    HIGH_QUALITY_PLASTIC_FOIL = "HIGH_QUALITY_PLASTIC_FOIL"


class RenovationMeasureType(str, Enum):
    # Facade related
    FACADE_EXTERNAL_WALL_INSULATION = "FACADE_EXTERNAL_WALL_INSULATION"
    FACADE_EXTERIOR_CLADDING = "FACADE_EXTERIOR_CLADDING"

    # Roof related
    ROOF_INSULATION = "ROOF_INSULATION"
    ROOF_NEW_COVERAGE = "ROOF_NEW_COVERAGE"
    ROOF_RE_ENFORCEMENT = "ROOF_RE_ENFORCEMENT"

    # Ceiling related
    CEILING_INSULATION = "CEILING_INSULATION"

    # Windows related
    WINDOWS_GLAZING_TYPE = "WINDOWS_GLAZING_TYPE"
    WINDOWS_SHUTTERS = "WINDOWS_SHUTTERS"
    WINDOWS_SECURITY_FEATURES = "WINDOWS_SECURITY_FEATURES"

    # Ground floor related
    GROUND_FLOOR_INSULATION = "GROUND_FLOOR_INSULATION"
    GROUND_FLOOR_MOISTURE_PROTECTION = "GROUND_FLOOR_MOISTURE_PROTECTION"

    # Basement related
    BASEMENT_INSULATION = "BASEMENT_INSULATION"
    BASEMENT_MOISTURE_PROTECTION = "BASEMENT_MOISTURE_PROTECTION"

    # Doors related
    DOORS_REPLACEMENT = "DOORS_REPLACEMENT"
    DOORS_SECURITY_FEATURES = "DOORS_SECURITY_FEATURES"

    # Heating related
    HEATING_PRIMARY_SOURCE = "HEATING_PRIMARY_SOURCE"
    HEATING_VENTILATION_HEAT_EXCHANGE = "HEATING_VENTILATION_HEAT_EXCHANGE"

    # Surface heating
    UNDERFLOOR_HEATING = "UNDERFLOOR_HEATING"
    WALL_HEATING = "WALL_HEATING"
    CEILING_HEATING = "CEILING_HEATING"

    # Radiator related
    BASIC_EFFICIENCY = "BASIC_EFFICIENCY"
    HIGH_PERFORMANCE = "HIGH_PERFORMANCE"

    # Solar panels related
    BATTERY_STORAGE = "BATTERY_STORAGE"
    IMMERSION_HEATER = "IMMERSION_HEATER"
    SOLAR_PANELS = "SOLAR_PANELS"

    # Smart home energy management related
    INTELLIGENT_HEATING_CONTROLS = "INTELLIGENT_HEATING_CONTROLS"
    LIGHTING_AUTOMATION = "LIGHTING_AUTOMATION"

    # Electric vehicle charging related
    WALLBOX_INSTALLATION = "WALLBOX_INSTALLATION"
    GRID_CONNECTION_UPGRADE = "GRID_CONNECTION_UPGRADE"

    # Age appropriate living related
    BARRIER_FREE_BATHROOM = "BARRIER_FREE_BATHROOM"
    STAIRLIFT = "STAIRLIFT"
    WIDENING_DOORS = "WIDENING_DOORS"
    EMERGENCY_SYSTEMS = "EMERGENCY_SYSTEMS"


class RenovationCostGenerationResultItem(BaseModel):
    renovationMeasureCategory: str
    renovationMeasureType: RenovationMeasureType
    renovationMeasureValue: RenovationMeasureValue
    minCost: float
    maxCost: float
    mostProbableValue: float


class RenovationCostGenerationResult(BaseModel):
    renovationCostGenerationResultItem: List[RenovationCostGenerationResultItem]
