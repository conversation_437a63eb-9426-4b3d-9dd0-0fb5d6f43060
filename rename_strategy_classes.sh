#!/bin/bash

# This script renames all files with "V2" postfix in the recommendationStrategy package
# and updates the class names inside the files

# Find all files with "V2.kt" postfix
find /Users/<USER>/Projects/finacte-epc/epc-backend/src/main/kotlin/de/finacte/epc/service/calculation/renovation/recommendationStrategy -type f -name "*V2.kt" | while read file; do
  # Get the new file name without "V2"
  new_file=$(echo "$file" | sed 's/V2\.kt/\.kt/')
  
  # Get the class name from the file name
  class_name=$(basename "$file" .kt)
  
  # Get the new class name without "V2"
  new_class_name=$(echo "$class_name" | sed 's/V2$//')
  
  echo "Renaming $file to $new_file"
  echo "Changing class name from $class_name to $new_class_name"
  
  # Create a temporary file with the updated content
  sed "s/$class_name/$new_class_name/g" "$file" > "$new_file"
  
  # Remove the old file
  rm "$file"
done

echo "Renaming complete!"
