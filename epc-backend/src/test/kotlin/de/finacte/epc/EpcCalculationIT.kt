package de.finacte.epc

import de.finacte.epc.configuration.PostgresContainerInitializer
import de.finacte.epc.dto.building.BuildingDto
import de.finacte.epc.dto.calculation.CalculationResultDto
import io.kotest.common.ExperimentalKotest
import io.kotest.core.spec.style.StringSpec
import io.kotest.engine.test.logging.warn
import io.kotest.matchers.doubles.shouldBeBetween
import io.kotest.matchers.shouldBe
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration


@OptIn(ExperimentalKotest::class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
)
@ContextConfiguration(
    initializers = [PostgresContainerInitializer::class]
)
@ActiveProfiles(profiles = ["no-security", "local"])
class EpcCalculationIT(
    @LocalServerPort val localPort: Int,
    val restTemplateBuilder: RestTemplateBuilder
) : StringSpec({
    val epcApiRestTemplateBuilder =
        restTemplateBuilder.rootUri("http://localhost:" + localPort + "/api/").messageConverters()
    val testRestTemplate = TestRestTemplate(epcApiRestTemplateBuilder)


    ITFixtures.buildingDtoList.forEach { testFixture ->
        "Calculate EPC for building building (street=${testFixture.first.street} , no=${testFixture.first.no})" {
            val buildingDtoResponse: ResponseEntity<BuildingDto> =
                testRestTemplate.postForEntity(
                    "/building/",
                    testFixture.first,
                    BuildingDto::class.java
                )

            buildingDtoResponse.statusCode shouldBe HttpStatus.OK


            val epcCalculationResponse: ResponseEntity<CalculationResultDto> =
                testRestTemplate.postForEntity(
                    "/calculation/${buildingDtoResponse.body?.id}",
                    buildingDtoResponse.body?.id,
                    CalculationResultDto::class.java
                )

            val primaryEnergyDemandMin = testFixture.second.first - 50.0
            val primaryEnergyDemandMax = testFixture.second.first + 50.0

            val co2dMin = testFixture.second.third - 50.0
            val co2Max = testFixture.second.third + 50.0
            val co2EmissionsPerArea = epcCalculationResponse.body?.co2Emissions?.div(testFixture.first.area)

            epcCalculationResponse.statusCode shouldBe HttpStatus.OK
            warn { "EPC calculation for building (street=${testFixture.first.street} , no=${testFixture.first.no}) is ${epcCalculationResponse.body?.energyEfficiency}. Should be between $primaryEnergyDemandMin and $primaryEnergyDemandMax. Original EPC: ${testFixture.second.first}" }
            warn { "EPC scale grade for building (street=${testFixture.first.street} , no=${testFixture.first.no}) is ${epcCalculationResponse.body?.energyEfficiencyScaleGrade}. Should be ${testFixture.second.second}" }
            warn { "Final demand for building (street=${testFixture.first.street} , no=${testFixture.first.no}) is ${epcCalculationResponse.body?.finalEnergyDemandPerArea}." }
            warn { "EPC CO2 calculation for building (street=${testFixture.first.street} , no=${testFixture.first.no}) is ${co2EmissionsPerArea}. Should be between $co2dMin and $co2Max. Original CO2 calc: ${testFixture.second.third}" }

            epcCalculationResponse.body?.energyEfficiency?.shouldBeBetween(
                primaryEnergyDemandMin,
                primaryEnergyDemandMax,
                0.0
            )

        }
    }

})
