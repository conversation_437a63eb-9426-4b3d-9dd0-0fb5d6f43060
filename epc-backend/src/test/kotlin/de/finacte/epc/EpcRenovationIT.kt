package de.finacte.epc

import de.finacte.epc.configuration.PostgresContainerInitializer
import de.finacte.epc.dto.building.BuildingDto
import de.finacte.epc.dto.calculation.CalculationResultDto
import de.finacte.epc.dto.calculation.RenovationCostGenerationDto
import de.finacte.epc.dto.calculation.RenovationDto
import io.kotest.common.ExperimentalKotest
import io.kotest.core.spec.style.StringSpec
import io.kotest.engine.test.logging.warn
import io.kotest.matchers.shouldBe
import kotlin.system.measureTimeMillis
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration


@OptIn(ExperimentalKotest::class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = ["spring.ai.openai.api-key=\${OPENAI_API_KEY}"]
)
@ContextConfiguration(
    initializers = [PostgresContainerInitializer::class]
)
@ActiveProfiles(profiles = ["no-security", "local"])
class EpcRenovationIT(
    @LocalServerPort val localPort: Int,
    val restTemplateBuilder: RestTemplateBuilder
) : StringSpec({
    val epcApiRestTemplateBuilder =
        restTemplateBuilder.rootUri("http://localhost:" + localPort + "/api/").messageConverters()
    val testRestTemplate = TestRestTemplate(epcApiRestTemplateBuilder)



        "Calculate renovation list and costs" {
            val testFixture = ITFixtures.buildingDtoList[4] //Lühebogen

            val buildingDtoResponse: ResponseEntity<BuildingDto> =
                testRestTemplate.postForEntity(
                    "/building/",
                    testFixture.first,
                    BuildingDto::class.java
                )

            buildingDtoResponse.statusCode shouldBe HttpStatus.OK


            var epcCalculationResponse: ResponseEntity<CalculationResultDto>
            val calculationRequestTime = measureTimeMillis {
                epcCalculationResponse = testRestTemplate.postForEntity(
                    "/calculation/${buildingDtoResponse.body?.id}",
                    buildingDtoResponse.body?.id,
                    CalculationResultDto::class.java
                )
            }
            epcCalculationResponse.statusCode shouldBe HttpStatus.OK
            warn { "EPC calculation request took $calculationRequestTime ms" }

            var generateRenovationResponse: ResponseEntity<List<RenovationDto>>
            val renovationRequestTime = measureTimeMillis {
                generateRenovationResponse = testRestTemplate.exchange(
                    "/renovation/${buildingDtoResponse.body?.id}",
                    HttpMethod.POST,
                    HttpEntity(buildingDtoResponse.body!!.id!!),
                    object : ParameterizedTypeReference<List<RenovationDto>>() {}
                )
            }
            generateRenovationResponse.statusCode shouldBe HttpStatus.OK
            warn { "Renovation request took $renovationRequestTime ms" }

            var generateRenovationCostResponse: ResponseEntity<List<RenovationCostGenerationDto>>
            val renovationCostRequestTime = measureTimeMillis {
                generateRenovationCostResponse = testRestTemplate.exchange(
                    "/renovation/${buildingDtoResponse.body?.id}/costs",
                    HttpMethod.POST,
                    HttpEntity(buildingDtoResponse.body!!.id!!),
                    object : ParameterizedTypeReference<List<RenovationCostGenerationDto>>() {}
                )
            }
            generateRenovationCostResponse.statusCode shouldBe HttpStatus.OK

            warn { "Renovation costs request took $renovationCostRequestTime ms" }
            warn { "Renovation costs generated: \n ${generateRenovationCostResponse.body}" }
        }
})
