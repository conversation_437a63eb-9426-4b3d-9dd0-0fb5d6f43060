package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.CeilingInsulationRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.data.forAll
import io.kotest.data.row
import io.kotest.matchers.shouldBe

class CeilingInsulationRenovationRecommendationStrategyTest : StringSpec({
    val sut = CeilingInsulationRenovationRecommendationStrategy()


    "Testing ceiling insulation renovation strategy when there is no insulation" {
        forAll(
            row(BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED, Pair(true, true)),
            row(BuildingRoofFloor.FULLY_LIVEABLE_REDUCED, Pair(true, true)),
            row(BuildingRoofFloor.PARTIALLY_LIVEABLE, Pair(true, false)),
            row(BuildingRoofFloor.COLD_STORAGE, Pair(true, false)),
            row(BuildingRoofFloor.FLAT_ROOF, Pair(true, false)),
        ) { roofFloor, expectedResult ->

            val input = createFullyInitializedBuildingCalculationInput()
            val buildingCalculationInput = input.copy(
                ceilingWallInsulation = null,
                fixedAttributes = input.fixedAttributes.copy(
                    roofFloor = roofFloor
                )
            )
            val renovationTemplateEntity = RenovationTemplateEntity(
                id = null,
                renovationMeasureCategory = RenovationMeasureCategory.ROOF,
                renovationMeasureType = RenovationMeasureType.CEILING_INSULATION,
                renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
                renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
                renovationMeasureObject = "", //not relevant
                listOrder = 1, //not relevant
                renovationTemplateTranslations = emptyList(),
                grantPrograms = emptyList(),
                renovationMeasureDescription = "Renovation measure", //not relevant
                renovationMeasureVersion = 1 //not relevant
            )

            val result = sut.test(buildingCalculationInput, renovationTemplateEntity)
            result shouldBe expectedResult
        }
    }

    "Testing ceiling insulation renovation strategy when ceiling insulation exist" {
        forAll(
            row(BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED,  Pair(true, false)),
            row(BuildingRoofFloor.FULLY_LIVEABLE_REDUCED, Pair(true, false)),
            row(BuildingRoofFloor.PARTIALLY_LIVEABLE, Pair(true, false)),
            row(BuildingRoofFloor.COLD_STORAGE, Pair(true, false)),
            row(BuildingRoofFloor.FLAT_ROOF, Pair(true, false)),
        ) { roofFloor, expectedResult ->

            val input = createFullyInitializedBuildingCalculationInput()
            val buildingCalculationInput = input.copy(
                fixedAttributes = input.fixedAttributes.copy(
                    roofFloor = roofFloor
                )
            )
            val renovationTemplateEntity = RenovationTemplateEntity(
                id = null,
                renovationMeasureCategory = RenovationMeasureCategory.ROOF,
                renovationMeasureType = RenovationMeasureType.CEILING_INSULATION,
                renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
                renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
                renovationMeasureObject = "", //not relevant
                listOrder = 1, //not relevant
                renovationTemplateTranslations = emptyList(),
                grantPrograms = emptyList(),
                renovationMeasureDescription = "Renovation measure", //not relevant
                renovationMeasureVersion = 1 //not relevant
            )

            val result = sut.test(buildingCalculationInput, renovationTemplateEntity)
            result shouldBe expectedResult
        }
    }
})