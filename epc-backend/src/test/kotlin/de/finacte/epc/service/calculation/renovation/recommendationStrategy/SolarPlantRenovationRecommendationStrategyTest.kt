package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.SolarPlantRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class SolarPlantRenovationRecommendationStrategyTest : StringSpec({
    val sut = SolarPlantRenovationRecommendationStrategy()


    "test recommendation strategy - solar plant exist == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarPlant = BuildingCalculationInput.SolarPlant(
                solarPlantExist = true,
                solarPlantNominalPower = null,
                solarPlantInstallationYear = 1990
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PLANT,
            renovationMeasureType = RenovationMeasureType.SOLAR_PLANT_PANELS,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_PANELS_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - solar plant not exist == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarPlant = BuildingCalculationInput.SolarPlant(
                solarPlantExist = false,
                solarPlantNominalPower = null,
                solarPlantInstallationYear = 1990
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PLANT,
            renovationMeasureType = RenovationMeasureType.SOLAR_PLANT_PANELS,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_PANELS_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

})
