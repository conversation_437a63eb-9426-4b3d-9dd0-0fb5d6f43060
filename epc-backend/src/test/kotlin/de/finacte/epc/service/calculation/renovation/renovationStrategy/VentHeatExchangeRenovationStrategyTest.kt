package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.UUID

class VentHeatExchangeRenovationStrategyTest : StringSpec({
    val sut = VentHeatExchangeRenovationStrategy()

    "apply renovation to BuildingCalculationInput " {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE,
            renovationMeasureValue = RenovationMeasureValue.HEAT_EXCHANGER,
            renovationMeasureObject = "{\"ventHeatExchangeInstalled\": true}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.ventHeatExchange.ventHeatExchangeInstalled shouldBe  true
    }

})
