package de.finacte.epc.service.calculation.renovation.recommendationStrategy.facadeWallInsulation

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.renovation.*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class FacadeWallInsulationRecommendationStrategyTest : StringSpec({
    val facadeWallInsulationBasicBefore1945Strategy =
        FacadeWallInsulationBasicBefore1945RenovationRecommendationStrategy()
    val facadeWallInsulationAdvancedBefore1945Strategy =
        FacadeWallInsulationAdvancedBefore1945RenovationRecommendationStrategy()
    val facadeWallInsulationBasicBefore1980Strategy =
        FacadeWallInsulationBasicBefore1980RenovationRecommendationStrategy()
    val facadeWallInsulationAdvancedBefore1980Strategy =
        FacadeWallInsulationAdvancedBefore1980RenovationRecommendationStrategy()
    val facadeWallInsulationBasicAfter1979Strategy =
        FacadeWallInsulationBasicAfter1979RenovationRecommendationStrategy()
    val facadeWallInsulationAdvancedAfter1979Strategy =
        FacadeWallInsulationAdvancedAfter1979RenovationRecommendationStrategy()



    "Building with construction year 1940 should only apply one basic strategies" {
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.MINERAL_WOOL_10_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.BASIC,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure"
        )

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1940
            )
        )

        // Basic Before1945 strategies should be applicable
        facadeWallInsulationBasicBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe true

        // Advanced Before1945 strategies should not be applicable
        facadeWallInsulationAdvancedBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Before1980 strategies should not be applicable (construction year < 1945)
        facadeWallInsulationBasicBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // After1979 strategies should not be applicable (construction year < 1980)
        facadeWallInsulationBasicAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
    }

    "Building with construction year 1940 should only apply one advanced strategies" {
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.WOOD_FIBER_15_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure"
        )

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1940
            )
        )

        // Advanced Before1945 strategies should be applicable
        facadeWallInsulationAdvancedBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe true

        // Basic Before1945 strategies should not be applicable
        facadeWallInsulationBasicBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Before1980 strategies should not be applicable (construction year < 1945)
        facadeWallInsulationBasicBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // After1979 strategies should not be applicable (construction year < 1980)
        facadeWallInsulationBasicAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
    }

    "Building with construction year 1960 should only apply one basic strategies" {
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.MINERAL_WOOL_10_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.BASIC,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure"
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1960
            )
        )

        // Before1945 strategies should not be applicable (construction year >= 1945)
        facadeWallInsulationBasicBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Basic Before1980 strategies should be applicable
        facadeWallInsulationBasicBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe true

        // Advanced Before1980 strategies should not be applicable
        facadeWallInsulationAdvancedBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // After1979 strategies should not be applicable (construction year < 1980)
        facadeWallInsulationBasicAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
    }

    "Building with construction year 1960 should only apply one advanced strategies" {
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure"
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1960
            )
        )

        // Before1945 strategies should not be applicable (construction year >= 1945)
        facadeWallInsulationBasicBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Basic Before1980 strategies should not be applicable
        facadeWallInsulationBasicBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Advanced Before1980 strategies should be applicable
        facadeWallInsulationAdvancedBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe true

        // After1979 strategies should not be applicable (construction year < 1980)
        facadeWallInsulationBasicAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
    }

    "Building with construction year 1990 should only apply one basic strategy" {
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.VACUUM_INSULATED_PANEL_5_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.BASIC,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure"
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1990
            )
        )

        // Before1945 strategies should not be applicable (construction year >= 1945)
        facadeWallInsulationBasicBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Before1980 strategies should not be applicable (construction year >= 1980)
        facadeWallInsulationBasicBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Advanced After1979 strategies should be applicable
        facadeWallInsulationBasicAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe true

        // Basic After1979 strategies should not be applicable
        facadeWallInsulationAdvancedAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
    }

    "Building with construction year 1990 should only apply one advanced strategy" {
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.AEROGEL_4_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure"
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1990
            )
        )

        // Before1945 strategies should not be applicable (construction year >= 1945)
        facadeWallInsulationBasicBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1945Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Before1980 strategies should not be applicable (construction year >= 1980)
        facadeWallInsulationBasicBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false
        facadeWallInsulationAdvancedBefore1980Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Advanced After1979 strategies should not be applicable
        facadeWallInsulationBasicAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe false

        // Basic After1979 strategies should be applicable
        facadeWallInsulationAdvancedAfter1979Strategy.test(
            buildingCalculationInput,
            renovationTemplateEntity
        ).first shouldBe true
    }

})
