package de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators

import de.finacte.epc.entity.building.*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import java.util.UUID

class BuildingMissingAttributesInputStaticGeneratorImplTest : StringSpec({
    val sut = BuildingMissingAttributesInputStaticGeneratorImpl()

    "calculateGroundFloor should return correct values for pre-1990 building" {
        // Arrange
        val building = createBuildingEntity(1985)

        // Act
        val result = sut.calculateGroundFloor(building)

        // Assert
        result.groundFloorType shouldBe BuildingScreedType.CEMENT
        result.groundFloorThickness shouldBe 10
        result.groundFloorFinishType.shouldBeTypeOf<BuildingFloorFinishType>()
        result.groundFloorFinishThickness shouldBe result.groundFloorFinishType.getExpectedThickness()
    }

    "calculateGroundFloor should return correct values for 1990-2000 building" {
        // Arrange
        val building = createBuildingEntity(1995)

        // Act
        val result = sut.calculateGroundFloor(building)

        // Assert
        result.groundFloorType shouldBe BuildingScreedType.ANHYDRITE
        result.groundFloorThickness shouldBe 7
        result.groundFloorFinishType.shouldBeTypeOf<BuildingFloorFinishType>()
        result.groundFloorFinishThickness shouldBe result.groundFloorFinishType.getExpectedThickness()
    }

    "calculateGroundFloor should return correct values for post-2000 building" {
        // Arrange
        val building = createBuildingEntity(2010)

        // Act
        val result = sut.calculateGroundFloor(building)

        // Assert
        result.groundFloorType shouldBe BuildingScreedType.CEMENT_WITH_ADDITIVES
        result.groundFloorThickness shouldBe 7
        result.groundFloorFinishType.shouldBeTypeOf<BuildingFloorFinishType>()
        result.groundFloorFinishThickness shouldBe result.groundFloorFinishType.getExpectedThickness()
    }

    "calculateBasementFloor should return correct values for pre-1990 building" {
        // Arrange
        val building = createBuildingEntity(1985)

        // Act
        val result = sut.calculateBasementFloor(building)

        // Assert
        result.basementFloorType shouldBe BuildingScreedType.CEMENT
        result.basementFloorThickness shouldBe 10
        result.basementFloorFinishType shouldBe BuildingFloorFinishType.WOODEN_PLANKS
        result.basementFloorFinishThickness shouldBe 2
    }

    "calculateBasementFloor should return correct values for 1990-2000 building" {
        // Arrange
        val building = createBuildingEntity(1995)

        // Act
        val result = sut.calculateBasementFloor(building)

        // Assert
        result.basementFloorType shouldBe BuildingScreedType.ANHYDRITE
        result.basementFloorThickness shouldBe 7
        result.basementFloorFinishType shouldBe BuildingFloorFinishType.CERAMIC_TILES
        result.basementFloorFinishThickness shouldBe 1
    }

    "calculateBasementFloor should return correct values for post-2000 building" {
        // Arrange
        val building = createBuildingEntity(2010)

        // Act
        val result = sut.calculateBasementFloor(building)

        // Assert
        result.basementFloorType shouldBe BuildingScreedType.CEMENT_WITH_ADDITIVES
        result.basementFloorThickness shouldBe 7
        result.basementFloorFinishType shouldBe BuildingFloorFinishType.CERAMIC_TILES
        result.basementFloorFinishThickness shouldBe 1
    }
})

private fun createBuildingEntity(constructionYear: Int): BuildingEntity {
    return mockk<BuildingEntity>().apply {
        every { <EMAIL> } returns constructionYear
        every { <EMAIL> } returns UUID.randomUUID()
        every { <EMAIL> } returns BuildingShape.COMPACT
        every { <EMAIL> } returns BuildingPosition.STAND_ALONE
        every { <EMAIL> } returns BuildingFloorHeight.REGULAR
        every { <EMAIL> } returns 120.0
        every { <EMAIL> } returns 2
    }
}

private fun BuildingFloorFinishType.getExpectedThickness(): Int {
    return when (this) {
        BuildingFloorFinishType.WOODEN_PLANKS, BuildingFloorFinishType.PARQUET, BuildingFloorFinishType.WOODEN_PANELS -> 2
        BuildingFloorFinishType.CERAMIC_TILES, BuildingFloorFinishType.PVC, BuildingFloorFinishType.LAMINATE -> 1
    }
}
