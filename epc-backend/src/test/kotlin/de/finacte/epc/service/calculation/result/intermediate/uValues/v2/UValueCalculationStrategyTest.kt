package de.finacte.epc.service.calculation.result.intermediate.uValues.v2

import de.finacte.epc.entity.ConstructionMaterialPropertiesEntity
import de.finacte.epc.entity.ConstructionMaterialType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.repository.ConstructionMaterialPropertiesRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.util.*

class UValueCalculationStrategyTest : StringSpec({

    // Create a concrete implementation of the abstract class for testing
    class TestUValueCalculationStrategy(
        constructionMaterialPropertiesRepository: ConstructionMaterialPropertiesRepository
    ) : UValueCalculationStrategy(constructionMaterialPropertiesRepository) {
        override fun getThermalEnvelopeElementTypeForStrategy(): ThermalEnvelopeElementType =
            ThermalEnvelopeElementType.FACADE_WALLS

        override fun apply(
            buildingCalculationInput: BuildingCalculationInput,
            buildingShapeResult: BuildingShapeResult
        ) = mockk<de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult.UValuesThermalEnvelopeElement>()
    }

    "findConstructionMaterialThermalResistance should use exact thickness value" {
        // Given
        val repository = mockk<ConstructionMaterialPropertiesRepository>()
        val strategy = TestUValueCalculationStrategy(repository)
        val materialName = "MINERAL_WOOL"
        val materialType = ConstructionMaterialType.INSULATION
        val originalThickness = 2.0164580794429554
        val expectedThickness = originalThickness
        val expectedThermalResistance = 0.5

        val materialProperties = ConstructionMaterialPropertiesEntity(
            id = UUID.randomUUID(),
            name = materialName,
            type = materialType,
            thermalConductivity = 0.04,
            thermalResistance = expectedThermalResistance,
            uValue = null,
            thicknessInMetersFrom = null,
            thicknessInMetersTo = null
        )

        val thicknessSlot = slot<Double>()

        every {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                capture(thicknessSlot)
            )
        } returns materialProperties

        // When
        val result = strategy.findConstructionMaterialThermalResistance(
            materialName,
            materialType,
            originalThickness
        )

        // Then
        result shouldBe expectedThermalResistance
        thicknessSlot.captured shouldBe expectedThickness

        verify(exactly = 1) {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                expectedThickness
            )
        }
    }

    "findConstructionMaterialThermalResistance should use minimum value 0.5 when thickness is too small" {
        // Given
        val repository = mockk<ConstructionMaterialPropertiesRepository>()
        val strategy = TestUValueCalculationStrategy(repository)
        val materialName = "MINERAL_WOOL"
        val materialType = ConstructionMaterialType.INSULATION
        val originalThickness = 0.1
        val expectedBoundedThickness = 0.5 // Should be bounded to minimum
        val expectedThermalResistance = 0.5

        val materialProperties = ConstructionMaterialPropertiesEntity(
            id = UUID.randomUUID(),
            name = materialName,
            type = materialType,
            thermalConductivity = 0.04,
            thermalResistance = expectedThermalResistance,
            uValue = null,
            thicknessInMetersFrom = null,
            thicknessInMetersTo = null
        )

        val thicknessSlot = slot<Double>()

        every {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                capture(thicknessSlot)
            )
        } returns materialProperties

        // When
        val result = strategy.findConstructionMaterialThermalResistance(
            materialName,
            materialType,
            originalThickness
        )

        // Then
        result shouldBe expectedThermalResistance
        thicknessSlot.captured shouldBe expectedBoundedThickness

        verify(exactly = 1) {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                expectedBoundedThickness
            )
        }
    }

    "findConstructionMaterialThermalResistance should use maximum value 4.0 when thickness is too large" {
        // Given
        val repository = mockk<ConstructionMaterialPropertiesRepository>()
        val strategy = TestUValueCalculationStrategy(repository)
        val materialName = "MINERAL_WOOL"
        val materialType = ConstructionMaterialType.INSULATION
        val originalThickness = 5.7
        val expectedBoundedThickness = 4.0 // Should be bounded to maximum
        val expectedThermalResistance = 0.5

        val materialProperties = ConstructionMaterialPropertiesEntity(
            id = UUID.randomUUID(),
            name = materialName,
            type = materialType,
            thermalConductivity = 0.04,
            thermalResistance = expectedThermalResistance,
            uValue = null,
            thicknessInMetersFrom = null,
            thicknessInMetersTo = null
        )

        val thicknessSlot = slot<Double>()

        every {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                capture(thicknessSlot)
            )
        } returns materialProperties

        // When
        val result = strategy.findConstructionMaterialThermalResistance(
            materialName,
            materialType,
            originalThickness
        )

        // Then
        result shouldBe expectedThermalResistance
        thicknessSlot.captured shouldBe expectedBoundedThickness

        verify(exactly = 1) {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                expectedBoundedThickness
            )
        }
    }

    "findConstructionMaterialThermalResistance should throw exception when thermal resistance is not found" {
        // Given
        val repository = mockk<ConstructionMaterialPropertiesRepository>()
        val strategy = TestUValueCalculationStrategy(repository)
        val materialName = "UNKNOWN_MATERIAL"
        val materialType = ConstructionMaterialType.INSULATION
        val thickness = 2.0

        every {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                thickness
            )
        } returns null

        // When/Then
        shouldThrow<EPCCalculationException> {
            strategy.findConstructionMaterialThermalResistance(
                materialName,
                materialType,
                thickness
            )
        }

        verify(exactly = 1) {
            repository.findByNameAndTypeBetweenThickness(
                materialName,
                materialType,
                thickness
            )
        }
    }
})
