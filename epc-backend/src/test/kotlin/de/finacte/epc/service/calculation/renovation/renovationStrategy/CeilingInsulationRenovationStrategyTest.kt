package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.UUID

class CeilingInsulationRenovationStrategyTest : StringSpec({
    val sut = CeilingInsulationRenovationStrategy()

    "apply renovation to BuildingCalculationInput " {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.CEILING_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureObject = "{ \"ceilingWallInsulationType\": \"EXTRUDED_POLYSTYRENE\", \"ceilingWallInsulationThickness\": 10}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.ceilingWallInsulation?.ceilingWallInsulationType shouldBe  BuildingInsulationType.EXTRUDED_POLYSTYRENE
        result.ceilingWallInsulation?.ceilingWallInsulationThickness shouldBe 10
    }

    "apply ceiling insulation renovation to BuildingCalculationInput with fiberglass insulation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.CEILING_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.FIBERGLASS_INSULATION_30_CM_2025,
            renovationMeasureObject = "{\"ceilingWallInsulationType\": \"FIBERGLASS_INSULATION\", \"ceilingWallInsulationThickness\": 30}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.ceilingWallInsulation?.ceilingWallInsulationType shouldBe BuildingInsulationType.FIBERGLASS_INSULATION
        result.ceilingWallInsulation?.ceilingWallInsulationThickness shouldBe 30
    }

    "apply ceiling insulation renovation to BuildingCalculationInput with vacuum insulated panels" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.CEILING_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.VACUUM_INSULATED_PANEL_5_CM_2025,
            renovationMeasureObject = "{\"ceilingWallInsulationType\": \"VACUUM_INSULATED_PANEL\", \"ceilingWallInsulationThickness\": 5}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.ceilingWallInsulation?.ceilingWallInsulationType shouldBe BuildingInsulationType.VACUUM_INSULATED_PANEL
        result.ceilingWallInsulation?.ceilingWallInsulationThickness shouldBe 5
    }
})
