package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year
import java.util.UUID

class GroundFloorInsulationRenovationStrategyTest : StringSpec({
    val sut = GroundFloorInsulationRenovationStrategy()

    "apply renovation to BuildingCalculationInput " {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.BASEMENT,
            renovationMeasureType = RenovationMeasureType.GROUND_FLOOR_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureObject = "{\"groundFloorInsulationType\": \"EXTRUDED_POLYSTYRENE\" , \"groundFloorInsulationThickness\": 10 , \"groundFloorInsulationYear\": 2025}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.groundFloorInsulation?.groundFloorInsulationType shouldBe BuildingInsulationType.EXTRUDED_POLYSTYRENE
        result.groundFloorInsulation?.groundFloorInsulationThickness shouldBe 10
        result.groundFloorInsulation?.groundFloorInsulationYear shouldBe Year.now().value
    }


    "apply renovation to BuildingCalculationInput with aerogel" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.BASEMENT,
            renovationMeasureType = RenovationMeasureType.GROUND_FLOOR_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.AEROGEL_1_CM_2025,
            renovationMeasureObject = "{\"groundFloorInsulationType\": \"AEROGEL\" , \"groundFloorInsulationThickness\": 1 , \"groundFloorInsulationYear\": 2025}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.groundFloorInsulation?.groundFloorInsulationType shouldBe BuildingInsulationType.AEROGEL
        result.groundFloorInsulation?.groundFloorInsulationThickness shouldBe 1
        result.groundFloorInsulation?.groundFloorInsulationYear shouldBe Year.now().value
    }
})
