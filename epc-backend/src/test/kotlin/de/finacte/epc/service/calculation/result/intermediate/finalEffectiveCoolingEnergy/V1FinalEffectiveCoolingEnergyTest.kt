package de.finacte.epc.service.calculation.result.intermediate.finalEffectiveCoolingEnergy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedClimateDataResult
import de.finacte.epc.entity.SystemEfficiencyEntity
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.BuildingWindowsShutters
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.util.*

class V1FinalEffectiveCoolingEnergyTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1FinalEffectiveCoolingEnergy(systemEfficiencyRepositoryMock)


    "calculate final effective cooling energy when AC is installed" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            coolingDegreeDays = 50
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                acInstalled = true
            ),
            windowsShutters = null
        )

        val heatTransferCoefficient = 90.0
        val ventilationLossCoefficient = 60.0
        every {
            systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
                SystemEfficiencyType.AC,
                2010
            )
        } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.AC,
            efficiency = 3.5,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInput,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        result shouldBe 51.42857142857143
    }

    "calculate final effective cooling energy when AC and manual shutters are installed" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            coolingDegreeDays = 50
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                acInstalled = true
            ),
            windowsShutters = BuildingCalculationInput.WindowsShutters(
                windowsShutters = BuildingWindowsShutters.MANUAL
            )
        )

        val heatTransferCoefficient = 90.0
        val ventilationLossCoefficient = 60.0
        every {
            systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
                SystemEfficiencyType.AC,
                2010
            )
        } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.AC,
            efficiency = 3.5,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInput,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        result shouldBe 46.28571428571429
    }

    "calculate final effective cooling energy when AC is not installed" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            heatingDegreeDays = 3000
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                acInstalled = false
            )
            //windows shutters shouldn't affect cooling energy when AC is not installed
        )
        val heatTransferCoefficient = 700.0
        val ventilationLossCoefficient = 60.0

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInput,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        result shouldBe 0.0
    }
})
