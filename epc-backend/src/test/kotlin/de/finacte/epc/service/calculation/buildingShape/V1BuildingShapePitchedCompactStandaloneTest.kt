package de.finacte.epc.service.calculation.buildingShape

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.*
import de.finacte.epc.entity.building.BuildingShape
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.roofFloorStrategies.RoofFloorCalculationImpl
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.doubles.plusOrMinus
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.fail

class V1BuildingShapePitchedCompactStandaloneTest : StringSpec({
    val sut = BuildingShapeImpl(RoofFloorCalculationImpl())

    "calculate COMPACT building shape with FULLY_LIVEABLE_REDUCED roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (273.771 plusOrMinus 1.0)
            roofFloorVolume shouldBe 140.84522197072494
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 106.34072305571611
            buildingThermalEnvelopeVolume shouldBe (250.00 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (81.94 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (106.82 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (18.84 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (27.79 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (31.90 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (53.19 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPACT building shape with PARTIALLY_LIVEABLE roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.PARTIALLY_LIVEABLE,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (334.223 plusOrMinus 1.0)
            roofFloorVolume shouldBe 164.14344946482288
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 112.02122390320508
            buildingThermalEnvelopeVolume shouldBe (252.10 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (103.00 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (102.53 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (18.09 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (44.01 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (10.26 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (68.06 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPACT building shape with COLD_STORAGE roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.COLD_STORAGE,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (421.280 plusOrMinus 1.0)
            roofFloorVolume shouldBe 171.28036613996852
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 100.0
            buildingThermalEnvelopeVolume shouldBe (250.00  plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (147.68 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (84.95 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (15.39 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (100.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (100.00 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPACT building shape with FULLY_LIVEABLE_ELEVATED roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 120.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (349.603 plusOrMinus 1.0)
            roofFloorVolume shouldBe 199.60392067108882
            buildingArea shouldBe 120.0
            buildingHeatedArea shouldBe 120.00000000000001
            buildingThermalEnvelopeVolume shouldBe (292.19  plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (91.60 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (124.93 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (22.05 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (11.81 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (50.96 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (60.00 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPACT building shape with FULLY_LIVEABLE_REDUCED roof floor - COLD BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,

                //changed:
                basementExist = true,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (390.74 plusOrMinus 1.0)
            roofFloorVolume shouldBe 140.84522197072494
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 106.34072305571611
            buildingThermalEnvelopeVolume shouldBe (250.00 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (81.94 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (106.82 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (18.84 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (27.79 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (31.90 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (53.19 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPACT building shape with FULLY_LIVEABLE_REDUCED roof floor - HEATED BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,

                //changed:
                basementExist = true
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = true
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (390.74 plusOrMinus 1.0)
            roofFloorVolume shouldBe 140.84522197072494
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 159.51108458357416
            buildingThermalEnvelopeVolume shouldBe (366.86 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (81.94 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (106.82 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (18.84 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (27.79 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (31.90 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS -> it.area shouldBe (65.49 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_FLOOR -> it.area shouldBe (53.19 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

})