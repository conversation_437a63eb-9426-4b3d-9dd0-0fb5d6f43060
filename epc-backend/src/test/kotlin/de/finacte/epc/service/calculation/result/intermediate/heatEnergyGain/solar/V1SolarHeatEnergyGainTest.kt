package de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.solar

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedBuildingShapeResult
import de.finacte.epc.createFullyInitializedClimateDataResult
import de.finacte.epc.entity.building.*
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.HeatEnergyGainResult
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class V1SolarHeatEnergyGainTest : StringSpec({
    val sut = V1SolarHeatEnergyGain()

    "calculate solar heat energy gain for double glazed e-coated windows with manual shutters" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
            ),
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = BuildingWindowsGlazing.DOUBLE,
                windowsRenewed = false,
                windowsInstallationYear = 1990,
            ),
            windowsShutters = BuildingCalculationInput.WindowsShutters(
                windowsShutters = BuildingWindowsShutters.MANUAL
            ),
            windowsCoating = BuildingCalculationInput.WindowsCoating(
                windowsCoatingType = BuildingWindowCoatingType.LOW_E_COATING,
            )

        )
        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            thermalEnvelopeElements = setOf(
                BuildingShapeResult.ThermalEnvelopeElement(
                    type = ThermalEnvelopeElementType.WINDOWS,
                    area = 15.0
                ),
            )
        )
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            amountOfDaysContributingToHeatingDegreeDays = 250,
            amountOfDaysContributingToCoolingDegreeDays = 20,
        )

        val result = sut.calculate(buildingCalculationInput, buildingShapeResult, climateDataResult)

        result shouldBe HeatEnergyGainResult(
            summerHeatEnergyGainKWH = 118.125,
            winterHeatEnergyGainKWH = 295.3125
        )
    }

    "calculate solar heat energy gain for single glazed none coated windows with electric shutters" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
            ),
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = BuildingWindowsGlazing.SINGLE,
                windowsRenewed = false,
                windowsInstallationYear = 1990,
            ),
            windowsShutters = BuildingCalculationInput.WindowsShutters(
                windowsShutters = BuildingWindowsShutters.ELECTRIC
            ),
            windowsCoating = BuildingCalculationInput.WindowsCoating(
                windowsCoatingType = BuildingWindowCoatingType.NONE
            )
        )
        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            thermalEnvelopeElements = setOf(
                BuildingShapeResult.ThermalEnvelopeElement(
                    type = ThermalEnvelopeElementType.WINDOWS,
                    area = 15.0
                ),
            )
        )
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            amountOfDaysContributingToHeatingDegreeDays = 250,
            amountOfDaysContributingToCoolingDegreeDays = 20,
        )

        val result = sut.calculate(buildingCalculationInput, buildingShapeResult, climateDataResult)

        result shouldBe HeatEnergyGainResult(
            summerHeatEnergyGainKWH = 137.02499999999998,
            winterHeatEnergyGainKWH = 342.56249999999994
        )
    }


})
