package de.finacte.epc.service.calculation.renovation.recommendationStrategy.roofCoverage

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.*

class RoofCoveragePitchedRoofSingleTenantAdvancedAfter1949RenovationRecommendationStrategyTest : StringSpec({
    val sut =
        RoofCoveragePitchedRoofSingleTenantAdvancedAfter1949RenovationRecommendationStrategy()

    "test method should return applicability=true for building after 1949 with 1-2 tenants and non-flat roof" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1950,
                tenants = 2,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.CLAY_TILES,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe true // isApplicable should be true
    }

    "test method should return applicability=false for building before 1950" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1949,
                tenants = 2,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.CLAY_TILES,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }

    "test method should return applicability=false for building with more than 2 tenants" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1950,
                tenants = 3,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.CLAY_TILES,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }

    "test method should return applicability=false for building with flat roof" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1950,
                tenants = 2,
                roofFloor = BuildingRoofFloor.FLAT_ROOF
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.CLAY_TILES,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }
})
