package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.UUID

class SolarPanelsImmersionHeaterRenovationStrategyTest : StringSpec({
    val sut = SolarPanelsImmersionHeaterRenovationStrategy()

    "apply buffer storage renovation with installed=true to BuildingCalculationInput" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.IMMERSION_HEATER,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT,
            renovationMeasureObject = """{"installed": true}""",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.solarPanelsImmersionHeater.installed shouldBe true
        result.solarPanels.solarPanelsExist shouldBe false // solar panels don't come with buffer renovation
    }

    "apply buffer storage renovation with installed=false to BuildingCalculationInput" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.IMMERSION_HEATER,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT,
            renovationMeasureObject = """{"installed": false}""",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.solarPanelsImmersionHeater.installed shouldBe false
        result.solarPanels.solarPanelsExist shouldBe false // solar panels don't come with buffer renovation
    }

    "return original BuildingCalculationInput when JSON parsing fails" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.IMMERSION_HEATER,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT,
            renovationMeasureObject = """{"invalid": json""", // Invalid JSON
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result shouldBe buildingCalculationInput
    }
})
