package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.AirToWaterHeatingRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year

class AirToWaterHeatingRenovationRecommendationStrategyTest : StringSpec({
    val sut = AirToWaterHeatingRenovationRecommendationStrategy()


    "test recommendation strategy - heating system older than 5 and different system == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType.ELECTRICITY, //not relevant
                hotWaterEnergySource = SystemEfficiencyType.ELECTRICITY, //not relevant
                heatingInstallationYear = Year.now().value - 6,
                heatingInstallationModernized = false
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = """
                {
                "heatingEnergySource":  "HEAT_PUMP",
                "hotWaterEnergySource": "HEAT_PUMP",
                "heatingInstallationYear": "2025"
                }
            """.trimIndent(),//not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - heating system older than 5 and same system is tested == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType.HEAT_PUMP,
                hotWaterEnergySource = SystemEfficiencyType.HEAT_PUMP, //not relevant
                heatingInstallationYear = Year.now().value - 6,
                heatingInstallationModernized = false
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = """
                {
                "heatingEnergySource":  "HEAT_PUMP",
                "hotWaterEnergySource": "HEAT_PUMP",
                "heatingInstallationYear": "2024"
                }
            """.trimIndent(),//not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - heating system younger than 5 and same system is tested == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType.HEAT_PUMP,
                hotWaterEnergySource = SystemEfficiencyType.HEAT_PUMP, //not relevant
                heatingInstallationYear = Year.now().value - 5,
                heatingInstallationModernized = false
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = """
                {
                "heatingEnergySource":  "HEAT_PUMP",
                "hotWaterEnergySource": "HEAT_PUMP",
                "heatingInstallationYear": "2025"
                }
            """.trimIndent(),//not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }
})