package de.finacte.epc.service.gpt

import de.finacte.epc.createFullyInitializedUValuesCalculationResult
import de.finacte.epc.dto.building.generated.BuildingMissingAttributesGenerated
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult
import io.kotest.assertions.json.shouldEqualJson
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf

class ClassToJsonSchemaConverterKtTest : StringSpec({

    "serialise uValues (second letter lowercased) data class to object" {
        val input =
            "{\"uvaluesThermalEnvelopeElements\":[{\"type\":\"DOOR\",\"uvalue\":2.4},{\"type\":\"FACADE_WALLS\"," +
                    "\"uvalue\":0.24},{\"type\":\"WINDOWS\",\"uvalue\":1.5},{\"type\":\"CEILING\",\"uvalue\":0.25}, " +
                    "{\"type\":\"BASEMENT_FLOOR\",\"uvalue\":0.24},{\"type\":\"BASEMENT_EXTERNAL_WALLS\",\"uvalue\":0.23}]}"

        val result = convertResponseToObject(UValueCalculationResult::class.java, input)

        result.shouldBeTypeOf<UValueCalculationResult>()
        result shouldBe createFullyInitializedUValuesCalculationResult()
    }

    "serialise uValues (second letter uppercase) data class to object" {
        val input =
            "{\"uValuesThermalEnvelopeElements\":[{\"type\":\"DOOR\",\"uValue\":2.4},{\"type\":\"FACADE_WALLS\"," +
                    "\"uValue\":0.24},{\"type\":\"WINDOWS\",\"uValue\":1.5},{\"type\":\"CEILING\",\"uValue\":0.25}, " +
                    "{\"type\":\"BASEMENT_FLOOR\",\"uvalue\":0.24},{\"type\":\"BASEMENT_EXTERNAL_WALLS\",\"uvalue\":0.23}]}"

        val result = convertResponseToObject(UValueCalculationResult::class.java, input)

        result.shouldBeTypeOf<UValueCalculationResult>()
        result shouldBe createFullyInitializedUValuesCalculationResult()
    }

    "convert BuildingMissingAttributesGenerated to JSON schema V4" {
        val result = convertClassToJsonV4Schema(BuildingMissingAttributesGenerated::class.java)

        result shouldEqualJson """
          {
             "${"$"}schema": "http://json-schema.org/draft-04/schema#",
              "title": "Building Missing Attributes Generated",
              "type": "object",
              "additionalProperties": false,
              "properties": {
                "roofInsulationThickness": {
                  "type": "integer",
                  "description": "Roof insulation thickness in centimeters"
                },
                "roofInsulationType": {
                  "type": "string",
                  "enum": [
                    "FIBERGLASS_INSULATION",
                    "MINERAL_WOOL",
                    "CELLULOSE_INSULATION"
                  ],
                  "description": "Roof insulation type"
                },
                "roofMaterial": {
                  "type": "string",
                  "enum": [
                    "CLAY_TILES",
                    "CONCRETE_TILES",
                    "NATURAL_SLATE",
                    "METAL_TILES",
                    "ASPHALT_SHINGLES",
                    "FIBER_CEMENT",
                    "WOOD_SHINGLES",
                    "BITUMEN_MEMBRANE",
                    "HIGH_QUALITY_PLASTIC_FOIL"
                  ],
                  "description": "The material from which the roof is made"
                },
                "facadeWallType": {
                  "type": "string",
                  "enum": [
                    "BRICK",
                    "CONCRETE",
                    "REINFORCED_CONCRETE",
                    "WOOD",
                    "HOLLOW_BLOCK",
                    "AUTOCLAVED_AERATED_CONCRETE"
                  ],
                  "description": "Facade wall type"
                },
                "facadeWallThickness": {
                  "type": "integer",
                  "description": "Facade wall thickness in centimeters"
                },
                "facadeWallInsulationType": {
                  "type": "string",
                  "enum": [
                    "EXPANDED_POLYSTYRENE",
                    "MINERAL_WOOL",
                    "FIBERGLASS_INSULATION",
                    "CORK_INSULATION_PLATE"
                  ],
                  "description": "Facade wall insulation type"
                },
                "facadeWallInsulationThickness": {
                  "type": "integer",
                  "description": "Facade wall insulation thickness in centimeters"
                },
                "windowsFrameMaterial": {
                  "type": "string",
                  "enum": [
                    "FIBERGLASS",
                    "COMPOSITE",
                    "PVC",
                    "FIBERGLASS_CLAD_WOOD",
                    "WOOD",
                    "VINYL_CLAD_WOOD",
                    "ALUMINUM_CLAD_WOOD",
                    "ALUMINUM",
                    "STEEL"
                  ],
                  "description": "Windows frame material"
                },
                "windowsCoatingType": {
                  "type": "string",
                  "enum": [
                    "LOW_E_COATING",
                    "TINTED_COATING",
                    "REFLECTIVE_COATING",
                    "UV_COATING",
                    "ANTI_GLARE_COATING",
                    "HYDROPHILIC_COATING",
                    "THERMAL_INSULATING_COATING",
                    "SOLAR_CONTROL_COATING",
                    "SCRATCH_RESISTANT_COATING",
                    "ANTI_FOG_COATING",
                    "SELF_CLEANING_COATING",
                    "SECURITY_FILM",
                    "PRIVACY_FILM",
                    "NONE"
                  ],
                  "description": "Windows coating type"
                },
                "basementFloorInsulationType": {
                  "type": "string",
                  "enum": [
                    "EXPANDED_POLYSTYRENE",
                    "EXPANDED_PERLITE"
                  ],
                  "description": "Basement floor insulation type"
                },
                "basementFloorInsulationThickness": {
                  "type": "integer",
                  "description": "Basement floor insulation thickness in centimeters"
                },
                "basementExternalWallsInsulationType": {
                  "type": "string",
                  "enum": [
                    "EXPANDED_POLYSTYRENE",
                    "EXPANDED_PERLITE"
                  ],
                  "description": "Basement external wall insulation type"
                },
                "basementExternalWallsInsulationThickness": {
                  "type": "integer",
                  "description": "Basement external walls insulation thickness in centimeters"
                },
                "basementExternalWallsType": {
                  "type": "string",
                  "enum": [
                    "BRICK",
                    "CONCRETE",
                    "REINFORCED_CONCRETE",
                    "WOOD",
                    "HOLLOW_BLOCK",
                    "AUTOCLAVED_AERATED_CONCRETE"
                  ],
                  "description": "Basement external wall type"
                },
                "ceilingWallInsulationType": {
                  "type": "string",
                  "enum": [
                    "MINERAL_WOOL",
                    "CELLULOSE_INSULATION"
                  ],
                  "description": "Ceiling insulation type"
                },
                "ceilingWallInsulationThickness": {
                  "type": "integer",
                  "description": "Ceiling wall insulation thickness in centimeters"
                },
                "ceilingWallType": {
                  "type": "string",
                  "enum": [
                    "BRICK",
                    "CONCRETE",
                    "REINFORCED_CONCRETE",
                    "WOOD",
                    "HOLLOW_BLOCK",
                    "AUTOCLAVED_AERATED_CONCRETE"
                  ],
                  "description": "Ceiling wall type"
                },
                "ceilingWallThickness": {
                  "type": "integer",
                  "description": "Ceiling wall thickness in centimeters"
                },
                "groundFloorInsulationType": {
                  "type": "string",
                  "enum": [
                    "EXPANDED_POLYSTYRENE",
                    "CORK_INSULATION_PLATE",
                    "EXPANDED_PERLITE"
                  ],
                  "description": "Ground floor insulation type"
                },
                "groundFloorInsulationThickness": {
                  "type": "integer",
                  "description": "Ground floor insulation thickness in centimeters"
                },
                "entranceDoorMaterial": {
                  "type": "string",
                  "enum": [
                    "COMPOSITE",
                    "FIBERGLASS",
                    "WOOD",
                    "INSULATED_METAL",
                    "PVC",
                    "GLASS_MODERN",
                    "GLASS_OLD"
                  ],
                  "description": "Entrance door material"
                }
              },
              "required": [
                "roofInsulationThickness",
                "roofInsulationType",
                "roofMaterial",
                "facadeWallType",
                "facadeWallThickness",
                "facadeWallInsulationType",
                "facadeWallInsulationThickness",
                "windowsFrameMaterial",
                "windowsCoatingType",
                "basementFloorInsulationType",
                "basementFloorInsulationThickness",
                "basementExternalWallsInsulationType",
                "basementExternalWallsInsulationThickness",
                "basementExternalWallsType",
                "ceilingWallInsulationType",
                "ceilingWallInsulationThickness",
                "ceilingWallType",
                "ceilingWallThickness",
                "groundFloorInsulationType",
                "groundFloorInsulationThickness",
                "entranceDoorMaterial"
              ]
            }
        """.trimIndent()
    }
})


