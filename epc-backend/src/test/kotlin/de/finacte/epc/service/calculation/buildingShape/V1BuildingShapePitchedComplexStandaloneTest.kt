package de.finacte.epc.service.calculation.buildingShape

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.*
import de.finacte.epc.entity.building.BuildingShape
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.roofFloorStrategies.RoofFloorCalculationImpl
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.doubles.plusOrMinus
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.fail

class V1BuildingShapePitchedComplexStandaloneTest : StringSpec({
    val sut = BuildingShapeImpl(RoofFloorCalculationImpl())

    "calculate COMPLEX building shape with FULLY_LIVEABLE_REDUCED roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPLEX,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (257.080 plusOrMinus 1.0)
            roofFloorVolume shouldBe 116.47099157522204
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 112.48754039263964
            buildingThermalEnvelopeVolume shouldBe (254.856 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (98.12 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (152.31 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (26.88 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (54.75 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (14.35 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (56.29 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPLEX building shape with FULLY_LIVEABLE_ELEVATED roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 120.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPLEX,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (311.571 plusOrMinus 1.0)
            roofFloorVolume shouldBe 161.57179174591937
            buildingArea shouldBe 120.0
            buildingHeatedArea shouldBe 120.0
            buildingThermalEnvelopeVolume shouldBe (290.53 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (103.76 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (179.31 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (31.64 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (22.61 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (42.68 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (60.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPLEX building shape with COLD_STORAGE roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.COLD_STORAGE,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPLEX,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (339.448 plusOrMinus 1.0)
            roofFloorVolume shouldBe 89.4482539466382
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 100.00000000000001
            buildingThermalEnvelopeVolume shouldBe (250.0 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (163.37 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (115.99 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (20.79 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (100.00 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (100.00 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPLEX building shape with PARTIALLY_LIVEABLE roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 300.0,
                roofFloor = BuildingRoofFloor.PARTIALLY_LIVEABLE,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPLEX,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (1011.432 plusOrMinus 1.0)
            roofFloorVolume shouldBe 484.49320659345517
            buildingArea shouldBe 300.0
            buildingHeatedArea shouldBe 340.5171186931715
            buildingThermalEnvelopeVolume shouldBe (758.94 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (322.96 plusOrMinus 0.5)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (252.09 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (44.49 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (148.35 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (16.06 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (210.88 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPLEX building shape with FULLY_LIVEABLE_REDUCED roof floor - COLD BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPLEX,
                position = BuildingPosition.STAND_ALONE,

                //changed:
                basementExist = true
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (380.81 plusOrMinus 1.0)
            roofFloorVolume shouldBe 116.47099157522204
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 112.48754039263964
            buildingThermalEnvelopeVolume shouldBe (254.856 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (98.12 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (152.31 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (26.88 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (54.75 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (14.35 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (56.29 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate COMPLEX building shape with FULLY_LIVEABLE_REDUCED roof floor - HEATED BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPLEX,
                position = BuildingPosition.STAND_ALONE,

                //changed:
                basementExist = true
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = true
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (380.81 plusOrMinus 1.0)
            roofFloorVolume shouldBe 116.47099157522204
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 168.73131058895947
            buildingThermalEnvelopeVolume shouldBe (378.37 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (98.12 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (152.31 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (26.88 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (54.75 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (14.35 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS -> it.area shouldBe (91.45 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_FLOOR -> it.area shouldBe (56.29 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }
})