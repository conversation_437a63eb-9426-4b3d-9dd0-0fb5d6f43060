package de.finacte.epc.service.calculation.renovation.renovationStrategy
import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingWindowsShutters
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.*

class WindowsShuttersRenovationStrategyTest : StringSpec({
    val sut = WindowsShuttersRenovationStrategy()

    "apply windows shutters renovation to BuildingCalculationInput with manual shutters" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_SHUTTERS,
            renovationMeasureValue = RenovationMeasureValue.MANUAL_SHUTTERS,
            renovationMeasureObject = "{\"windowsShutters\": \"MANUAL\"}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.windowsShutters?.windowsShutters shouldBe BuildingWindowsShutters.MANUAL
    }

    "apply windows shutters renovation to BuildingCalculationInput with electric shutters" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_SHUTTERS,
            renovationMeasureValue = RenovationMeasureValue.ELECTRIC_SHUTTERS,
            renovationMeasureObject = "{\"windowsShutters\": \"ELECTRIC\"}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.windowsShutters?.windowsShutters shouldBe BuildingWindowsShutters.ELECTRIC
    }
})
