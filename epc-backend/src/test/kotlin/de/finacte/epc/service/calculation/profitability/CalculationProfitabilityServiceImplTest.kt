package de.finacte.epc.service.calculation.profitability

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedCalculationResultDto
import de.finacte.epc.createFullyInitializedCalculationResultEntity
import de.finacte.epc.dto.calculation.CalculationProfitabilityDto
import de.finacte.epc.dto.calculation.RenovationCostDto
import de.finacte.epc.dto.calculation.RenovationDto
import de.finacte.epc.entity.CalculationProfitabilityEntity
import de.finacte.epc.entity.CalculationResultCategory
import de.finacte.epc.entity.EnergySourceCostEntity
import de.finacte.epc.entity.Language
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.mapper.CalculationProfitabilityMapperImpl
import de.finacte.epc.repository.CalculationProfitabilityRepository
import de.finacte.epc.repository.EnergySourceCostRepository
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.util.EnumMap
import java.util.UUID

class CalculationProfitabilityServiceImplTest : StringSpec({
    val calculationProfitabilityRepository = mockk<CalculationProfitabilityRepository>()
    val calculationProfitabilityMapper = CalculationProfitabilityMapperImpl()
    val energySourceCostRepository = mockk<EnergySourceCostRepository>()
    val sut = CalculationProfitabilityServiceImpl(
        calculationProfitabilityRepository,
        calculationProfitabilityMapper,
        energySourceCostRepository
    )

    "calculate profitability when no ALTERNATIVE calculation result found " {
        val buildingCalculationInputBeforeRenovation = createFullyInitializedBuildingCalculationInput()
        val buildingCalculationInputAfterRenovation = buildingCalculationInputBeforeRenovation.copy()
        val calculationResults = listOf(createFullyInitializedCalculationResultEntity())
        val renovations = listOf(createRenovationDto())

        val result = sut.calculateProfitability(
            buildingCalculationInputBeforeRenovation,
            buildingCalculationInputAfterRenovation,
            calculationResults,
            renovations
        )

        result shouldBe createEmptyCalculationProfitabilityDto()
    }

    "calculate new profitability" {
        val buildingId = UUID.randomUUID()
        val buildingCalculationInputBeforeRenovation =
            createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingCalculationInputAfterRenovation = buildingCalculationInputBeforeRenovation.copy()
        val currentCalcResult = createFullyInitializedCalculationResultEntity().copy(
            finalHotWaterDemand = 100.0,
            finalElectricityDemand = 200.0,
            finalCoolingEnergyDemand = 300.0,
            finalHeatingEnergyDemand = 400.0
        )
        val alternativeCalcResult = createFullyInitializedCalculationResultEntity().copy(
            category = CalculationResultCategory.ALTERNATIVE,
            finalHotWaterDemand = 0.0,
            finalElectricityDemand = 0.0,
            finalCoolingEnergyDemand = 0.0,
            finalHeatingEnergyDemand = 0.0
        )
        val calculationResults = listOf(currentCalcResult, alternativeCalcResult)
        val renovations = listOf(createRenovationDto())
        every { energySourceCostRepository.findByEnergySource(SystemEfficiencyType.ELECTRICITY) } returns EnergySourceCostEntity(
            id = UUID.randomUUID(),
            energySource = SystemEfficiencyType.ELECTRICITY,
            cost = 1.0
        )
        every { energySourceCostRepository.findByEnergySource(SystemEfficiencyType.GAS) } returns EnergySourceCostEntity(
            id = UUID.randomUUID(),
            energySource = SystemEfficiencyType.GAS,
            cost = 2.0
        )
        every { calculationProfitabilityRepository.findByBuildingId(buildingId = buildingId) } returns null
        val savedEntity = slot<CalculationProfitabilityEntity>()
        every { calculationProfitabilityRepository.save(capture(savedEntity)) } returns createEmptyCalculationProfitabilityEntity(
            buildingId
        )

        sut.calculateProfitability(
            buildingCalculationInputBeforeRenovation,
            buildingCalculationInputAfterRenovation,
            calculationResults,
            renovations
        )

        savedEntity.captured shouldBe CalculationProfitabilityEntity(
            id = null,
            cost = 20.0,
            energyCostSaved = 15000.0,
            propertyValueIncrease = 12.0,
            totalProfit = 14992.0,
            buildingId = buildingId,
        )
    }


    "calculate updated profitability" {
        val buildingId = UUID.randomUUID()
        val buildingCalculationInputBeforeRenovation =
            createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingCalculationInputAfterRenovation = buildingCalculationInputBeforeRenovation.copy()
        val currentCalcResult = createFullyInitializedCalculationResultEntity().copy(
            finalHotWaterDemand = 100.0,
            finalElectricityDemand = 200.0,
            finalCoolingEnergyDemand = 300.0,
            finalHeatingEnergyDemand = 400.0
        )
        val alternativeCalcResult = createFullyInitializedCalculationResultEntity().copy(
            category = CalculationResultCategory.ALTERNATIVE,
            finalHotWaterDemand = 0.0,
            finalElectricityDemand = 0.0,
            finalCoolingEnergyDemand = 0.0,
            finalHeatingEnergyDemand = 0.0
        )
        val calculationResults = listOf(currentCalcResult, alternativeCalcResult)
        val renovations = listOf(createRenovationDto())
        every { energySourceCostRepository.findByEnergySource(SystemEfficiencyType.ELECTRICITY) } returns EnergySourceCostEntity(
            id = UUID.randomUUID(),
            energySource = SystemEfficiencyType.ELECTRICITY,
            cost = 1.0
        )
        every { energySourceCostRepository.findByEnergySource(SystemEfficiencyType.GAS) } returns EnergySourceCostEntity(
            id = UUID.randomUUID(),
            energySource = SystemEfficiencyType.GAS,
            cost = 2.0
        )
        val currentProfitability = createEmptyCalculationProfitabilityEntity(buildingId)
        every { calculationProfitabilityRepository.findByBuildingId(buildingId = buildingId) } returns currentProfitability
        val savedEntity = slot<CalculationProfitabilityEntity>()
        every { calculationProfitabilityRepository.save(capture(savedEntity)) } returns createEmptyCalculationProfitabilityEntity(
            buildingId
        )

        sut.calculateProfitability(
            buildingCalculationInputBeforeRenovation,
            buildingCalculationInputAfterRenovation,
            calculationResults,
            renovations
        )

        savedEntity.captured shouldBe CalculationProfitabilityEntity(
            id = currentProfitability.id,
            cost = 20.0,
            energyCostSaved = 15000.0,
            propertyValueIncrease = 12.0,
            totalProfit = 14992.0,
            buildingId = buildingId,
        )
    }

})

private fun createEmptyCalculationProfitabilityDto() = CalculationProfitabilityDto(
    id = null,
    totalProfit = 0.0,
    cost = 0.0,
    propertyValueIncrease = 0.0,
    energyCostSaved = 0.0,
)

private fun createEmptyCalculationProfitabilityEntity(buildingId: UUID) = CalculationProfitabilityEntity(
    id = null,
    totalProfit = 0.0,
    cost = 0.0,
    propertyValueIncrease = 0.0,
    energyCostSaved = 0.0,
    buildingId = buildingId
)

private fun createRenovationDto(): RenovationDto {
    val enumMap = EnumMap<Language, String>(Language::class.java)
    return RenovationDto(
        id = UUID.randomUUID(),
        measure = enumMap,
        measureDsc = enumMap,
        recommended = true,
        selected = true,
        renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
        renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
        renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        renovatedElementListOrder = 1,
        grantPrograms = emptyList(),
        cost = RenovationCostDto(
            range = 10.0 to 20.0,
            mostProbableValue = 15.0,
        )
    )
}