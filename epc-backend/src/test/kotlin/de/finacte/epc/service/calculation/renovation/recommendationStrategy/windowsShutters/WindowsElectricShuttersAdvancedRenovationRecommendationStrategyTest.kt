package de.finacte.epc.service.calculation.renovation.recommendationStrategy.windowsShutters

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingWindowsShutters
import de.finacte.epc.entity.renovation.*
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.*

class WindowsElectricShuttersAdvancedRenovationRecommendationStrategyTest : StringSpec({
    val sut =
        WindowsElectricShuttersAdvancedRenovationRecommendationStrategy()

    "test method should return recommendation=true when windowsShutters is null" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsShutters = null
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_SHUTTERS,
            renovationMeasureValue = RenovationMeasureValue.ELECTRIC_SHUTTERS,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{\"windowsShutters\": \"ELECTRIC\"}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Electric shutters",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result shouldBe Pair(true, true) // (isApplicable, isRecommended)
    }

    "test method should return recommendation=false when windowsShutters is ELECTRIC" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsShutters = BuildingCalculationInput.WindowsShutters(
                windowsShutters = BuildingWindowsShutters.ELECTRIC
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_SHUTTERS,
            renovationMeasureValue = RenovationMeasureValue.ELECTRIC_SHUTTERS,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{\"windowsShutters\": \"ELECTRIC\"}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Electric shutters",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result shouldBe Pair(true, false) // (isApplicable, isRecommended)
    }

    "test method should return recommendation=true when windowsShutters is MANUAL" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsShutters = BuildingCalculationInput.WindowsShutters(
                windowsShutters = BuildingWindowsShutters.MANUAL
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_SHUTTERS,
            renovationMeasureValue = RenovationMeasureValue.ELECTRIC_SHUTTERS,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{\"windowsShutters\": \"ELECTRIC\"}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Electric shutters",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result shouldBe Pair(true, true) // (isApplicable, isRecommended)
    }
})
