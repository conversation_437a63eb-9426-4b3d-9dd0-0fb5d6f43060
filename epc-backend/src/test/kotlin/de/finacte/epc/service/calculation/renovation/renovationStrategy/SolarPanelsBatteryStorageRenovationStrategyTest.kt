package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.SolarPanelsBatteryType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.UUID

class SolarPanelsBatteryStorageRenovationStrategyTest : StringSpec({
    val sut = SolarPanelsBatteryStorageRenovationStrategy()

    "apply battery storage renovation with LITHIUM_ION type to BuildingCalculationInput" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.BATTERY_STORAGE,
            renovationMeasureValue = RenovationMeasureValue.LITHIUM_ION_BATTERY,
            renovationMeasureObject = """{"batteryType": "LITHIUM_ION"}""",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        // When
        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        // Then
        result.solarPanelsBattery?.batteryType shouldBe SolarPanelsBatteryType.LITHIUM_ION
        result.solarPanels.solarPanelsExist shouldBe false // solar panels don't come with battery renovation
    }

    "return original BuildingCalculationInput when JSON parsing fails" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.BATTERY_STORAGE,
            renovationMeasureValue = RenovationMeasureValue.LITHIUM_ION_BATTERY,
            renovationMeasureObject = """{"invalid": json""", // Invalid JSON
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result shouldBe buildingCalculationInput
    }
})
