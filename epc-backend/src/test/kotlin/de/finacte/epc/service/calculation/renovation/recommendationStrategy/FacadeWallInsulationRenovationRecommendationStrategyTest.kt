package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.FacadeWallInsulationRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year

class FacadeWallInsulationRenovationRecommendationStrategyTest : StringSpec({
    val sut = FacadeWallInsulationRenovationRecommendationStrategy()


    "test recommendation strategy - facade insulation year younger than 5 == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            facadeWallInsulation = BuildingCalculationInput.FacadeWallInsulation(
                facadeWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                facadeWallInsulationThickness = 10,
                facadeWallInsulationYear = Year.now().value - 2, //relevant
                facadeWallInsulationRenewed = true
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXPANDED_POLYSTYRENE_30_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - facade insulation year older than 5 == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            facadeWallInsulation = BuildingCalculationInput.FacadeWallInsulation(
                facadeWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                facadeWallInsulationThickness = 10,
                facadeWallInsulationYear = Year.now().value - 6, //relevant
                facadeWallInsulationRenewed = true
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXPANDED_POLYSTYRENE_30_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - facade insulation not exist at all" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            facadeWallInsulation = null
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXPANDED_POLYSTYRENE_30_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

})
