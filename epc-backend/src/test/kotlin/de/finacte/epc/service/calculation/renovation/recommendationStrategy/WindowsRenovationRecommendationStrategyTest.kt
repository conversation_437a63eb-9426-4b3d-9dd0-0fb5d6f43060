package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingWindowsGlazing
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.TripleGlazedWindowsRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year

class WindowsRenovationRecommendationStrategyTest : StringSpec({
    val sut = TripleGlazedWindowsRenovationRecommendationStrategy()


    "test recommendation strategy - renovation younger than 5 years and no TRIPLE glazing  == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = BuildingWindowsGlazing.SINGLE,
                windowsRenewed = false,
                windowsInstallationYear = Year.now().value - 3,
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - renovation older than 5 years and SINGLE glazing  == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = BuildingWindowsGlazing.SINGLE,
                windowsRenewed = false,
                windowsInstallationYear = Year.now().value - 6,
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure",
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - renovation younger than 5 years and DOUBLE glazing  == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = BuildingWindowsGlazing.DOUBLE,
                windowsRenewed = false,
                windowsInstallationYear = Year.now().value - 3,
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - renovation older than 5 years and DOUBLE glazing  == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = BuildingWindowsGlazing.DOUBLE,
                windowsRenewed = false,
                windowsInstallationYear = Year.now().value - 6,
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1,//not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - renovation older than 5 years and TRIPLE glazing  == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = BuildingWindowsGlazing.TRIPLE,
                windowsRenewed = false,
                windowsInstallationYear = Year.now().value - 6,
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,//not relevant
            renovationMeasureObject = "",//not relevant
            listOrder = 1,//not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure",
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

})
