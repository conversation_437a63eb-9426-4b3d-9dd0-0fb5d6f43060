package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedBuildingShapeResult
import de.finacte.epc.createFullyInitializedClimateDataResult
import de.finacte.epc.entity.SystemEfficiencyEntity
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.Season
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.util.*

class V1SolarPlantProductionTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1SolarPlantProduction(systemEfficiencyRepositoryMock)

    "calculate solar plant energy when solar plant exist and nominal energy is provided" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarPlant = BuildingCalculationInput.SolarPlant(
                solarPlantExist = true,
                solarPlantNominalPower = 5.0,
                solarPlantInstallationYear = 2023
            )
        )
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            averageAnnualSolarRadiation = 1100.0,
        )
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        every { systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(SystemEfficiencyType.SOLAR_PLANT, 2023) } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.SOLAR_PLANT,
            efficiency = 0.80,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(buildingCalculationInput, buildingShapeResult, climateDataResult)

        result shouldBe SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1320.0,
                Season.SUMMER to 1980.0,
                Season.AUTUMN to 792.0,
                Season.WINTER to 308.00000000000006
            ),
            annualProducedEnergy = 4400.0
        )
    }

    "calculate solar plant energy when solar plant exist and roof is PITCHED" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarPlant = BuildingCalculationInput.SolarPlant(
                solarPlantExist = true,
                solarPlantNominalPower = null,
                solarPlantInstallationYear = 2023,
            ),
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                roofFloor = BuildingRoofFloor.PARTIALLY_LIVEABLE,
            ),
        )
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            averageAnnualSolarRadiation = 1100.0,
        )
        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            buildingRoofSurfaceArea = 50.0
        )
        every { systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(SystemEfficiencyType.SOLAR_PANEL, 2023) } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.SOLAR_PANEL,
            efficiency = 0.18,
            validFrom = 1,
            validTo = 9999
        )
        every { systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(SystemEfficiencyType.SOLAR_PLANT, 2023) } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.SOLAR_PLANT,
            efficiency = 0.80,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(buildingCalculationInput, buildingShapeResult, climateDataResult)

        result shouldBe SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1663.2,
                Season.SUMMER to 2494.8,
                Season.AUTUMN to 997.92,
                Season.WINTER to 388.0800000000001
            ),
            annualProducedEnergy = 5544.0
        )
    }

    "calculate solar plant energy when solar plant exist and roof is FLAT" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarPlant = BuildingCalculationInput.SolarPlant(
                solarPlantExist = true,
                solarPlantNominalPower = null,
                solarPlantInstallationYear = 2023,
            ),
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                roofFloor = BuildingRoofFloor.FLAT_ROOF,
            ),
        )
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            averageAnnualSolarRadiation = 1100.0,
        )
        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            buildingRoofSurfaceArea = 50.0
        )
        every { systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(SystemEfficiencyType.SOLAR_PANEL, 2023) } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.SOLAR_PANEL,
            efficiency = 0.18,
            validFrom = 1,
            validTo = 9999
        )
        every { systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(SystemEfficiencyType.SOLAR_PLANT, 2023) } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.SOLAR_PLANT,
            efficiency = 0.80,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(buildingCalculationInput, buildingShapeResult, climateDataResult)

        result shouldBe result shouldBe SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1425.6000000000001,
                Season.SUMMER to 2138.4,
                Season.AUTUMN to 855.36,
                Season.WINTER to 332.6400000000001
            ),
            annualProducedEnergy = 4752.0
        )
    }

    "calculate solar plant energy when solar plant not exist" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarPlant = BuildingCalculationInput.SolarPlant(
                solarPlantExist = false,
                solarPlantNominalPower = null,
                solarPlantInstallationYear = 2023,
            ),
        )
        val climateDataResult = createFullyInitializedClimateDataResult()
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()

        val result = sut.calculate(buildingCalculationInput, buildingShapeResult, climateDataResult)

        result shouldBe result shouldBe SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 0.0,
                Season.SUMMER to 0.0,
                Season.AUTUMN to 0.0,
                Season.WINTER to 0.0
            ),
            annualProducedEnergy = 0.0
        )
    }

})
