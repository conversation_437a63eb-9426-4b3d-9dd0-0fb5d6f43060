package de.finacte.epc.service.calculation.result.intermediate.uValues.v2

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedBuildingShapeResult
import de.finacte.epc.entity.ConstructionMaterialPropertiesEntity
import de.finacte.epc.entity.ConstructionMaterialType
import de.finacte.epc.entity.building.BuildingFloorFinishType
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.building.BuildingScreedType
import de.finacte.epc.entity.building.BuildingWallType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.repository.ConstructionMaterialPropertiesRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.doubles.plusOrMinus
import io.mockk.every
import io.mockk.mockk
import java.util.UUID

class BasementFloorUValueCalculationStrategyTest : StringSpec({
    // Constants for the test - they are the same as in BuildingMissingAttributesInputStaticGeneratorImpl
    val CEMENT_SCREED_THICKNESS = 10
    val WOODEN_FINISH_THICKNESS = 2

    val constructionMaterialPropertiesRepository = mockk<ConstructionMaterialPropertiesRepository>()
    val sut = BasementFloorUValueCalculationStrategy(constructionMaterialPropertiesRepository)

    // Mock repository responses - values are the same as in the database
    // For concrete base slab
    every {
        constructionMaterialPropertiesRepository.findByNameAndType(
            BuildingWallType.CONCRETE.name,
            ConstructionMaterialType.WALL
        )
    } returns ConstructionMaterialPropertiesEntity(
        id = UUID.randomUUID(),
        name = BuildingWallType.CONCRETE.name,
        type = ConstructionMaterialType.WALL,
        thermalConductivity = 1.280,
        thermalResistance = null,
        thicknessInMetersFrom = null,
        thicknessInMetersTo = null,
        uValue = null
    )

    // For cement screed
    every {
        constructionMaterialPropertiesRepository.findByNameAndType(
            BuildingScreedType.CEMENT.name,
            ConstructionMaterialType.SCREED
        )
    } returns ConstructionMaterialPropertiesEntity(
        id = UUID.randomUUID(),
        name = BuildingScreedType.CEMENT.name,
        type = ConstructionMaterialType.SCREED,
        thermalConductivity = 1.6,
        thermalResistance = null,
        thicknessInMetersFrom = null,
        thicknessInMetersTo = null,
        uValue = null
    )

    // For wooden planks finish
    every {
        constructionMaterialPropertiesRepository.findByNameAndType(
            BuildingFloorFinishType.WOODEN_PLANKS.name,
            ConstructionMaterialType.FLOOR_FINISH
        )
    } returns ConstructionMaterialPropertiesEntity(
        id = UUID.randomUUID(),
        name = BuildingFloorFinishType.WOODEN_PLANKS.name,
        type = ConstructionMaterialType.FLOOR_FINISH,
        thermalConductivity = 0.14,
        thermalResistance = null,
        thicknessInMetersFrom = null,
        thicknessInMetersTo = null,
        uValue = null
    )

    // For mineral wool insulation
    every {
        constructionMaterialPropertiesRepository.findByNameAndType(
            BuildingInsulationType.MINERAL_WOOL.name,
            ConstructionMaterialType.INSULATION
        )
    } returns ConstructionMaterialPropertiesEntity(
        id = UUID.randomUUID(),
        name = BuildingInsulationType.MINERAL_WOOL.name,
        type = ConstructionMaterialType.INSULATION,
        thermalConductivity = 0.036,
        thermalResistance = null,
        thicknessInMetersFrom = null,
        thicknessInMetersTo = null,
        uValue = null
    )

    "calculate basement floor U-value with cement screed, wooden planks finish, and without insulation - large building" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            basementFloor = BuildingCalculationInput.BasementFloor(
                basementFloorThickness = CEMENT_SCREED_THICKNESS,
                basementFloorType = BuildingScreedType.CEMENT,
                basementFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
                basementFloorFinishThickness = WOODEN_FINISH_THICKNESS
            ),
            basementInsulation = null,
        )

        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            buildingBaseArea = 150.0,
            buildingPerimeter = 50.0
        )

        val result = sut.apply(buildingCalculationInput, buildingShapeResult)


        result.thermalEnvelopeElementType shouldBe ThermalEnvelopeElementType.BASEMENT_FLOOR
        val expectedUValue = 1.573
        result.uValue shouldBe (expectedUValue plusOrMinus 0.005)
    }

    "calculate basement floor U-value with cement screed, wooden planks finish, and with insulation - large building" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            basementFloor = BuildingCalculationInput.BasementFloor(
                basementFloorThickness = CEMENT_SCREED_THICKNESS,
                basementFloorType = BuildingScreedType.CEMENT,
                basementFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
                basementFloorFinishThickness = WOODEN_FINISH_THICKNESS
            ),
            basementInsulation = BuildingCalculationInput.BasementInsulation(
                basementFloorInsulationType = BuildingInsulationType.MINERAL_WOOL,
                basementFloorInsulationThickness = 10,
                basementExternalWallsInsulationType = BuildingInsulationType.MINERAL_WOOL,
                basementExternalWallsInsulationThickness = 10,
                basementInsulationYear = 2020
            ),
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                basementExist = true
            )
        )

        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            buildingBaseArea = 150.0,
            buildingPerimeter = 50.0
        )

        val result = sut.apply(buildingCalculationInput, buildingShapeResult)


        result.thermalEnvelopeElementType shouldBe ThermalEnvelopeElementType.BASEMENT_FLOOR
        val expectedUValue = 0.294
        result.uValue shouldBe (expectedUValue plusOrMinus 0.005)
    }


    "calculate basement floor U-value with cement screed, wooden planks finish, and without insulation - small building" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            basementFloor = BuildingCalculationInput.BasementFloor(
                basementFloorThickness = CEMENT_SCREED_THICKNESS,
                basementFloorType = BuildingScreedType.CEMENT,
                basementFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
                basementFloorFinishThickness = WOODEN_FINISH_THICKNESS
            ),
            basementInsulation = null,
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                basementExist = false
            )
        )

        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            buildingBaseArea = 54.0,
            buildingPerimeter = 32.0
        )

        val result = sut.apply(buildingCalculationInput, buildingShapeResult)


        result.thermalEnvelopeElementType shouldBe ThermalEnvelopeElementType.BASEMENT_FLOOR
        val expectedUValue = 1.675
        result.uValue shouldBe (expectedUValue plusOrMinus 0.005)
    }

    "calculate basement floor U-value with cement screed, wooden planks finish, and with insulation - small building" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            basementFloor = BuildingCalculationInput.BasementFloor(
                basementFloorThickness = CEMENT_SCREED_THICKNESS,
                basementFloorType = BuildingScreedType.CEMENT,
                basementFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
                basementFloorFinishThickness = WOODEN_FINISH_THICKNESS
            ),
            basementInsulation = BuildingCalculationInput.BasementInsulation(
                basementFloorInsulationType = BuildingInsulationType.MINERAL_WOOL,
                basementFloorInsulationThickness = 10,
                basementExternalWallsInsulationType = BuildingInsulationType.MINERAL_WOOL,
                basementExternalWallsInsulationThickness = 10,
                basementInsulationYear = 2020
            ),
        )

        val buildingShapeResult = createFullyInitializedBuildingShapeResult().copy(
            buildingBaseArea = 54.0,
            buildingPerimeter = 32.0
        )

        val result = sut.apply(buildingCalculationInput, buildingShapeResult)


        result.thermalEnvelopeElementType shouldBe ThermalEnvelopeElementType.BASEMENT_FLOOR
        val expectedUValue = 0.299
        result.uValue shouldBe (expectedUValue plusOrMinus 0.005)
    }
})
