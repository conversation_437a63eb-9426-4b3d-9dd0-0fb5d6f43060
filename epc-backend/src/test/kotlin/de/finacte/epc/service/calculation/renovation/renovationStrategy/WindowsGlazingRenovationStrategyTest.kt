package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingWindowsGlazing
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year
import java.util.UUID

class WindowsGlazingRenovationStrategyTest : StringSpec({
    val sut = WindowsGlazingRenovationStrategy()

    "apply windows glazing renovation to BuildingCalculationInput with double glazing" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.DOUBLE_GLAZING,
            renovationMeasureObject = "{ \"windowsGlazing\": \"DOUBLE\", \"windowsRenewed\": true , \"windowsInstallationYear\": 2025 }",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.windowsGlazing.windowsGlazing shouldBe BuildingWindowsGlazing.DOUBLE
        result.windowsGlazing.windowsRenewed shouldBe true
        result.windowsGlazing.windowsInstallationYear shouldBe Year.now().value
    }

    "apply windows glazing renovation to BuildingCalculationInput with triple glazing" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.TRIPLE_GLAZING,
            renovationMeasureObject = "{ \"windowsGlazing\": \"TRIPLE\", \"windowsRenewed\": true , \"windowsInstallationYear\": 2025 }",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.windowsGlazing.windowsGlazing shouldBe BuildingWindowsGlazing.TRIPLE
        result.windowsGlazing.windowsRenewed shouldBe true
        result.windowsGlazing.windowsInstallationYear shouldBe Year.now().value
    }
})

