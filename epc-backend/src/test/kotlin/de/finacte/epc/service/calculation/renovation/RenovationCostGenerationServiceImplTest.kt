package de.finacte.epc.service.calculation.renovation

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedBuildingShapeResult
import de.finacte.epc.entity.Language
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationCostEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.entity.renovation.RenovationTemplateTranslationEntity
import de.finacte.epc.repository.renovation.RenovationCostRepository
import de.finacte.epc.service.gpt.GPTChat
import de.finacte.epc.service.gpt.GPTChatOpenAIImpl
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.springframework.ai.chat.messages.MessageType
import java.util.UUID

class RenovationCostGenerationServiceImplTest : StringSpec({
    val gptChatMock = mockk<GPTChat>()
    val renovationCostRepository = mockk<RenovationCostRepository>()
    val sut = RenovationCostGenerationServiceImpl(gptChatMock, renovationCostRepository)


    "generate user message for one recommended renovation" {
        val buildingId = UUID.randomUUID()
        val renovationId = UUID.randomUUID()
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val renovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
        )
        val renovations = setOf(
            createRenovationSetWithTemplate(
                buildingId = buildingId,
                renovationTemplate = renovationTemplate,
                recommended = true,
                renovationId = renovationId
            )
        )
        val result = sut.generateUserMessage(renovations, buildingCalculationInput, buildingShapeResult)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
            Building description:
            - Country: Germany
            - Zip code: 20146
            - Building area: 100.0 sqm
            - Facade surface: 89.25 sqm
            - Windows surface: 15.75 sqm
            
            Planned renovations:
            - renovation_measure_category: WINDOWS
              renovation_measure_id: PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
              description: Renovation measure

        """.trimIndent()
    }

    "generate user message for one recommended renovation and one not recommended" {
        val buildingId = UUID.randomUUID()
        val recommendedRenovationId = UUID.randomUUID()
        val notRecommendedRenovationId = UUID.randomUUID()
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val recommendedRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
        )
        val notRecommendedRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_THERMAL,
            renovationMeasureType = RenovationMeasureType.SOLAR_THERMAL,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_THERMAL_2025
        )
        val renovations = setOf(
            createRenovationSetWithTemplate(
                buildingId = buildingId,
                renovationTemplate = recommendedRenovationTemplate,
                recommended = true,
                renovationId = recommendedRenovationId
            ),
            createRenovationSetWithTemplate(
                buildingId = buildingId,
                renovationTemplate = notRecommendedRenovationTemplate,
                recommended = false,
                renovationId = notRecommendedRenovationId
            )
        )
        val result = sut.generateUserMessage(renovations, buildingCalculationInput, buildingShapeResult)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
            Building description:
            - Country: Germany
            - Zip code: 20146
            - Building area: 100.0 sqm
            - Facade surface: 89.25 sqm
            - Windows surface: 15.75 sqm
            
            Planned renovations:
            - renovation_measure_category: WINDOWS
              renovation_measure_id: PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
              description: Renovation measure

        """.trimIndent()
    }

    "generate system message " {
        val result = sut.generateSystemMessage()

        result.messageType shouldBe MessageType.SYSTEM
        result.text shouldBe """
            The user will provide a description of a building and a list of planned renovation measures. Your task is to estimate the renovation cost for each listed measure.
            
            Instructions:
            - Estimate only the renovations provided by the user.
            - Consider the building's shape, surface areas, and location (based on German zip code).
            - For each renovation measure:
              - Repeat the cost estimation 5 times using different data sources:
                1. German Construction Cost Index
                2. Quotations from local construction companies (use zip code)
                3. Market analysis from German Renewable Energy Associations
                4. Standard pricing from German construction material suppliers
                5. Any relevant German cost estimation dataset
            - Aggregate the results to produce:
              - A cost range (minimum and maximum)
              - A most probable cost value

        """.trimIndent()
    }

    "remove duplicates from GPT response" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
        )
        val solarThermalRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_THERMAL,
            renovationMeasureType = RenovationMeasureType.SOLAR_THERMAL,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_THERMAL_2025
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarThermalRenovation =createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarThermalRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarThermalRenovation
        )
        val listOfDuplicatedResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.WINDOWS,
                RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
                100.0,
                200.0,
                150.0
            ),
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.WINDOWS,
                RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_THERMAL,
                RenovationMeasureValue.SOLAR_THERMAL_2025,
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = listOf(
            RenovationCostEntity(id=null, minCost=100.0, maxCost=200.0, mostProbableValue=150.0, renovation = windowRenovation),
            RenovationCostEntity(id=null, minCost=300.0, maxCost=400.0, mostProbableValue=350.0, renovation = solarThermalRenovation)
        )
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_41_NANO
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfDuplicatedResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }

    }

    "do not generate cost in case GPT mix renovation types" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
        )
        val solarThermalRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_THERMAL,
            renovationMeasureType = RenovationMeasureType.SOLAR_THERMAL,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_THERMAL_2025
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarThermalRenovation =createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarThermalRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarThermalRenovation
        )
        val listOfStrangeResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.WINDOWS,
                RenovationMeasureValue.SOLAR_THERMAL_2025, // MIXED result!
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_THERMAL,
                RenovationMeasureValue.SOLAR_THERMAL_2025,
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = listOf(
            RenovationCostEntity(id=null, minCost=300.0, maxCost=400.0, mostProbableValue=350.0, renovation = solarThermalRenovation)
        )
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_41_NANO
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfStrangeResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }

    }

    "do not generate cost in case GPT miss renovation types" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
        )
        val solarThermalRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_THERMAL,
            renovationMeasureType = RenovationMeasureType.SOLAR_THERMAL,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_THERMAL_2025
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarThermalRenovation =createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarThermalRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarThermalRenovation
        )
        val listOfStrangeResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.HEATING_SYSTEM, // It wasn't supposed to return this
                RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_THERMAL,
                RenovationMeasureValue.SOLAR_THERMAL_2025,
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = listOf(
            RenovationCostEntity(id=null, minCost=300.0, maxCost=400.0, mostProbableValue=350.0, renovation = solarThermalRenovation)
        )
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_41_NANO
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfStrangeResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }

    }

    "do not generate cost in case GPT miss and mix renovation types" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025
        )
        val solarThermalRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_THERMAL,
            renovationMeasureType = RenovationMeasureType.SOLAR_THERMAL,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_THERMAL_2025
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarThermalRenovation =createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarThermalRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarThermalRenovation
        )
        val listOfStrangeResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.HEATING_SYSTEM, // It wasn't supposed to return this
                RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_THERMAL,
                RenovationMeasureValue.SOLAR_PANELS_2025,  // MIXED result!
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = emptyList<RenovationCostEntity>()
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_41_NANO
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfStrangeResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }
    }

})

private fun createRenovationTemplateEntity(
    renovationMeasureCategory: RenovationMeasureCategory,
    renovationMeasureType: RenovationMeasureType,
    renovationMeasureValue: RenovationMeasureValue
) = RenovationTemplateEntity(
    id = null,
    renovationMeasureCategory = renovationMeasureCategory,
    renovationMeasureType = renovationMeasureType,
    renovationMeasureValue = renovationMeasureValue,
    renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
    renovationMeasureObject = "",
    listOrder = 1,
    renovationTemplateTranslations = listOf(
        RenovationTemplateTranslationEntity(
            id = null,
            lang = Language.EN,
            measureDsc = "Measure $renovationMeasureCategory description",
            measure = "Measure $renovationMeasureCategory",
            renovationTemplate = mockk<RenovationTemplateEntity>()
        )
    ),
    grantPrograms = emptyList(),
    renovationMeasureDescription = "Renovation measure",
    renovationMeasureVersion = 1
)

private fun createRenovationSetWithTemplate(
    buildingId: UUID,
    renovationTemplate: RenovationTemplateEntity,
    recommended: Boolean,
    renovationId: UUID
) = RenovationEntity(
    buildingId = buildingId,
    renovationTemplate = renovationTemplate,
    recommended = recommended,
    selected = false,
    id = renovationId,
    renovationCost = null
)
