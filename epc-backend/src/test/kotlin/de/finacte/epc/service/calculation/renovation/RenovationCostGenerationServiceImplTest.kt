package de.finacte.epc.service.calculation.renovation

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedBuildingShapeResult
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationCostEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.repository.renovation.RenovationCostRepository
import de.finacte.epc.service.gpt.GPTChat
import de.finacte.epc.service.gpt.GPTChatOpenAIImpl
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.springframework.ai.chat.messages.MessageType
import java.util.UUID

class RenovationCostGenerationServiceImplTest : StringSpec({
    val gptChatMock = mockk<GPTChat>()
    val renovationCostRepository = mockk<RenovationCostRepository>()
    val sut = RenovationCostGenerationServiceImpl(gptChatMock, renovationCostRepository)


    "generate user message for one recommended renovation" {
        val buildingId = UUID.randomUUID()
        val renovationId = UUID.randomUUID()
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val renovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.TRIPLE_GLAZING
        )
        val renovations = setOf(
            createRenovationSetWithTemplate(
                buildingId = buildingId,
                renovationTemplate = renovationTemplate,
                recommended = true,
                renovationId = renovationId
            )
        )
        val result = sut.generateUserMessage(renovations, buildingCalculationInput, buildingShapeResult)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
            Building description:
            - Country: Germany
            - Zip code: 20146
            - Building living area: 100.0 sqm
            - Facade surface: 89.25 sqm
            - Windows surface: 15.75 sqm
            - Roof surface: 50.0 sqm
            
            Planned renovations:
            - renovation_measure_category: WINDOWS
              renovation_measure_type: WINDOWS_GLAZING_TYPE
              renovation_measure_value: TRIPLE_GLAZING
              variant: DEFAULT
              description: Renovation measure

        """.trimIndent()
    }

    "generate user message for one recommended renovation and one not recommended" {
        val buildingId = UUID.randomUUID()
        val recommendedRenovationId = UUID.randomUUID()
        val notRecommendedRenovationId = UUID.randomUUID()
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val recommendedRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.TRIPLE_GLAZING
        )
        val notRecommendedRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.SOLAR_PANELS,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT
        )
        val renovations = setOf(
            createRenovationSetWithTemplate(
                buildingId = buildingId,
                renovationTemplate = recommendedRenovationTemplate,
                recommended = true,
                renovationId = recommendedRenovationId
            ),
            createRenovationSetWithTemplate(
                buildingId = buildingId,
                renovationTemplate = notRecommendedRenovationTemplate,
                recommended = false,
                renovationId = notRecommendedRenovationId
            )
        )
        val result = sut.generateUserMessage(renovations, buildingCalculationInput, buildingShapeResult)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
            Building description:
            - Country: Germany
            - Zip code: 20146
            - Building living area: 100.0 sqm
            - Facade surface: 89.25 sqm
            - Windows surface: 15.75 sqm
            - Roof surface: 50.0 sqm
            
            Planned renovations:
            - renovation_measure_category: WINDOWS
              renovation_measure_type: WINDOWS_GLAZING_TYPE
              renovation_measure_value: TRIPLE_GLAZING
              variant: DEFAULT
              description: Renovation measure
            - renovation_measure_category: SOLAR_PANELS
              renovation_measure_type: SOLAR_PANELS
              renovation_measure_value: DEFAULT
              variant: DEFAULT
              description: Renovation measure

        """.trimIndent()
    }

    "generate system message " {
        val result = sut.generateSystemMessage()

        result.messageType shouldBe MessageType.SYSTEM
        result.text shouldBe """
        The user will provide a description of a building and a list of planned renovation measures. Your task is to estimate the renovation cost for each listed measure.
        
        Instructions:
        - Estimate only the renovations provided by the user.
        - For all cost calculations, use ONLY the user’s provided building areas for living, facade, windows, and roof surfaces, to estimate the cost. Never attempt to calculate these areas.
        - For all cost calculations, use the location (based on German zip code) provided by user to find local cost data.
        - Consider costs of installation and materials.
        - Costs must be in EUR, and they must be gross costs (VAT according to Germany).
        - If you find material cost data that is more than a year old, take into account any cost increases or decreases that occurred during that time.
        - For each renovation measure:
          - Repeat the cost estimation 5 times using different data sources:
            1. German Construction Cost Index
            2. Quotations from local construction companies (use zip code)
            3. Market analysis from German Renewable Energy Associations
            4. Standard pricing from German construction material suppliers
            5. Any relevant German cost estimation dataset
        
        - When comparing similar renovation measures:
          - If two measures have the same category and type but different variants (e.g. BASIC and ADVANCED), enforce the following rule:
            - The ADVANCED variant must **always** result in a **higher** most probable cost and upper range value than the BASIC variant.
            - Adjust estimates as needed across the 5 sources to comply with this rule.
          - Always differentiate cost estimates based on technical complexity and included features.Even if two renovation measures belong to the similar or same category (e.g., underfloor heating), you must treat them as separate if they differ in method, materials, or scope. Consider the following when adjusting costs for similar measures:
            - Installation method (e.g., wet heating system vs. dry systems).
            - Included components (e.g., includes flooring or not, basic or advanced controls).
            - Additional features (e.g., security features, advanced controls, etc.).
            - Level of reconstruction required (e.g., minimal vs. structural changes).
            - Material quality (e.g., standard vs. premium fittings).
          - When calculating solar panel system size and battery size for this system, calculate the size using modules with 0.45 kWp per 2 sqm and covering 50% of the roof surface.
          - For a given project, the total installed price of lithium-ion battery storage must never exceed the total installed price of the rooftop solar panel system sized based on the specification (0.45 kWp per 2 sqm, 50% roof coverage).
        
        - For each renovation measure, provide:
          - A cost range (minimum and maximum)
          - A most probable cost value (e.g., median or weighted average of the estimates)

        """.trimIndent()
    }

    "remove duplicates from GPT response" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.TRIPLE_GLAZING
        )
        val solarThermalRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.SOLAR_PANELS,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarThermalRenovation =createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarThermalRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarThermalRenovation
        )
        val listOfDuplicatedResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.WINDOWS,
                RenovationMeasureType.WINDOWS_GLAZING_TYPE,
                RenovationMeasureValue.TRIPLE_GLAZING,
                100.0,
                200.0,
                150.0
            ),
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.WINDOWS,
                RenovationMeasureType.WINDOWS_GLAZING_TYPE,
                RenovationMeasureValue.TRIPLE_GLAZING,
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_PANELS,
                RenovationMeasureType.SOLAR_PANELS,
                RenovationMeasureValue.DEFAULT,
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = listOf(
            RenovationCostEntity(id=null, minCost=100.0, maxCost=200.0, mostProbableValue=150.0, renovation = windowRenovation),
            RenovationCostEntity(id=null, minCost=300.0, maxCost=400.0, mostProbableValue=350.0, renovation = solarThermalRenovation)
        )
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_4_1
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfDuplicatedResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }

    }

    "do not generate cost in case GPT mix renovation types" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.TRIPLE_GLAZING
        )
        val solarPanelsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.SOLAR_PANELS,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarPanelsRenovation =createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarPanelsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarPanelsRenovation
        )
        val listOfStrangeResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.WINDOWS,
                RenovationMeasureType.WINDOWS_GLAZING_TYPE,
                RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025, // MIXED result!
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_PANELS,
                RenovationMeasureType.SOLAR_PANELS,
                RenovationMeasureValue.DEFAULT,
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = listOf(
            RenovationCostEntity(id=null, minCost=300.0, maxCost=400.0, mostProbableValue=350.0, renovation = solarPanelsRenovation)
        )
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_4_1
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfStrangeResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }

    }

    "do not generate cost in case GPT miss renovation types" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.TRIPLE_GLAZING
        )
        val solarPanelsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.SOLAR_PANELS,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarPanelsRenovation =createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarPanelsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarPanelsRenovation
        )
        val listOfStrangeResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.HEATING_SYSTEM, // It wasn't supposed to return this
                RenovationMeasureType.HEATING_PRIMARY_SOURCE,
                RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_PANELS,
                RenovationMeasureType.SOLAR_PANELS,
                RenovationMeasureValue.DEFAULT,
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = listOf(
            RenovationCostEntity(id=null, minCost=300.0, maxCost=400.0, mostProbableValue=350.0, renovation = solarPanelsRenovation)
        )
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_4_1
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfStrangeResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }

    }

    "do not generate cost in case GPT miss and mix renovation types" {
        val buildingId = UUID.randomUUID()

        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId = buildingId)
        val buildingShapeResult = createFullyInitializedBuildingShapeResult()
        val windowsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.WINDOWS,
            renovationMeasureType = RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            renovationMeasureValue = RenovationMeasureValue.TRIPLE_GLAZING
        )
        val solarPanelsRenovationTemplate = createRenovationTemplateEntity(
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PANELS,
            renovationMeasureType = RenovationMeasureType.SOLAR_PANELS,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT
        )
        val windowRenovation =  createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = windowsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val solarPanelsRenovation = createRenovationSetWithTemplate(
            buildingId = buildingId,
            renovationTemplate = solarPanelsRenovationTemplate,
            recommended = true,
            renovationId = UUID.randomUUID()
        )
        val renovations = setOf(
            windowRenovation,solarPanelsRenovation
        )
        val listOfStrangeResults = listOf(
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.HEATING_SYSTEM, // It wasn't supposed to return this
                RenovationMeasureType.HEATING_PRIMARY_SOURCE,
                RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
                110.0,
                210.0,
                160.0
            ), // Duplicate based on type & subtype
            RenovationCostGenerationService.RenovationCostGenerationResultItem(
                RenovationMeasureCategory.SOLAR_PANELS,
                RenovationMeasureType.SOLAR_PANELS,
                RenovationMeasureValue.TRIPLE_GLAZING,  // MIXED result!
                300.0,
                400.0,
                350.0
            )
        )
        val expectedUniqueEntitiesToBeSaved = emptyList<RenovationCostEntity>()
        every {
            gptChatMock.call(
                any(),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_4_1
            )
        } returns RenovationCostGenerationService.RenovationCostGenerationResult(listOfStrangeResults)
        every {
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        } returns expectedUniqueEntitiesToBeSaved

        sut.getRenovationCost(
            renovations, buildingCalculationInput, buildingShapeResult
        )

        verify{
            renovationCostRepository.saveAll(expectedUniqueEntitiesToBeSaved)
        }
    }

})

private fun createRenovationTemplateEntity(
    renovationMeasureCategory: RenovationMeasureCategory,
    renovationMeasureType: RenovationMeasureType,
    renovationMeasureValue: RenovationMeasureValue
) = RenovationTemplateEntity(
    id = null,
    renovationMeasureCategory = renovationMeasureCategory,
    renovationMeasureType = renovationMeasureType,
    renovationMeasureValue = renovationMeasureValue,
    renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
    renovationMeasureObject = "",
    listOrder = 1,
    grantPrograms = emptyList(),
    renovationMeasureDescription = "Renovation measure"
)

private fun createRenovationSetWithTemplate(
    buildingId: UUID,
    renovationTemplate: RenovationTemplateEntity,
    recommended: Boolean,
    renovationId: UUID
) = RenovationEntity(
    buildingId = buildingId,
    renovationTemplate = renovationTemplate,
    recommended = recommended,
    selected = false,
    id = renovationId,
    renovationCost = null
)
