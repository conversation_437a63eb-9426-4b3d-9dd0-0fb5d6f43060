package de.finacte.epc.service.calculation.result.intermediate.finalEffectiveHeatingEnergy

import de.finacte.epc.createFullyInitializedClimateDataResult
import de.finacte.epc.entity.SystemEfficiencyEntity
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.util.*

class V1FinalEffectiveHeatingEnergyTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1FinalEffectiveHeatingEnergy(systemEfficiencyRepositoryMock)


    "calculate final effective heating energy" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            heatingDegreeDays = 3000
        )
        val buildingCalculationInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.GAS,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false,
        )
        val heatTransferCoefficient = 90.0
        val ventilationLossCoefficient = 60.0
        every {
            systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
                SystemEfficiencyType.GAS,
                1990
            )
        } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.GAS,
            efficiency = 0.90,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInputHeating,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        result shouldBe 12000.0
    }
})
