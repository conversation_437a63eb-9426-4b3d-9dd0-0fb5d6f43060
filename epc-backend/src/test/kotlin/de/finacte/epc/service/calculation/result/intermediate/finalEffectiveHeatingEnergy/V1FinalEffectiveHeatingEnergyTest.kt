package de.finacte.epc.service.calculation.result.intermediate.finalEffectiveHeatingEnergy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedClimateDataResult
import de.finacte.epc.entity.SystemEfficiencyEntity
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.BuildingWindowsShutters
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.util.*

class V1FinalEffectiveHeatingEnergyTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1FinalEffectiveHeatingEnergy(systemEfficiencyRepositoryMock)

    "calculate final effective heating energy with electric shutters" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            heatingDegreeDays = 3000
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating =
                BuildingCalculationInput.Heating(
                    heatingEnergySource = SystemEfficiencyType.GAS,
                    hotWaterEnergySource = SystemEfficiencyType.GAS,
                    heatingInstallationYear = 1990,
                    heatingInstallationModernized = false,
                ),
            windowsShutters = BuildingCalculationInput.WindowsShutters(
                windowsShutters = BuildingWindowsShutters.ELECTRIC
            )
        )
        val heatTransferCoefficient = 90.0
        val ventilationLossCoefficient = 60.0
        every {
            systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
                SystemEfficiencyType.GAS,
                1990
            )
        } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.GAS,
            efficiency = 0.90,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInput,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        result shouldBe 10800.0
    }

    "calculate final effective heating energy with manual shutters" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            heatingDegreeDays = 3000
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating =
                BuildingCalculationInput.Heating(
                    heatingEnergySource = SystemEfficiencyType.GAS,
                    hotWaterEnergySource = SystemEfficiencyType.GAS,
                    heatingInstallationYear = 1990,
                    heatingInstallationModernized = false,
                ),
            windowsShutters = BuildingCalculationInput.WindowsShutters(
                windowsShutters = BuildingWindowsShutters.MANUAL
            )
        )
        val heatTransferCoefficient = 90.0
        val ventilationLossCoefficient = 60.0
        every {
            systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
                SystemEfficiencyType.GAS,
                1990
            )
        } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.GAS,
            efficiency = 0.90,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInput,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        result shouldBe 11400.0
    }

    "calculate final effective heating energy" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            heatingDegreeDays = 3000
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating =
                BuildingCalculationInput.Heating(
                    heatingEnergySource = SystemEfficiencyType.GAS,
                    hotWaterEnergySource = SystemEfficiencyType.GAS,
                    heatingInstallationYear = 1990,
                    heatingInstallationModernized = false,
                ),
            windowsShutters = null
        )
        val heatTransferCoefficient = 90.0
        val ventilationLossCoefficient = 60.0
        every {
            systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
                SystemEfficiencyType.GAS,
                1990
            )
        } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.GAS,
            efficiency = 0.90,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInput,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        result shouldBe 12000.0
    }

    "calculate final effective heating energy with lighting automation installed" {
        val climateDataResult = createFullyInitializedClimateDataResult().copy(
            heatingDegreeDays = 3000
        )
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType.GAS,
                hotWaterEnergySource = SystemEfficiencyType.GAS,
                heatingInstallationYear = 1990,
                heatingInstallationModernized = false,
            ),
            intelligentHeatingControls = BuildingCalculationInput.IntelligentHeatingControls(
                installed = true
            ),
            windowsShutters = null
        )
        val heatTransferCoefficient = 90.0
        val ventilationLossCoefficient = 60.0

        every {
            systemEfficiencyRepositoryMock.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
                SystemEfficiencyType.GAS,
                1990
            )
        } returns SystemEfficiencyEntity(
            id = UUID.randomUUID(),
            systemEfficiencyType = SystemEfficiencyType.GAS,
            efficiency = 0.90,
            validFrom = 1,
            validTo = 9999
        )

        val result = sut.calculate(
            climateDataResult,
            buildingCalculationInput,
            heatTransferCoefficient,
            ventilationLossCoefficient
        )

        // With lighting automation, we expect a reduction in heating energy demand by a factor of 0.92
        // Base calculation: ((90.0 + 60.0) * 3000 * 24 / 1000) / 0.9 = 12000.0
        // With lighting automation: 12000.0 * 0.92 = 11040.0
        result shouldBe 11040.0
    }
})
