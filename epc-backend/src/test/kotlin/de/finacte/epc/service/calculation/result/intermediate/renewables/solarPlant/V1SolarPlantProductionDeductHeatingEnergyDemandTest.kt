package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.Season
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk

class V1SolarPlantProductionDeductHeatingEnergyDemandTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1SolarPanelsProduction(systemEfficiencyRepositoryMock)

    "deduct solar plant energy from heating energy demand when heat pump is installed" {
        val buildingAttributesInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.HEAT_PUMP,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 500.0,
                Season.SUMMER to 1000.0,
                Season.AUTUMN to 300.0,
                Season.WINTER to 200.0
            ),
            annualProducedEnergy = 2000.0
        )
        val finalHeatingEnergyDemand = 1000.0

        val result = sut.deductFromHeatingEnergyDemand(
            finalHeatingEnergyDemand,
            solarPlantProduction,
            buildingAttributesInputHeating
        )

        result shouldBe Pair(
            0.0,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 0.0,
                    Season.SUMMER to 1000.0,
                    Season.AUTUMN to 0.0,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 1000.0
            )
        )
    }

    "deduct solar plant energy from heating energy demand when electric heating is installed" {
        val buildingAttributesInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.ELECTRICITY,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 500.0,
                Season.SUMMER to 1000.0,
                Season.AUTUMN to 300.0,
                Season.WINTER to 200.0
            ),
            annualProducedEnergy = 2000.0
        )
        val finalHeatingEnergyDemand = 1100.0

        val result = sut.deductFromHeatingEnergyDemand(
            finalHeatingEnergyDemand,
            solarPlantProduction,
            buildingAttributesInputHeating
        )

        result shouldBe Pair(
            100.0,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 0.0,
                    Season.SUMMER to 1000.0,
                    Season.AUTUMN to 0.0,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 1000.0
            )
        )
    }

    "deduct solar plant energy from heating energy demand when electric heating is installed - more production than consumption" {
        val buildingAttributesInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.ELECTRICITY,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 500.0,
                Season.SUMMER to 1000.0,
                Season.AUTUMN to 300.0,
                Season.WINTER to 200.0
            ),
            annualProducedEnergy = 2000.0
        )
        val finalHeatingEnergyDemand = 1100.0

        val result = sut.deductFromHeatingEnergyDemand(
            finalHeatingEnergyDemand,
            solarPlantProduction,
            buildingAttributesInputHeating
        )

        result shouldBe Pair(
            100.0,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 0.0,
                    Season.SUMMER to 1000.0,
                    Season.AUTUMN to 0.0,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 1000.0
            )
        )
    }

    "deduct solar plant energy from heating energy demand when electric heating is installed - more consumption than production" {
        val buildingAttributesInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.ELECTRICITY,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 500.0,
                Season.SUMMER to 1000.0,
                Season.AUTUMN to 300.0,
                Season.WINTER to 200.0
            ),
            annualProducedEnergy = 2000.0
        )
        val finalHeatingEnergyDemand = 900.0

        val result = sut.deductFromHeatingEnergyDemand(
            finalHeatingEnergyDemand,
            solarPlantProduction,
            buildingAttributesInputHeating
        )

        result shouldBe Pair(
            0.0,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 100.0,
                    Season.SUMMER to 1000.0,
                    Season.AUTUMN to 0.0,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 1100.0
            )
        )
    }

    "deduct solar plant energy from heating energy demand when electric heating is installed - equal consumption than production" {
        val buildingAttributesInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.HEAT_PUMP,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 500.0,
                Season.SUMMER to 1000.0,
                Season.AUTUMN to 300.0,
                Season.WINTER to 200.0
            ),
            annualProducedEnergy = 2000.0
        )
        val finalHeatingEnergyDemand = 1000.0

        val result = sut.deductFromHeatingEnergyDemand(
            finalHeatingEnergyDemand,
            solarPlantProduction,
            buildingAttributesInputHeating
        )

        result shouldBe Pair(
            0.0,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 0.0,
                    Season.SUMMER to 1000.0,
                    Season.AUTUMN to 0.0,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 1000.0
            )
        )
    }

    "not deduct solar plant energy from heating energy demand when electric heating is not installed" {
        val buildingAttributesInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.GAS,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 500.0,
                Season.SUMMER to 1000.0,
                Season.AUTUMN to 300.0,
                Season.WINTER to 200.0
            ),
            annualProducedEnergy = 2000.0
        )
        val finalHeatingEnergyDemand = 1100.0

        val result = sut.deductFromHeatingEnergyDemand(
            finalHeatingEnergyDemand,
            solarPlantProduction,
            buildingAttributesInputHeating
        )

        result shouldBe Pair(
            1100.0,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 500.0,
                    Season.SUMMER to 1000.0,
                    Season.AUTUMN to 300.0,
                    Season.WINTER to 200.0
                ),
                annualProducedEnergy = 2000.0
            )
        )
    }

})
