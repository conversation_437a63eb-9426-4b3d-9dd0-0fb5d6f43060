package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.createFullyInitializedBuildingEntity
import de.finacte.epc.createFullyInitializedBuildingMissingAttributesGenerated
import de.finacte.epc.createFullyInitializedBuildingMissingAttributesGeneratedEntity
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.mapper.BuildingMissingAttributesGeneratedMapper
import de.finacte.epc.repository.BuildingMissingAttributesGeneratedRepository
import de.finacte.epc.repository.BuildingRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.BuildingMissingAttributesInputGPTGenerator
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.*

class BuildingCalculationInputServiceImplTest : StringSpec({
    val buildingCalculationInputFactory = mockk<BuildingCalculationInputFactory>()
    val buildingRepository = mockk<BuildingRepository>()

    val sut = BuildingCalculationInputServiceImpl(
        buildingCalculationInputFactory,
        buildingRepository
    )

    "getBuildingCalculationInput should throw exception when building not found" {
        val buildingId = UUID.randomUUID()
        every { buildingRepository.findById(buildingId) } returns Optional.empty()

        shouldThrow<EPCCalculationException> {
            sut.getBuildingCalculationInput(buildingId)
        }
    }

    "getBuildingCalculationInput should return existing attributes from repository when available" {
        val buildingId = UUID.randomUUID()
        val buildingEntity = createFullyInitializedBuildingEntity().copy(id = buildingId)
        val expectedBuildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId)

        every { buildingRepository.findById(buildingId) } returns Optional.of(buildingEntity)
        every { buildingCalculationInputFactory.createBuildingCalculationInput(buildingEntity) } returns expectedBuildingCalculationInput

        val result = sut.getBuildingCalculationInput(buildingId)

        result shouldBe expectedBuildingCalculationInput
    }

    "getBuildingCalculationInput should generate new attributes when not available in repository" {
        val buildingId = UUID.randomUUID()
        val buildingEntity = createFullyInitializedBuildingEntity().copy(id = buildingId)
        val expectedBuildingCalculationInput = createFullyInitializedBuildingCalculationInput(buildingId)

        every { buildingRepository.findById(buildingId) } returns Optional.of(buildingEntity)
        every { buildingCalculationInputFactory.createBuildingCalculationInput(buildingEntity) } returns expectedBuildingCalculationInput

        val result = sut.getBuildingCalculationInput(buildingId)

        result shouldBe expectedBuildingCalculationInput
    }
})
