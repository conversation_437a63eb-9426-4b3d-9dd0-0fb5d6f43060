package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.Season
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarThermal.HotWaterDemandWithoutSolarThermalProductionResult
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk

class V1SolarPlantProductionDeductHotWaterDemandTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1SolarPlantProduction(systemEfficiencyRepositoryMock)

    "deduct solar plant energy from hot water demand when hot water energy source is electricity" {
        val buildingCalculationInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.GAS,
            hotWaterEnergySource = SystemEfficiencyType.ELECTRICITY,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 2000.0,
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 3600.0
        )
        val hotWaterDemand = HotWaterDemandWithoutSolarThermalProductionResult(
            annualWaterDemandKWH = 3000.0,
            dailyWaterDemandKWH = 8.21917808219
        )

        val result = sut.deductFromHotWaterDemand(hotWaterDemand, solarPlantProduction, buildingCalculationInputHeating)

        result shouldBe Pair(
            HotWaterDemandWithoutSolarPlantProductionResult(annualWaterDemandKWH = 887.67123287639),
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 243.83561643852,
                    Season.SUMMER to 1243.83561643852,
                    Season.AUTUMN to 0.0,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 1487.67123287704
            )
        )
    }

    "do not deduct solar plant energy from hot water demand when hot water energy source is not electricity" {
        val buildingCalculationInputHeating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.GAS,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 2000.0,
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 3600.0
        )
        val hotWaterDemand = HotWaterDemandWithoutSolarThermalProductionResult(
            annualWaterDemandKWH = 3000.0,
            dailyWaterDemandKWH = 8.21917808219
        )

        val result = sut.deductFromHotWaterDemand(hotWaterDemand, solarPlantProduction, buildingCalculationInputHeating)

        result shouldBe Pair(
            HotWaterDemandWithoutSolarPlantProductionResult(annualWaterDemandKWH = 3000.0),
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 1000.0,
                    Season.SUMMER to 2000.0,
                    Season.AUTUMN to 500.0,
                    Season.WINTER to 100.0
                ),
                annualProducedEnergy = 3600.0
            )
        )
    }

})
