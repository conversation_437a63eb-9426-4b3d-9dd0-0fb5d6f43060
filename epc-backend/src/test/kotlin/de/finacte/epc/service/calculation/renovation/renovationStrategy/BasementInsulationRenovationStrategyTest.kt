package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year
import java.util.UUID

class BasementInsulationRenovationStrategyTest : StringSpec({
    val sut = BasementInsulationRenovationStrategy()

    "apply basement insulation renovation to BuildingCalculationInput with extruded polystyrene" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.BASEMENT,
            renovationMeasureType = RenovationMeasureType.BASEMENT_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureObject = "{\"basementExternalWallsInsulationType\": \"EXTRUDED_POLYSTYRENE\", \"basementExternalWallsInsulationThickness\": 10, \"basementFloorInsulationType\": \"EXTRUDED_POLYSTYRENE\", \"basementFloorInsulationThickness\": 10, \"basementInsulationYear\": 2025}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.basementInsulation?.basementExternalWallsInsulationType shouldBe BuildingInsulationType.EXTRUDED_POLYSTYRENE
        result.basementInsulation?.basementExternalWallsInsulationThickness shouldBe 10
        result.basementInsulation?.basementFloorInsulationType shouldBe BuildingInsulationType.EXTRUDED_POLYSTYRENE
        result.basementInsulation?.basementFloorInsulationThickness shouldBe 10
        result.basementInsulation?.basementInsulationYear shouldBe Year.now().value
    }

    "apply basement insulation renovation to BuildingCalculationInput with aerogel" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.BASEMENT,
            renovationMeasureType = RenovationMeasureType.BASEMENT_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.AEROGEL_1_CM_2025,
            renovationMeasureObject = "{\"basementExternalWallsInsulationType\": \"AEROGEL\", \"basementExternalWallsInsulationThickness\": 5, \"basementFloorInsulationType\": \"AEROGEL\", \"basementFloorInsulationThickness\": 5, \"basementInsulationYear\": 2025}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.basementInsulation?.basementExternalWallsInsulationType shouldBe BuildingInsulationType.AEROGEL
        result.basementInsulation?.basementExternalWallsInsulationThickness shouldBe 5
        result.basementInsulation?.basementFloorInsulationType shouldBe BuildingInsulationType.AEROGEL
        result.basementInsulation?.basementFloorInsulationThickness shouldBe 5
        result.basementInsulation?.basementInsulationYear shouldBe Year.now().value
    }
})
