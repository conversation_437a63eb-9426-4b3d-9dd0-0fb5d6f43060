package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.Season
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk

class V1SolarPlantProductionDeductElectricityDemandTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1SolarPlantProduction(systemEfficiencyRepositoryMock)

    "deduct solar plant energy from electricity demand" {
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 2000.0,
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 3600.0
        )
        val electricityDemand = 3000.0

        val result = sut.deductFromElectricityDemand(electricityDemand, solarPlantProduction)

        result shouldBe Pair(
            1543.8356164383563,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 546.3013698630136,
                    Season.SUMMER to 1546.3013698630136,
                    Season.AUTUMN to 51.23287671232873,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 2143.835616438356
            )
        )
    }

})
