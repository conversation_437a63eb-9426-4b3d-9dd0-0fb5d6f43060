package de.finacte.epc.service.calculation.result.intermediate.electricityDemand

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.data.headers
import io.kotest.data.row
import io.kotest.data.table
import io.kotest.matchers.shouldBe

class V1ElectricDemandTest : StringSpec({
    val sut = V1ElectricDemand(
        singleTenantHouseholdAvgConsumptionPerYearKWH = 1750.0,
        tenantAvgConsumptionPerYearKWH = 1000.0
    )

    "calculate electricity demand per tenant number" {
        io.kotest.data.forAll(
            table(
                headers("tenantsNo", "result"),
                row(0, 0.0),
                row(1, 1750.0),
                row(2, 2750.0),
                row(3, 3750.0),
                row(4, 4750.0),
                row(5, 5750.0),

                )
        ) { tenantsNo: Int, result: Double ->
            val input = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                tenants = tenantsNo
            )


            sut.calculate(input) shouldBe result
        }
    }
})
