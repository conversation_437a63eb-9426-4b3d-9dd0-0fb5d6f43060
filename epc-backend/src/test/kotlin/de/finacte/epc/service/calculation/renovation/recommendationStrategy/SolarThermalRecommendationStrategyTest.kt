package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.SolarThermalRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class SolarThermalRecommendationStrategyTest : StringSpec({
    val sut = SolarThermalRenovationRecommendationStrategy()


    "test recommendation strategy - solar thermal exist == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarThermal = BuildingCalculationInput.SolarThermal(
                solarThermalExist = true
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_THERMAL,
            renovationMeasureType = RenovationMeasureType.SOLAR_THERMAL,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_THERMAL_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - solar thermal not exist == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            solarThermal = BuildingCalculationInput.SolarThermal(
                solarThermalExist = false
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_THERMAL,
            renovationMeasureType = RenovationMeasureType.SOLAR_THERMAL,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_THERMAL_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

})
