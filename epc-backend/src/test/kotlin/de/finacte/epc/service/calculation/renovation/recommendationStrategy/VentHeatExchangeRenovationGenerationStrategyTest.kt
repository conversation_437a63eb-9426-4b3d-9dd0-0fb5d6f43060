package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.VentHeatExchangeRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class VentHeatExchangeRenovationGenerationStrategyTest : StringSpec({
    val sut = VentHeatExchangeRenovationRecommendationStrategy()


    "test recommendation strategy - vent heat exchange exist == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = true
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE,
            renovationMeasureValue = RenovationMeasureValue.HEAT_EXCHANGER,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - vent heat exchange not exist == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = false
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE,
            renovationMeasureValue = RenovationMeasureValue.HEAT_EXCHANGER,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

})
