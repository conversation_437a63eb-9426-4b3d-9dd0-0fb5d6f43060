package de.finacte.epc.service.calculation.buildingShape

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.*
import de.finacte.epc.entity.building.BuildingShape
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.roofFloorStrategies.RoofFloorCalculationImpl
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.doubles.plusOrMinus
import org.junit.jupiter.api.fail


class V1BuildingShapeFlatCompactStandaloneTest : StringSpec({
    val sut = BuildingShapeImpl(RoofFloorCalculationImpl())

    "calculate building shape without basement (status quo)" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 2,
                area = 100.0,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false,

                ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (250.0 plusOrMinus 0.1)
            roofFloorVolume shouldBe 0.0
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 100.00000000000001
            buildingThermalEnvelopeVolume shouldBe (250.0 plusOrMinus 0.1)
            buildingRoofSurfaceArea shouldBe (50.0 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (120.88 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (21.65 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (50.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (50.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate building shape with basement (not heated)" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 2,
                area = 100.0,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,
                //changed:
                basementExist = true,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false,
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (360.00 plusOrMinus 0.1)
            roofFloorVolume shouldBe 0.0
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 100.00000000000001
            buildingThermalEnvelopeVolume shouldBe (250.0 plusOrMinus 0.1)
            buildingRoofSurfaceArea shouldBe (50.0 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (120.88 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (21.65 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (50.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (50.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate building shape with basement (heated)" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 2,
                area = 100.0,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.COMPACT,
                position = BuildingPosition.STAND_ALONE,

                //changed:
                basementExist = true,
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = true,
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (360.0 plusOrMinus 0.1)
            roofFloorVolume shouldBe 0.0
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 150.00000000000003
            buildingThermalEnvelopeVolume shouldBe (360.00 plusOrMinus 0.1)
            buildingRoofSurfaceArea shouldBe (50.0 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (120.88 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (21.65 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (50.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS -> it.area shouldBe (63.50 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_FLOOR -> it.area shouldBe (50.0 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }



})