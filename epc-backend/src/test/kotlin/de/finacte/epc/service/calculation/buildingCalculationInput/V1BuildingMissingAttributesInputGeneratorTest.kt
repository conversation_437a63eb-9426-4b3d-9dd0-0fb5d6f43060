package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.createFullyInitializedBuildingEntity
import de.finacte.epc.entity.building.*
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.BuildingMissingAttributesInputGPTGeneratorImpl
import de.finacte.epc.service.gpt.GPTChat
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import org.springframework.ai.chat.messages.MessageType
import java.util.*

class V1BuildingMissingAttributesInputGeneratorTest : StringSpec({
    val gptChatMock = mockk<GPTChat>()
    val sut = BuildingMissingAttributesInputGPTGeneratorImpl(gptChatMock)


    "generate user message with fully initialized building dto" {
        val input = createFullyInitializedBuildingEntity()

        val result = sut.generateUserMessage(input)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
                The Building is located in Germany 
                The Zipcode of the building is 20146 
                Basic building information: 
                - building was built in 1990 
                - building dwelling type: Compact 
                Roof: 
                - type: Flat
                - roof insulation year 2018
                Windows: 
                - window glazing type: double
                - renewed in: 2016
                - shutters: Manual
                External walls:
                - wall insulation year 2010

        """.trimIndent()
    }

    "generate user message without roof insulation year" {
        val input = createFullyInitializedBuildingEntity().copy(
            roof = BuildingRoofEntity(
                id = UUID.randomUUID(),
                floor = BuildingRoofFloor.FLAT_ROOF,
                hasSolarPlant = false,
                eligibleForSolar = true,
                ceilingOrRoofInsulated = true,
                insulationYear = null,
                solarPlantInstallationYear = null,
                solarPlantPower = 15.5,
                building = mockk<BuildingEntity>()
            )
        )

        val result = sut.generateUserMessage(input)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
                The Building is located in Germany 
                The Zipcode of the building is 20146 
                Basic building information: 
                - building was built in 1990 
                - building dwelling type: Compact 
                Roof: 
                - type: Flat
                - roof insulation year 1990
                Windows: 
                - window glazing type: double
                - renewed in: 2016
                - shutters: Manual
                External walls:
                - wall insulation year 2010

        """.trimIndent()
    }

    "generate user message without facade insulation" {
        val input = createFullyInitializedBuildingEntity().copy(
            facade = BuildingFacadeEntity(
                id = UUID.randomUUID(),
                insulated = false,
                insulationYear = null,
                building = mockk<BuildingEntity>()
            ),
        )

        val result = sut.generateUserMessage(input)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
                The Building is located in Germany 
                The Zipcode of the building is 20146 
                Basic building information: 
                - building was built in 1990 
                - building dwelling type: Compact 
                Roof: 
                - type: Flat
                - roof insulation year 2018
                Windows: 
                - window glazing type: double
                - renewed in: 2016
                - shutters: Manual

        """.trimIndent()
    }

    "generate user message without facade insulation year" {
        val input = createFullyInitializedBuildingEntity().copy(
            facade = BuildingFacadeEntity(
                id = UUID.randomUUID(),
                insulated = true,
                insulationYear = null,
                building = mockk<BuildingEntity>()
            ),
        )

        val result = sut.generateUserMessage(input)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
                The Building is located in Germany 
                The Zipcode of the building is 20146 
                Basic building information: 
                - building was built in 1990 
                - building dwelling type: Compact 
                Roof: 
                - type: Flat
                - roof insulation year 2018
                Windows: 
                - window glazing type: double
                - renewed in: 2016
                - shutters: Manual
                External walls:
                - wall insulation year 1990

        """.trimIndent()
    }

    "generate user message without windows installation year" {
        val input = createFullyInitializedBuildingEntity().copy(
            windows = BuildingWindowsEntity(
                id = UUID.randomUUID(),
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = BuildingWindowsShutters.MANUAL,
                installationYear = null,
                building = mockk<BuildingEntity>()
            )
        )

        val result = sut.generateUserMessage(input)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
                The Building is located in Germany 
                The Zipcode of the building is 20146 
                Basic building information: 
                - building was built in 1990 
                - building dwelling type: Compact 
                Roof: 
                - type: Flat
                - roof insulation year 2018
                Windows: 
                - window glazing type: double
                - production year: 1990
                - shutters: Manual
                External walls:
                - wall insulation year 2010

        """.trimIndent()
    }

    "generate user message without windows shutters" {
        val input = createFullyInitializedBuildingEntity().copy(
            windows = BuildingWindowsEntity(
                id = UUID.randomUUID(),
                windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
                glazing = BuildingWindowsGlazing.DOUBLE,
                shutters = null,
                installationYear = 2016,
                building = mockk<BuildingEntity>()
            )
        )

        val result = sut.generateUserMessage(input)

        result.messageType shouldBe MessageType.USER
        result.text shouldBe """
                The Building is located in Germany 
                The Zipcode of the building is 20146 
                Basic building information: 
                - building was built in 1990 
                - building dwelling type: Compact 
                Roof: 
                - type: Flat
                - roof insulation year 2018
                Windows: 
                - window glazing type: double
                - renewed in: 2016
                External walls:
                - wall insulation year 2010

        """.trimIndent()
    }

    "generate system message with fully initialized building dto" {
        val input = createFullyInitializedBuildingEntity()

        val result = sut.generateSystemMessage(input)

        result.messageType shouldBe MessageType.SYSTEM
        result.text shouldBe """
            User will provide information about the building, 
            estimate additional information for this building.
            Do not return insulation thickness more than 10 cm.
            Base insulation info on the building’s construction year or insulation date if given.Even if renovated, choose renovation options suitable for the original construction type,as some of the modern methods may not fit older buildings.
        """.trimIndent()
    }
})
