package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.SolarPanelsBatteryType
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.Season
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import java.time.Year

class V1SolarPanelsProductionDeductElectricityDemandTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1SolarPanelsProduction(systemEfficiencyRepositoryMock)

    "deduct solar plant energy from electricity demand with NONE battery" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
            .copy(
                solarPanels = createFullyInitializedBuildingCalculationInput().solarPanels.copy(
                    solarPanelsExist = true,
                    solarPanelsNominalPower = null,
                    installationYear = Year.now().value
                )
            )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 2000.0,
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 3600.0
        )
        val electricityDemand = 3000.0

        val result = sut.deductFromElectricityDemand(electricityDemand, solarPlantProduction, buildingCalculationInput)

        result shouldBe Pair(
            1543.8356164383563,
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 546.3013698630136,
                    Season.SUMMER to 1546.3013698630136,
                    Season.AUTUMN to 51.23287671232873,
                    Season.WINTER to 0.0
                ),
                annualProducedEnergy = 2143.835616438356
            )
        )
    }

    for (batteryType in listOf(SolarPanelsBatteryType.LITHIUM_ION)) {
        "deduct solar plant energy from electricity demand with $batteryType battery" {
            val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
                .copy(
                    solarPanels = BuildingCalculationInput.SolarPanels(
                        solarPanelsExist = true,
                        solarPanelsNominalPower = null,
                        installationYear = Year.now().value
                    ),
                    solarPanelsBattery = BuildingCalculationInput.SolarPanelsBattery(
                        batteryType = batteryType
                    )
                )
            val solarPlantProduction = SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 1000.0,
                    Season.SUMMER to 2000.0,
                    Season.AUTUMN to 500.0,
                    Season.WINTER to 100.0
                ),
                annualProducedEnergy = 3600.0
            )
            val electricityDemand = 3000.0

            val result =
                sut.deductFromElectricityDemand(electricityDemand, solarPlantProduction, buildingCalculationInput)

            result shouldBe Pair(
                1190.13698630137,
                SolarPlantProductionResult(
                    seasonalProducedEnergy = mapOf(
                        Season.SPRING to 395.06849315068484,
                        Season.SUMMER to 1395.0684931506848,
                        Season.AUTUMN to 0.0,
                        Season.WINTER to 0.0
                    ),
                    annualProducedEnergy = 1790.1369863013697
                )
            )
        }
    }

})
