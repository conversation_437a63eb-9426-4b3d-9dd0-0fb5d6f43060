package de.finacte.epc.service.calculation.renovation.recommendationStrategy.roofCoverage

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.*

class RoofCoverageFlatRoofAdvancedRenovationRecommendationStrategyTest : StringSpec({
    val sut =
        RoofCoverageFlatRoofAdvancedRenovationRecommendationStrategy()

    "test method should return applicability=true for building after 1949 with flat roof" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                roofFloor = BuildingRoofFloor.FLAT_ROOF,
                constructionYear = 1990
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.HIGH_QUALITY_PLASTIC_FOIL,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Flat roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe true // isApplicable should be true
    }

    "test method should return applicability=true for building before 1949 with flat roof" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                roofFloor = BuildingRoofFloor.FLAT_ROOF,
                constructionYear = 1900
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.HIGH_QUALITY_PLASTIC_FOIL,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Flat roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe true // isApplicable should be true
    }

    "test method should return applicability=false for building with non-flat roof" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.HIGH_QUALITY_PLASTIC_FOIL,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Flat roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }

    "test method should return applicability=false for non-matching renovation template" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                roofFloor = BuildingRoofFloor.FLAT_ROOF
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.BITUMEN_MEMBRANE, // Different value than expected
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Flat roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }
})
