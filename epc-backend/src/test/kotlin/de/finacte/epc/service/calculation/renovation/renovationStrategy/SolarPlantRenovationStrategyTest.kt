package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.UUID

class SolarPlantRenovationStrategyTest : StringSpec({
    val sut = SolarPlantRenovationStrategy()

    "apply renovation to BuildingCalculationInput " {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.SOLAR_PLANT,
            renovationMeasureType = RenovationMeasureType.SOLAR_PLANT_PANELS,
            renovationMeasureValue = RenovationMeasureValue.SOLAR_PANELS_2025,
            renovationMeasureObject = "{\"solarPlantExist\": true}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.solarPlant.solarPlantExist shouldBe  true
    }

})
