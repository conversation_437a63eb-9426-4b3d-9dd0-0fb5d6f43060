package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.Season
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk

class V1SolarPlantProductionDeductCoolingEnergyDemandTest : StringSpec({
    val systemEfficiencyRepositoryMock = mockk<SystemEfficiencyRepository>()
    val sut = V1SolarPanelsProduction(systemEfficiencyRepositoryMock)

    "deduct solar plant energy from cooling energy demand when AC is installed - more production than consumption" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                acInstalled = true
            )
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 300.0, //important
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 1900.0
        )
        val finalCoolingEnergyDemand = 200.0

        val result = sut.deductFromCoolingEnergyDemand(
            finalCoolingEnergyDemand,
            solarPlantProduction,
            buildingCalculationInput
        )

        result shouldBe Pair(
            0.0,//important
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 1000.0,
                    Season.SUMMER to 100.0,//important
                    Season.AUTUMN to 500.0,
                    Season.WINTER to 100.0
                ),
                annualProducedEnergy = 1700.0
            )
        )
    }

    "deduct solar plant energy from cooling energy demand when AC is installed - less production than consumption" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                acInstalled = true
            )
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 100.0,//important
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 1700.0
        )
        val finalCoolingEnergyDemand = 200.0

        val result = sut.deductFromCoolingEnergyDemand(
            finalCoolingEnergyDemand,
            solarPlantProduction,
            buildingCalculationInput
        )

        result shouldBe Pair(
            100.0,//important
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 1000.0,
                    Season.SUMMER to 0.0,//important
                    Season.AUTUMN to 500.0,
                    Season.WINTER to 100.0
                ),
                annualProducedEnergy = 1600.0
            )
        )
    }

    "deduct solar plant energy from cooling energy demand when AC is installed - equal production to consumption" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                acInstalled = true
            )
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 200.0,//important
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 1800.0
        )
        val finalCoolingEnergyDemand = 200.0

        val result = sut.deductFromCoolingEnergyDemand(
            finalCoolingEnergyDemand,
            solarPlantProduction,
            buildingCalculationInput
        )

        result shouldBe Pair(
            0.0,//important
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 1000.0,
                    Season.SUMMER to 0.0,//important
                    Season.AUTUMN to 500.0,
                    Season.WINTER to 100.0
                ),
                annualProducedEnergy = 1600.0
            )
        )
    }

    "not deduct solar plant energy from cooling energy demand when AC is not installed" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                acInstalled = false
            )
        )
        val solarPlantProduction = SolarPlantProductionResult(
            seasonalProducedEnergy = mapOf(
                Season.SPRING to 1000.0,
                Season.SUMMER to 100.0,//important
                Season.AUTUMN to 500.0,
                Season.WINTER to 100.0
            ),
            annualProducedEnergy = 1700.0
        )
        val finalCoolingEnergyDemand = 200.0

        val result = sut.deductFromCoolingEnergyDemand(
            finalCoolingEnergyDemand,
            solarPlantProduction,
            buildingCalculationInput
        )

        result shouldBe Pair(
            200.0,//important
            SolarPlantProductionResult(
                seasonalProducedEnergy = mapOf(
                    Season.SPRING to 1000.0,
                    Season.SUMMER to 100.0,//important
                    Season.AUTUMN to 500.0,
                    Season.WINTER to 100.0
                ),
                annualProducedEnergy = 1700.0
            )
        )
    }

})
