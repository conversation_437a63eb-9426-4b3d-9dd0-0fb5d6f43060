package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.createFullyInitializedBuildingEntity
import de.finacte.epc.createFullyInitializedBuildingMissingAttributesGeneratedEntity
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.*
import de.finacte.epc.mapper.BuildingMissingAttributesGeneratedMapper
import de.finacte.epc.repository.BuildingMissingAttributesGeneratedRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.BuildingMissingAttributesInputGPTGenerator
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.BuildingMissingAttributesInputStaticGenerator
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.util.*

class BuildingCalculationInputFactoryImplTest : StringSpec({
    val buildingMissingAttributesGeneratedMapper = mockk<BuildingMissingAttributesGeneratedMapper>()
    val buildingMissingAttributesGeneratedRepository = mockk<BuildingMissingAttributesGeneratedRepository>()
    val buildingMissingAttributesInputGPTGenerator = mockk<BuildingMissingAttributesInputGPTGenerator>()
    val buildingMissingAttributesInputStaticGenerator = mockk<BuildingMissingAttributesInputStaticGenerator>()

    val buildingMissingAttributesGeneratedEntity = createFullyInitializedBuildingMissingAttributesGeneratedEntity()

    every { buildingMissingAttributesGeneratedRepository.findByBuildingId(any()) } returns buildingMissingAttributesGeneratedEntity

    every { buildingMissingAttributesInputStaticGenerator.calculateGroundFloor(any()) } returns BuildingCalculationInput.GroundFloor(
        groundFloorThickness = 10,
        groundFloorType = BuildingScreedType.CEMENT,
        groundFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
        groundFloorFinishThickness = 2
    )

    every { buildingMissingAttributesInputStaticGenerator.calculateBasementFloor(any()) } returns BuildingCalculationInput.BasementFloor(
        basementFloorThickness = 10,
        basementFloorType = BuildingScreedType.CEMENT,
        basementFloorFinishType = BuildingFloorFinishType.CERAMIC_TILES,
        basementFloorFinishThickness = 2
    )

    val sut = BuildingCalculationInputFactoryImpl(
        buildingMissingAttributesGeneratedMapper,
        buildingMissingAttributesGeneratedRepository,
        buildingMissingAttributesInputGPTGenerator,
        buildingMissingAttributesInputStaticGenerator
    )

    "create building calculation input - all optional not exist" {
        val buildingEntity = createFullyInitializedBuildingEntity()

        val result = sut.createBuildingCalculationInput(buildingEntity)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!)
    }

    "create building calculation input - AC installed" {
        val buildingEntity = createFullyInitializedBuildingEntity().copy(
            electricalEquipment =
                listOf(
                    BuildingElectricalEquipmentEntity(
                        id = UUID.randomUUID(),
                        type = BuildingElectricalEquipment.AC,
                        installationYear = 2015,
                        building = mockk<BuildingEntity>()
                    )
                )
        )
        val result = sut.createBuildingCalculationInput(buildingEntity)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!).copy(
            fixedAttributes = expectedResult(buildingId = buildingEntity.id!!).fixedAttributes.copy(
                acInstalled = true
            )
        )
    }

    "create building calculation input - Vent heat exchange installed" {
        val buildingEntity = createFullyInitializedBuildingEntity().copy(
            electricalEquipment =
                listOf(
                    BuildingElectricalEquipmentEntity(
                        id = UUID.randomUUID(),
                        type = BuildingElectricalEquipment.VENTILATION_HEAT_EXCHANGE,
                        installationYear = 2015,
                        building = mockk<BuildingEntity>()
                    )
                )
        )
        val result = sut.createBuildingCalculationInput(buildingEntity)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!).copy(
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = true
            ),
        )
    }

    "create building calculation input - basement exist" {
        val buildingEntity = createFullyInitializedBuildingEntity().copy(
            basement = BuildingBasementEntity(
                id = UUID.randomUUID(),
                heated = false,
                insulated = true,
                insulationYear = 2020,
                building = mockk<BuildingEntity>()
            )
        )
        val result = sut.createBuildingCalculationInput(buildingEntity)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!).copy(
            fixedAttributes = expectedResult(buildingId = buildingEntity.id!!).fixedAttributes.copy(
                basementExist = true
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
            basementInsulation = BuildingCalculationInput.BasementInsulation(
                basementFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                basementFloorInsulationThickness = 10,
                basementExternalWallsInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                basementExternalWallsInsulationThickness = 10,
                basementInsulationYear = 2020
            ),
        )
    }

    "create building calculation input - floor heating exist" {
        val buildingEntity = createFullyInitializedBuildingEntity().copy(
            heating = BuildingHeatingEntity(
                id = UUID.randomUUID(),
                primaryHeating = BuildingHeating.GAS_BOILER,
                waterHeating = BuildingHeating.ELECTRIC,
                hasFloorHeating = true,
                hasSolarThermalPlant = false,
                primaryHeatingInstallationYear = 2005,
                building = mockk<BuildingEntity>()
            )
        )
        val result = sut.createBuildingCalculationInput(buildingEntity)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!).copy(
            surfaceHeating = BuildingCalculationInput.SurfaceHeating(
                deliverySystem = BuildingHeatingDeliverySystem.UNDERFLOOR_WET
            )
        )
    }
})

fun expectedResult(buildingId: UUID) =
    BuildingCalculationInput(
        fixedAttributes = BuildingCalculationInput.FixedAttributes(
            buildingId = buildingId,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            zipCode = "20146",
            constructionYear = 1990,
            area = 120.0,
            floors = 2,
            floorHeight = 2.5,
            tenants = 2,
            roofFloor = BuildingRoofFloor.FLAT_ROOF,
            facadeWallType = BuildingWallType.BRICK,
            facadeWallThickness = 30,
            windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
            basementExist = true,
            basementExternalWallsType = BuildingWallType.BRICK,
            ceilingWallType = BuildingWallType.BRICK,
            ceilingWallThickness = 20,
            acInstalled = false,
        ),
        heating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.GAS,
            hotWaterEnergySource = SystemEfficiencyType.ELECTRICITY,
            heatingInstallationYear = 2005,
            heatingInstallationModernized = true,
        ),
        roofMaterial = BuildingCalculationInput.RoofMaterial(
            roofMaterial = BuildingRoofMaterial.CLAY_TILES
        ),
        roofInsulation = BuildingCalculationInput.RoofInsulation(
            roofInsulationRenewed = false,
            roofInsulationYear = 2018,
            roofInsulationThickness = 10,
            roofInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION
        ),
        basementHeating = BuildingCalculationInput.BasementHeating(
            basementHeated = false
        ),
        basementInsulation = BuildingCalculationInput.BasementInsulation(
            basementFloorInsulationThickness = 10,
            basementFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            basementExternalWallsInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            basementExternalWallsInsulationThickness = 10,
            basementInsulationYear = 1990
        ),
        ceilingWallInsulation = BuildingCalculationInput.CeilingWallInsulation(
            ceilingWallInsulationThickness = 10,
            ceilingWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION
        ),
        entranceDoor = BuildingCalculationInput.EntranceDoor(
            entranceDoorMaterial = BuildingDoorMaterial.WOOD
        ),
        groundFloorInsulation = BuildingCalculationInput.GroundFloorInsulation(
            groundFloorInsulationThickness = 10,
            groundFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            groundFloorInsulationYear = 1990
        ),
        solarThermal = BuildingCalculationInput.SolarThermal(
            solarThermalExist = false,
        ),
        solarPanels = BuildingCalculationInput.SolarPanels(
            solarPanelsExist = false,
            solarPanelsNominalPower = 15.5,
            installationYear = 1990,
        ),
        facadeWallInsulation = BuildingCalculationInput.FacadeWallInsulation(
            facadeWallInsulationYear = 2010,
            facadeWallInsulationRenewed = false,
            facadeWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            facadeWallInsulationThickness = 10
        ),
        windowsGlazing = BuildingCalculationInput.WindowsGlazing(
            windowsGlazing = BuildingWindowsGlazing.DOUBLE,
            windowsRenewed = false,
            windowsInstallationYear = 2016,
        ),
        windowsFrame = BuildingCalculationInput.WindowsFrame(
            windowsFrameMaterial = BuildingWindowFrameMaterial.PVC
        ),
        ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
            ventHeatExchangeInstalled = false,
        ),
        windowsShutters = BuildingCalculationInput.WindowsShutters(
            windowsShutters = BuildingWindowsShutters.MANUAL
        ),
        windowsCoating = BuildingCalculationInput.WindowsCoating(
            windowsCoatingType = BuildingWindowCoatingType.LOW_E_COATING,
        ),
        surfaceHeating = null,
        heatingRadiators = BuildingCalculationInput.HeatingRadiators(
            radiatorType = BuildingHeatingRadiatorType.STANDARD
        ),
        intelligentHeatingControls = BuildingCalculationInput.IntelligentHeatingControls(
            installed = false
        ),
        lightingAutomation = BuildingCalculationInput.LightingAutomation(
            installed = false
        ),
        solarPanelsBattery = null,
        solarPanelsImmersionHeater = BuildingCalculationInput.SolarPanelsImmersionHeater(
            installed = false
        ),
        groundFloor = BuildingCalculationInput.GroundFloor(
            groundFloorThickness = 10,
            groundFloorType = BuildingScreedType.CEMENT,
            groundFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
            groundFloorFinishThickness = 2,
        ),
        basementFloor = BuildingCalculationInput.BasementFloor(
            basementFloorThickness = 10,
            basementFloorType = BuildingScreedType.CEMENT,
            basementFloorFinishType = BuildingFloorFinishType.CERAMIC_TILES,
            basementFloorFinishThickness = 2,
        ),
    )

