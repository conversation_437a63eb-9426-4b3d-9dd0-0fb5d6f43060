package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.createFullyInitializedBuildingEntity
import de.finacte.epc.createFullyInitializedBuildingMissingAttributesGenerated
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import java.util.*

class BuildingCalculationInputFactoryImplTest : StringSpec({
    val sut = BuildingCalculationInputFactoryImpl()

    "create building calculation input - all optional not exist" {
        val buildingEntity = createFullyInitializedBuildingEntity()
        val buildingMissingAttributesGenerated = createFullyInitializedBuildingMissingAttributesGenerated()

        val result = sut.createBuildingCalculationInput(buildingEntity, buildingMissingAttributesGenerated)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!)
    }

    "create building calculation input - AC installed" {
        val buildingEntity = createFullyInitializedBuildingEntity().copy(
            electricalEquipment =
                listOf(
                    BuildingElectricalEquipmentEntity(
                        id = UUID.randomUUID(),
                        type = BuildingElectricalEquipment.AC,
                        installationYear = 2015,
                        building = mockk<BuildingEntity>()
                    )
                )
        )
        val buildingMissingAttributesGenerated = createFullyInitializedBuildingMissingAttributesGenerated()

        val result = sut.createBuildingCalculationInput(buildingEntity, buildingMissingAttributesGenerated)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!).copy(
            fixedAttributes = expectedResult(buildingId = buildingEntity.id).fixedAttributes.copy(
                acInstalled = true
            )
        )
    }

    "create building calculation input - Vent heat exchange installed" {
        val buildingEntity = createFullyInitializedBuildingEntity().copy(
            electricalEquipment =
                listOf(
                    BuildingElectricalEquipmentEntity(
                        id = UUID.randomUUID(),
                        type = BuildingElectricalEquipment.VENTILATION_HEAT_EXCHANGE,
                        installationYear = 2015,
                        building = mockk<BuildingEntity>()
                    )
                )
        )
        val buildingMissingAttributesGenerated = createFullyInitializedBuildingMissingAttributesGenerated()

        val result = sut.createBuildingCalculationInput(buildingEntity, buildingMissingAttributesGenerated)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!).copy(
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = true
            ),
        )
    }

    "create building calculation input - basement exist" {
        val buildingEntity = createFullyInitializedBuildingEntity().copy(
            basement = BuildingBasementEntity(
                id = UUID.randomUUID(),
                heated = false,
                insulated = true,
                insulationYear = 2020,
                building = mockk<BuildingEntity>()
            )
        )
        val buildingMissingAttributesGenerated = createFullyInitializedBuildingMissingAttributesGenerated()

        val result = sut.createBuildingCalculationInput(buildingEntity, buildingMissingAttributesGenerated)

        result shouldBe expectedResult(buildingId = buildingEntity.id!!).copy(
            fixedAttributes = expectedResult(buildingId = buildingEntity.id).fixedAttributes.copy(
                basementExist = true
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
            basementInsulation = BuildingCalculationInput.BasementInsulation(
                basementFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                basementFloorInsulationThickness = 10,
                basementExternalWallsInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                basementExternalWallsInsulationThickness = 10,
                basementInsulationYear = 2020
            ),
        )
    }
})

fun expectedResult(buildingId: UUID) =
    BuildingCalculationInput(
        fixedAttributes = BuildingCalculationInput.FixedAttributes(
            buildingId = buildingId,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            zipCode = "20146",
            constructionYear = 1990,
            area = 120.0,
            floors = 2,
            floorHeight = 2.5,
            tenants = 2,
            roofFloor = BuildingRoofFloor.FLAT_ROOF,
            facadeWallType = BuildingWallType.BRICK,
            facadeWallThickness = 30,
            windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
            basementFloorType = BuildingWallType.CONCRETE,
            basementFloorThickness = 10,
            basementExternalWallsType = BuildingWallType.BRICK,
            basementExist = true,
            groundFloorType = BuildingWallType.BRICK,
            groundFloorThickness = 10,
            ceilingWallType = BuildingWallType.BRICK,
            ceilingWallThickness = 20,
            acInstalled = false,
        ),
        heating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.GAS,
            hotWaterEnergySource = SystemEfficiencyType.ELECTRICITY,
            heatingInstallationYear = 2005,
            heatingInstallationModernized = true,
        ),
        roofMaterial = BuildingCalculationInput.RoofMaterial(
            roofMaterial = BuildingRoofMaterial.CLAY_TILES
        ),
        roofInsulation = BuildingCalculationInput.RoofInsulation(
            roofInsulationRenewed = false,
            roofInsulationYear = 2018,
            roofInsulationThickness = 10,
            roofInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION
        ),
        basementHeating = BuildingCalculationInput.BasementHeating(
            basementHeated = false
        ),
        basementInsulation = BuildingCalculationInput.BasementInsulation(
            basementFloorInsulationThickness = 10,
            basementFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            basementExternalWallsInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            basementExternalWallsInsulationThickness = 10,
            basementInsulationYear = 1990
        ),
        ceilingWallInsulation = BuildingCalculationInput.CeilingWallInsulation(
            ceilingWallInsulationThickness = 10,
            ceilingWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION
        ),
        entranceDoor = BuildingCalculationInput.EntranceDoor(
            entranceDoorMaterial = BuildingDoorMaterial.WOOD
        ),
        groundFloorInsulation = BuildingCalculationInput.GroundFloorInsulation(
            groundFloorInsulationThickness = 10,
            groundFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            groundFloorInsulationYear = 1990
        ),
        solarThermal = BuildingCalculationInput.SolarThermal(
            solarThermalExist = false,
        ),
        solarPlant = BuildingCalculationInput.SolarPlant(
            solarPlantExist = false,
            solarPlantNominalPower = 15.5,
            solarPlantInstallationYear = 1990
        ),
        facadeWallInsulation = BuildingCalculationInput.FacadeWallInsulation(
            facadeWallInsulationYear = 2010,
            facadeWallInsulationRenewed = false,
            facadeWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            facadeWallInsulationThickness = 10
        ),
        windowsGlazing = BuildingCalculationInput.WindowsGlazing(
            windowsGlazing = BuildingWindowsGlazing.DOUBLE,
            windowsRenewed = false,
            windowsInstallationYear = 2016,
        ),
        windowsFrame = BuildingCalculationInput.WindowsFrame(
            windowsFrameMaterial = BuildingWindowFrameMaterial.PVC
        ),
        ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
            ventHeatExchangeInstalled = false,
        ),
        windowsShutters = BuildingCalculationInput.WindowsShutters(
            windowsShutters = BuildingWindowsShutters.MANUAL
        ),
        windowsCoating = BuildingCalculationInput.WindowsCoating(
            windowsCoatingType = BuildingWindowCoatingType.LOW_E_COATING,
        )
    )

