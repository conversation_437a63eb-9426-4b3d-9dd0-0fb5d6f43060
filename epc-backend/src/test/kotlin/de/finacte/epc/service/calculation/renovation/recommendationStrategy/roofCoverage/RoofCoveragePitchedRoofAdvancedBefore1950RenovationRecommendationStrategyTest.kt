package de.finacte.epc.service.calculation.renovation.recommendationStrategy.roofCoverage

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.*

class RoofCoveragePitchedRoofAdvancedBefore1950RenovationRecommendationStrategyTest : StringSpec({
    val sut =
        RoofCoveragePitchedRoofAdvancedBefore1950RenovationRecommendationStrategy()

    "test method should return applicability=true for building before 1950 with non-flat roof" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1949,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.NATURAL_SLATE,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe true // isApplicable should be true
    }

    "test method should return applicability=false for building after 1949" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1950,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.NATURAL_SLATE,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }

    "test method should return applicability=false for building with flat roof" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1949,
                roofFloor = BuildingRoofFloor.FLAT_ROOF
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.NATURAL_SLATE,
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }

    "test method should return applicability=false for non-matching renovation template" {
        // Given
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                constructionYear = 1949,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED
            )
        )
        val renovationTemplate = RenovationTemplateEntity(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_NEW_COVERAGE,
            renovationMeasureValue = RenovationMeasureValue.CLAY_TILES, // Different value than expected
            renovationMeasureVariant = RenovationMeasureVariant.ADVANCED,
            renovationMeasureObject = "{}",
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Roof coverage renovation",
            listOrder = 1
        )

        // When
        val result = sut.test(buildingCalculationInput, renovationTemplate)

        // Then
        result.first shouldBe false // isApplicable should be false
    }
})
