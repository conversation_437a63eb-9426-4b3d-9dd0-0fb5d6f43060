package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year
import java.util.UUID

class RoofInsulationRenovationStrategyTest : StringSpec({
    val sut = RoofInsulationRenovationStrategy()

    "apply renovation to BuildingCalculationInput with fiberglass insulation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.FIBERGLASS_INSULATION_30_CM_2025,
            renovationMeasureObject = "{ \"roofInsulationRenewed\": true, \"roofInsulationYear\": 2025, \"roofInsulationThickness\": 30, \"roofInsulationType\": \"FIBERGLASS_INSULATION\"}",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.roofInsulation?.roofInsulationType shouldBe BuildingInsulationType.FIBERGLASS_INSULATION
        result.roofInsulation?.roofInsulationThickness shouldBe 30
        result.roofInsulation?.roofInsulationRenewed shouldBe true
        result.roofInsulation?.roofInsulationYear shouldBe 2025
    }

    "apply renovation to BuildingCalculationInput with vacuum insulated panels" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.VACUUM_INSULATED_PANEL_5_CM_2025,
            renovationMeasureObject = "{ \"roofInsulationRenewed\": true, \"roofInsulationYear\": 2025, \"roofInsulationThickness\": 5, \"roofInsulationType\": \"VACUUM_INSULATED_PANEL\"}",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.roofInsulation?.roofInsulationType shouldBe BuildingInsulationType.VACUUM_INSULATED_PANEL
        result.roofInsulation?.roofInsulationThickness shouldBe 5
        result.roofInsulation?.roofInsulationRenewed shouldBe true
        result.roofInsulation?.roofInsulationYear shouldBe 2025
    }

    "return original BuildingCalculationInput when JSON parsing fails" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.ROOF,
            renovationMeasureType = RenovationMeasureType.ROOF_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.VACUUM_INSULATED_PANEL_5_CM_2025,
            renovationMeasureObject = "{\"invalid\": json", // Invalid JSON
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result shouldBe buildingCalculationInput
    }
})
