package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.GroundFloorInsulationRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class GroundFloorInsulationRenovationRecommendationStrategyTest : StringSpec({
    val sut = GroundFloorInsulationRenovationRecommendationStrategy()


    "test recommendation strategy - basement not heated == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.BASEMENT,
            renovationMeasureType = RenovationMeasureType.GROUND_FLOOR_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - basement heated == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = true
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.BASEMENT,
            renovationMeasureType = RenovationMeasureType.GROUND_FLOOR_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - basement not exist == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                basementExist = false
            )
        )
        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.BASEMENT,
            renovationMeasureType = RenovationMeasureType.GROUND_FLOOR_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
            renovationMeasureObject = "", //not relevant
            listOrder = 1, //not relevant
            renovationTemplateTranslations = emptyList(),
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Renovation measure", //not relevant
            renovationMeasureVersion = 1 //not relevant
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

})
