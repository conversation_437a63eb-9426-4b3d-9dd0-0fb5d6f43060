package de.finacte.epc.service.calculation.buildingShape

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.*
import de.finacte.epc.entity.building.BuildingShape
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.roofFloorStrategies.RoofFloorCalculationImpl
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.doubles.plusOrMinus
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.fail

class V1BuildingShapePitchedLShapeStandaloneTest : StringSpec({
    val sut = BuildingShapeImpl(RoofFloorCalculationImpl())

    "calculate L_SHAPED building shape with FULLY_LIVEABLE_REDUCED roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.L_SHAPE,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (260.585 plusOrMinus 1.0)
            roofFloorVolume shouldBe 123.74889349857239
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 109.4696037003227
            buildingThermalEnvelopeVolume shouldBe (252.473 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (90.18 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (127.99 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (22.59 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (41.51 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (22.96 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (54.77 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate L_SHAPED building shape with FULLY_LIVEABLE_ELEVATED roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 180.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.L_SHAPE,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (504.351 plusOrMinus 1.0)
            roofFloorVolume shouldBe 279.3516984372388
            buildingArea shouldBe 180.0
            buildingHeatedArea shouldBe 180.00000000000006
            buildingThermalEnvelopeVolume shouldBe (437.74 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (141.43 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (184.92 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (32.63 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (21.29 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (73.69 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (90.00 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate L_SHAPED building shape with COLD_STORAGE roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.COLD_STORAGE,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.L_SHAPE,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (366.362 plusOrMinus 1.0)
            roofFloorVolume shouldBe 116.36209498809836
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 100.0
            buildingThermalEnvelopeVolume shouldBe (250.0 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (155.78 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (98.39 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (17.68 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (100.00 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (100.00 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate L_SHAPED building shape with PARTIALLY_LIVEABLE roof floor - NO BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 180.0,
                roofFloor = BuildingRoofFloor.PARTIALLY_LIVEABLE,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.L_SHAPE,
                position = BuildingPosition.STAND_ALONE,
                basementExist = false
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (606.350 plusOrMinus 1.0)
            roofFloorVolume shouldBe 290.99591445704436
            buildingArea shouldBe 180.0
            buildingHeatedArea shouldBe 204.09450967212553
            buildingThermalEnvelopeVolume shouldBe (455.24 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (193.10 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (164.58 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (29.04 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (88.22 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (10.35 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (126.20 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate L_SHAPED building shape with FULLY_LIVEABLE_REDUCED roof floor - COLD BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.L_SHAPE,
                position = BuildingPosition.STAND_ALONE,

                //changed:
                basementExist = true
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = false
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (381.0 plusOrMinus 1.0)
            roofFloorVolume shouldBe 123.74889349857239
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 109.4696037003227
            buildingThermalEnvelopeVolume shouldBe (252.473 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (90.18 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (127.99 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (22.59 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (41.51 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (22.96 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.GROUND_FLOOR -> it.area shouldBe (54.77 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }

    "calculate L_SHAPED building shape with FULLY_LIVEABLE_REDUCED roof floor - HEATED BASEMENT" {
        val input = createFullyInitializedBuildingCalculationInput().copy(
            fixedAttributes = createFullyInitializedBuildingCalculationInput().fixedAttributes.copy(
                floorHeight = 2.5,
                floors = 1,
                area = 100.0,
                roofFloor = BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
                windowsToWallRatio = BuildingWindowsToWallRatio.LOW,
                shape = BuildingShape.L_SHAPE,
                position = BuildingPosition.STAND_ALONE,

                //changed:
                basementExist = true
            ),
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = true
            ),
        )

        sut.calculate(input).apply {
            buildingVolume shouldBe (381.0 plusOrMinus 1.0)
            roofFloorVolume shouldBe 123.74889349857239
            buildingArea shouldBe 100.0
            buildingHeatedArea shouldBe 164.20440555048404
            buildingThermalEnvelopeVolume shouldBe (372.72 plusOrMinus 1.0)
            buildingRoofSurfaceArea shouldBe (90.18 plusOrMinus 0.1)
            thermalEnvelopeElements.forEach {
                when (it.type) {
                    ThermalEnvelopeElementType.FACADE_WALLS -> it.area shouldBe (127.99 plusOrMinus 2.0)
                    ThermalEnvelopeElementType.WINDOWS -> it.area shouldBe (22.59 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.ROOF -> it.area shouldBe (41.51 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.CEILING -> it.area shouldBe (22.96 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS -> it.area shouldBe (76.74 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.BASEMENT_FLOOR -> it.area shouldBe (54.77 plusOrMinus 1.0)
                    ThermalEnvelopeElementType.DOOR -> it.area shouldBe 1.8
                    else -> fail("Unexpected Thermal Envelope Type: ${it.type}")
                }
            }
        }
    }
})