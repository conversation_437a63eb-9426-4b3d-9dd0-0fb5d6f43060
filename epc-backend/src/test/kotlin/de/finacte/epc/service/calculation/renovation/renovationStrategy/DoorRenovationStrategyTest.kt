package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingDoorMaterial
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.util.*

class DoorRenovationStrategyTest : StringSpec({
    val sut = DoorRenovationStrategy()

    "apply door replacement renovation to BuildingCalculationInput" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.DOOR,
            renovationMeasureType = RenovationMeasureType.DOORS_REPLACEMENT,
            renovationMeasureValue = RenovationMeasureValue.DEFAULT,
            renovationMeasureObject = "{\"entranceDoorMaterial\": \"COMPOSITE\"}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.entranceDoor.entranceDoorMaterial shouldBe BuildingDoorMaterial.COMPOSITE
    }
})
