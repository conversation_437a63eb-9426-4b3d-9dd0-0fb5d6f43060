package de.finacte.epc.service.calculation.renovation.recommendationStrategy.heatingSystem

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.renovation.*
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class VentHeatExchangeRenovationRecommendationStrategyTest : StringSpec({
    val sut =
        VentHeatExchangeRenovationRecommendationStrategy()

    "test recommendation strategy - vent heat exchange not installed == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = false
            )
        )

        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE,
            renovationMeasureValue = RenovationMeasureValue.HEAT_EXCHANGER,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Ventilation system with heat exchanger",
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - vent heat exchange already installed == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = true
            )
        )

        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE,
            renovationMeasureValue = RenovationMeasureValue.HEAT_EXCHANGER,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Ventilation system with heat exchanger",
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - wrong renovation template == not applicable" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()

        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE, // Different type
            renovationMeasureValue = RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Air to water heat pump installation (2025)",
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(false, false)
    }
})
