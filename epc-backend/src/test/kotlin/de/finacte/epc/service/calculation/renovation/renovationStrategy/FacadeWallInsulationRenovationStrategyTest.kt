package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year
import java.util.UUID

class FacadeWallInsulationRenovationStrategyTest : StringSpec({
    val sut = FacadeWallInsulationRenovationStrategy()

    "apply renovation to BuildingCalculationInput with MINERAL_WOOL" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.MINERAL_WOOL_10_CM_2025,
            renovationMeasureObject = "{\"facadeWallInsulationType\": \"MINERAL_WOOL\", \"facadeWallInsulationYear\": 2025, \"facadeWallInsulationRenewed\": true, \"facadeWallInsulationThickness\": 10}",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.facadeWallInsulation?.facadeWallInsulationType shouldBe BuildingInsulationType.MINERAL_WOOL
        result.facadeWallInsulation?.facadeWallInsulationThickness shouldBe 10
        result.facadeWallInsulation?.facadeWallInsulationRenewed shouldBe true
        result.facadeWallInsulation?.facadeWallInsulationYear shouldBe Year.now().value
    }

    "apply renovation to BuildingCalculationInput with WOOD_FIBER" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.WOOD_FIBER_15_CM_2025,
            renovationMeasureObject = "{\"facadeWallInsulationType\": \"WOOD_FIBER\", \"facadeWallInsulationYear\": 2025, \"facadeWallInsulationRenewed\": true, \"facadeWallInsulationThickness\": 15}",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.facadeWallInsulation?.facadeWallInsulationType shouldBe BuildingInsulationType.WOOD_FIBER
        result.facadeWallInsulation?.facadeWallInsulationThickness shouldBe 15
        result.facadeWallInsulation?.facadeWallInsulationRenewed shouldBe true
        result.facadeWallInsulation?.facadeWallInsulationYear shouldBe Year.now().value
    }

    "apply renovation to BuildingCalculationInput with EXTRUDED_POLYSTYRENE" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            renovationMeasureObject = "{\"facadeWallInsulationType\": \"EXTRUDED_POLYSTYRENE\", \"facadeWallInsulationYear\": 2025, \"facadeWallInsulationRenewed\": true, \"facadeWallInsulationThickness\": 10}",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.facadeWallInsulation?.facadeWallInsulationType shouldBe BuildingInsulationType.EXTRUDED_POLYSTYRENE
        result.facadeWallInsulation?.facadeWallInsulationThickness shouldBe 10
        result.facadeWallInsulation?.facadeWallInsulationRenewed shouldBe true
        result.facadeWallInsulation?.facadeWallInsulationYear shouldBe Year.now().value
    }

    "apply renovation to BuildingCalculationInput with AEROGEL" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.AEROGEL_4_CM_2025,
            renovationMeasureObject = "{\"facadeWallInsulationType\": \"AEROGEL\", \"facadeWallInsulationYear\": 2025, \"facadeWallInsulationRenewed\": true, \"facadeWallInsulationThickness\": 1}",
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.facadeWallInsulation?.facadeWallInsulationType shouldBe BuildingInsulationType.AEROGEL
        result.facadeWallInsulation?.facadeWallInsulationThickness shouldBe 1
        result.facadeWallInsulation?.facadeWallInsulationRenewed shouldBe true
        result.facadeWallInsulation?.facadeWallInsulationYear shouldBe Year.now().value
    }

    "return original BuildingCalculationInput when JSON parsing fails" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.FACADE,
            renovationMeasureType = RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            renovationMeasureValue = RenovationMeasureValue.AEROGEL_4_CM_2025,
            renovationMeasureObject = "{\"invalid\": json", // Invalid JSON
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result shouldBe buildingCalculationInput
    }
})
