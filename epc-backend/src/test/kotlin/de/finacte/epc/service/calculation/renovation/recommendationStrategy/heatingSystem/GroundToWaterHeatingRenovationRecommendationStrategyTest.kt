package de.finacte.epc.service.calculation.renovation.recommendationStrategy.heatingSystem

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.renovation.*
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year

class GroundToWaterHeatingRenovationRecommendationStrategyTest : StringSpec({
    val sut =
        GroundToWaterHeatingRenovationRecommendationStrategy()

    "test recommendation strategy - heating system older than 10 years == recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType.ELECTRICITY,
                hotWaterEnergySource = SystemEfficiencyType.ELECTRICITY,
                heatingInstallationYear = Year.now().value - 11,
                heatingInstallationModernized = false
            )
        )

        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.GROUND_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Ground to water heat pump installation (2025)",
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, true)
    }

    "test recommendation strategy - heating system newer than 10 years == no recommendation" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput().copy(
            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType.ELECTRICITY,
                hotWaterEnergySource = SystemEfficiencyType.ELECTRICITY,
                heatingInstallationYear = Year.now().value - 5,
                heatingInstallationModernized = false
            )
        )

        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.GROUND_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Ground to water heat pump installation (2025)",
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(true, false)
    }

    "test recommendation strategy - wrong renovation template == not applicable" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()

        val renovationTemplateEntity = RenovationTemplateEntity(
            id = null,
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025, // Different value
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
            renovationMeasureObject = "",
            listOrder = 1,
            grantPrograms = emptyList(),
            renovationMeasureDescription = "Air to water heat pump installation (2025)",
        )

        val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

        result shouldBe Pair(false, false)
    }
})
