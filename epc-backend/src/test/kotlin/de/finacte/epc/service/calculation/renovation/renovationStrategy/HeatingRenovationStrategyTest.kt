package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.time.Year
import java.util.UUID

class HeatingRenovationStrategyTest : StringSpec({
    val sut = HeatingRenovationStrategy()

    "apply renovation to BuildingCalculationInput - 1" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureObject = "{\"hotWaterEnergySource\" : \"HEAT_PUMP\", \"heatingEnergySource\": \"HEAT_PUMP\", \"heatingInstallationYear\": \"2025\"}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.heating.heatingEnergySource shouldBe SystemEfficiencyType.HEAT_PUMP
        result.heating.hotWaterEnergySource shouldBe SystemEfficiencyType.HEAT_PUMP
        result.heating.heatingInstallationYear shouldBe Year.now().value
    }

    "apply renovation to BuildingCalculationInput - 2" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.GROUND_TO_WATER_HEAT_PUMP_2025,
            renovationMeasureObject = "{\"hotWaterEnergySource\" : \"HEAT_PUMP\", \"heatingEnergySource\": \"HEAT_PUMP\", \"heatingInstallationYear\": \"2025\"}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.heating.heatingEnergySource shouldBe SystemEfficiencyType.HEAT_PUMP
        result.heating.hotWaterEnergySource shouldBe SystemEfficiencyType.HEAT_PUMP
        result.heating.heatingInstallationYear shouldBe Year.now().value
    }

    "apply renovation to BuildingCalculationInput - 3" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.DISTRICT_HEATING_2025,
            renovationMeasureObject = "{\"hotWaterEnergySource\" : \"DISTRICT\", \"heatingEnergySource\": \"DISTRICT\", \"heatingInstallationYear\": \"2025\"}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.heating.heatingEnergySource shouldBe SystemEfficiencyType.DISTRICT
        result.heating.hotWaterEnergySource shouldBe SystemEfficiencyType.DISTRICT
        result.heating.heatingInstallationYear shouldBe Year.now().value
    }

    "apply renovation to BuildingCalculationInput - 4" {
        val buildingCalculationInput = createFullyInitializedBuildingCalculationInput()
        val renovationTemplate = RenovationTemplateDto(
            id = UUID.randomUUID(),
            renovationMeasureCategory = RenovationMeasureCategory.HEATING_SYSTEM,
            renovationMeasureType = RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            renovationMeasureValue = RenovationMeasureValue.BIOMASS_HEATING_2025,
            renovationMeasureObject = "{\"hotWaterEnergySource\" : \"BIOMASS\", \"heatingEnergySource\": \"BIOMASS\", \"heatingInstallationYear\": \"2025\"}",
            renovationTemplateTranslations = emptySet(),
            renovationMeasureVariant = RenovationMeasureVariant.DEFAULT,
        )

        val result = sut.apply(buildingCalculationInput, renovationTemplate)

        result.heating.heatingEnergySource shouldBe SystemEfficiencyType.BIOMASS
        result.heating.hotWaterEnergySource shouldBe SystemEfficiencyType.BIOMASS
        result.heating.heatingInstallationYear shouldBe Year.now().value
    }
})
