package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.createFullyInitializedBuildingCalculationInput
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1.RoofInsulationRenovationRecommendationStrategy
import io.kotest.core.spec.style.StringSpec
import io.kotest.data.forAll
import io.kotest.data.row
import io.kotest.matchers.shouldBe
import java.time.Year

class RoofInsulationRenovationRecommendationStrategyTest : StringSpec({
    val sut = RoofInsulationRenovationRecommendationStrategy()


    "Testing roof insulation renovation strategy - insulation older than 5 years" {
        forAll(
            row(BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED, Pair(true, false)), // Roof takes a very small part of ceiling
            row(BuildingRoofFloor.FULLY_LIVEABLE_REDUCED, Pair(true, true)),
            row(BuildingRoofFloor.PARTIALLY_LIVEABLE, Pair(true, true)),
            row(BuildingRoofFloor.COLD_STORAGE, Pair(true, false)),
            row(BuildingRoofFloor.FLAT_ROOF, Pair(true, true)),
        ) { roofFloor, expectedResult ->
            val input = createFullyInitializedBuildingCalculationInput()
            val buildingCalculationInput = input.copy(
                fixedAttributes = input.fixedAttributes.copy(
                    roofFloor = roofFloor
                ),
                roofInsulation = BuildingCalculationInput.RoofInsulation(
                    roofInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                    roofInsulationThickness = 10,
                    roofInsulationRenewed = true,
                    roofInsulationYear = Year.now().value - 6
                )
            )
            val renovationTemplateEntity = RenovationTemplateEntity(
                id = null,
                renovationMeasureCategory = RenovationMeasureCategory.ROOF,
                renovationMeasureType = RenovationMeasureType.ROOF_INSULATION,
                renovationMeasureValue = RenovationMeasureValue.VACUUM_INSULATED_PANEL_30_CM_2025,
                renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
                renovationMeasureObject = "", //not relevant
                listOrder = 1, //not relevant
                renovationTemplateTranslations = emptyList(),
                grantPrograms = emptyList(),
                renovationMeasureDescription = "Renovation measure", //not relevant
                renovationMeasureVersion = 1 //not relevant
            )

            val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

            result shouldBe expectedResult
        }
    }

    "Testing roof insulation renovation strategy - insulation younger than 5 years (not recommended)" {
        forAll(
            row(BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED),
            row(BuildingRoofFloor.FULLY_LIVEABLE_REDUCED),
            row(BuildingRoofFloor.PARTIALLY_LIVEABLE),
            row(BuildingRoofFloor.COLD_STORAGE),
            row(BuildingRoofFloor.FLAT_ROOF),
        ) { roofFloor ->
            val input = createFullyInitializedBuildingCalculationInput()
            val buildingCalculationInput = input.copy(
                fixedAttributes = input.fixedAttributes.copy(
                    roofFloor = roofFloor
                ),
                roofInsulation = BuildingCalculationInput.RoofInsulation(
                    roofInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
                    roofInsulationThickness = 10,
                    roofInsulationRenewed = true,
                    roofInsulationYear = Year.now().value - 3
                )
            )
            val renovationTemplateEntity = RenovationTemplateEntity(
                id = null,
                renovationMeasureCategory = RenovationMeasureCategory.ROOF,
                renovationMeasureType = RenovationMeasureType.ROOF_INSULATION,
                renovationMeasureValue = RenovationMeasureValue.VACUUM_INSULATED_PANEL_30_CM_2025,
                renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
                renovationMeasureObject = "", //not relevant
                listOrder = 1, //not relevant
                renovationTemplateTranslations = emptyList(),
                grantPrograms = emptyList(),
                renovationMeasureDescription = "Renovation measure", //not relevant
                renovationMeasureVersion = 1 //not relevant
            )

            val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

            result shouldBe Pair(true, false)
        }
    }

    "Testing roof insulation renovation strategy - insulation not exist" {
        forAll(
            row(BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED, Pair(true, false)), // Roof takes a very small part of ceiling
            row(BuildingRoofFloor.FULLY_LIVEABLE_REDUCED, Pair(true, true)),
            row(BuildingRoofFloor.PARTIALLY_LIVEABLE, Pair(true, true)),
            row(BuildingRoofFloor.COLD_STORAGE, Pair(true, false)),
            row(BuildingRoofFloor.FLAT_ROOF, Pair(true, true)),
        ) { roofFloor, expectedResult ->
            val input = createFullyInitializedBuildingCalculationInput()
            val buildingCalculationInput = input.copy(
                fixedAttributes = input.fixedAttributes.copy(
                    roofFloor = roofFloor
                ),
                roofInsulation = null
            )
            val renovationTemplateEntity = RenovationTemplateEntity(
                id = null,
                renovationMeasureCategory = RenovationMeasureCategory.ROOF,
                renovationMeasureType = RenovationMeasureType.ROOF_INSULATION,
                renovationMeasureValue = RenovationMeasureValue.VACUUM_INSULATED_PANEL_30_CM_2025,
                renovationMeasureVariant = RenovationMeasureVariant.DEFAULT, //not relevant
                renovationMeasureObject = "", //not relevant
                listOrder = 1, //not relevant
                renovationTemplateTranslations = emptyList(),
                grantPrograms = emptyList(),
                renovationMeasureDescription = "Renovation measure",
                renovationMeasureVersion = 1 //not relevant
            )

            val result = sut.test(buildingCalculationInput, renovationTemplateEntity)

            result shouldBe expectedResult
        }
    }
})
