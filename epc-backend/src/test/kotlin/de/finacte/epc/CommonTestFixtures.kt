package de.finacte.epc

import de.finacte.epc.dto.building.*
import de.finacte.epc.dto.building.generated.BuildingMissingAttributesGenerated
import de.finacte.epc.dto.building.generated.insulation.*
import de.finacte.epc.dto.calculation.CalculationResultDto
import de.finacte.epc.entity.*
import de.finacte.epc.entity.building.*
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult
import io.mockk.mockk
import java.util.*

fun createFullyInitializedBuildingCalculationInput(buildingId: UUID = UUID.randomUUID()): BuildingCalculationInput =
    BuildingCalculationInput(
        fixedAttributes = BuildingCalculationInput.FixedAttributes(
            buildingId = buildingId,
            shape = BuildingShape.COMPACT,
            position = BuildingPosition.STAND_ALONE,
            zipCode = "20146",
            constructionYear = 1990,
            area = 100.0,
            floors = 2,
            floorHeight = 2.5,
            tenants = 2,
            roofFloor = BuildingRoofFloor.FLAT_ROOF,
            facadeWallType = BuildingWallType.BRICK,
            facadeWallThickness = 30,
            windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
            basementExist = true,
            basementExternalWallsType = BuildingWallType.BRICK,
            ceilingWallType = BuildingWallType.BRICK,
            ceilingWallThickness = 20,
            acInstalled = true,
        ),
        heating = BuildingCalculationInput.Heating(
            heatingEnergySource = SystemEfficiencyType.GAS,
            hotWaterEnergySource = SystemEfficiencyType.GAS,
            heatingInstallationYear = 1990,
            heatingInstallationModernized = false,
        ),
        roofMaterial = BuildingCalculationInput.RoofMaterial(
            roofMaterial = BuildingRoofMaterial.CLAY_TILES
        ),
        roofInsulation = BuildingCalculationInput.RoofInsulation(
            roofInsulationRenewed = false,
            roofInsulationYear = 1990,
            roofInsulationThickness = 10,
            roofInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION
        ),
        basementHeating = BuildingCalculationInput.BasementHeating(
            basementHeated = true
        ),
        basementInsulation = BuildingCalculationInput.BasementInsulation(
            basementFloorInsulationThickness = 10,
            basementFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            basementInsulationYear = 1990,
            basementExternalWallsInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            basementExternalWallsInsulationThickness = 10,
        ),
        ceilingWallInsulation = BuildingCalculationInput.CeilingWallInsulation(
            ceilingWallInsulationThickness = 10,
            ceilingWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
        ),
        entranceDoor = BuildingCalculationInput.EntranceDoor(
            entranceDoorMaterial = BuildingDoorMaterial.WOOD
        ),
        groundFloorInsulation = BuildingCalculationInput.GroundFloorInsulation(
            groundFloorInsulationThickness = 10,
            groundFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            groundFloorInsulationYear = 1990,
        ),
        solarThermal = BuildingCalculationInput.SolarThermal(
            solarThermalExist = false
        ),
        solarPanels = BuildingCalculationInput.SolarPanels(
            solarPanelsExist = false,
            solarPanelsNominalPower = null,
            installationYear = 1990,
        ),
        facadeWallInsulation = BuildingCalculationInput.FacadeWallInsulation(
            facadeWallInsulationYear = 1990,
            facadeWallInsulationRenewed = false,
            facadeWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
            facadeWallInsulationThickness = 10
        ),
        windowsGlazing = BuildingCalculationInput.WindowsGlazing(
            windowsGlazing = BuildingWindowsGlazing.DOUBLE,
            windowsRenewed = false,
            windowsInstallationYear = 1990,
        ),
        windowsFrame = BuildingCalculationInput.WindowsFrame(
            windowsFrameMaterial = BuildingWindowFrameMaterial.PVC
        ),
        ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
            ventHeatExchangeInstalled = true
        ),
        windowsShutters = BuildingCalculationInput.WindowsShutters(
            windowsShutters = BuildingWindowsShutters.MANUAL
        ),
        windowsCoating = BuildingCalculationInput.WindowsCoating(
            windowsCoatingType = BuildingWindowCoatingType.LOW_E_COATING,
        ),
        surfaceHeating = null,
        heatingRadiators = BuildingCalculationInput.HeatingRadiators(
            radiatorType = BuildingHeatingRadiatorType.STANDARD
        ),
        intelligentHeatingControls = BuildingCalculationInput.IntelligentHeatingControls(
            installed = false
        ),
        lightingAutomation = BuildingCalculationInput.LightingAutomation(
            installed = false
        ),
        solarPanelsBattery = null,
        solarPanelsImmersionHeater = BuildingCalculationInput.SolarPanelsImmersionHeater(
            installed = false
        ),
        groundFloor = BuildingCalculationInput.GroundFloor(
            groundFloorThickness = 10,
            groundFloorType = BuildingScreedType.CEMENT,
            groundFloorFinishThickness = 2,
            groundFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS
        ),
        basementFloor = BuildingCalculationInput.BasementFloor(
            basementFloorThickness = 10,
            basementFloorType = BuildingScreedType.CEMENT,
            basementFloorFinishThickness = 2,
            basementFloorFinishType = BuildingFloorFinishType.CERAMIC_TILES
        )
    )

fun createFullyInitializedBuildingMissingAttributesGeneratedEntity(): BuildingMissingAttributesGeneratedEntity =
    BuildingMissingAttributesGeneratedEntity(
        id = UUID.randomUUID(),
        roofInsulationThickness = 10,
        roofInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
        roofMaterial = BuildingRoofMaterial.CLAY_TILES,

        facadeWallType = BuildingWallType.BRICK,
        facadeWallThickness = 30,

        windowsFrameMaterial = BuildingWindowFrameMaterial.PVC,
        windowsCoatingType = BuildingWindowCoatingType.LOW_E_COATING,

        facadeWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
        facadeWallInsulationThickness = 10,

        basementFloorInsulationThickness = 10,
        basementFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,

        basementExternalWallsInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
        basementExternalWallsInsulationThickness = 10,
        basementExternalWallsType = BuildingWallType.BRICK,

        ceilingWallType = BuildingWallType.BRICK,
        ceilingWallThickness = 20,
        ceilingWallInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
        ceilingWallInsulationThickness = 10,

        groundFloorInsulationType = BuildingInsulationType.FIBERGLASS_INSULATION,
        groundFloorInsulationThickness = 10,

        entranceDoorMaterial = BuildingDoorMaterial.WOOD,
        buildingId = UUID.randomUUID()
    )

fun createFullyInitializedBuildingMissingAttributesGenerated(): BuildingMissingAttributesGenerated =
    BuildingMissingAttributesGenerated(
        roofInsulationThickness = 10,
        roofInsulationType = MissingAttributesRoofInsulationType.FIBERGLASS_INSULATION,
        roofMaterial = BuildingRoofMaterial.CLAY_TILES,

        facadeWallType = BuildingWallType.BRICK,
        facadeWallThickness = 30,

        windowsFrameMaterial = BuildingWindowFrameMaterial.PVC,
        windowsCoatingType = BuildingWindowCoatingType.LOW_E_COATING,

        facadeWallInsulationType = MissingAttributesFacadeInsulationType.FIBERGLASS_INSULATION,
        facadeWallInsulationThickness = 10,

        basementFloorInsulationThickness = 10,
        basementFloorInsulationType = MissingAttributesBasementInsulationType.EXPANDED_POLYSTYRENE,

        basementExternalWallsInsulationType = MissingAttributesBasementInsulationType.EXPANDED_POLYSTYRENE,
        basementExternalWallsInsulationThickness = 10,
        basementExternalWallsType = BuildingWallType.BRICK,

        ceilingWallType = BuildingWallType.BRICK,
        ceilingWallThickness = 20,
        ceilingWallInsulationType = MissingAttributesCeilingInsulationType.MINERAL_WOOL,
        ceilingWallInsulationThickness = 10,

        groundFloorInsulationType = MissingAttributesGroundFloorInsulationType.EXPANDED_POLYSTYRENE,
        groundFloorInsulationThickness = 10,

        entranceDoorMaterial = BuildingDoorMaterial.WOOD
    )

fun createFullyInitializedBuildingShapeResult(): BuildingShapeResult =
    BuildingShapeResult(
        buildingVolume = 200.0,
        buildingArea = 100.0,
        buildingHeatedArea = 0.0,
        buildingThermalEnvelopeVolume = 200.0,
        buildingRoofSurfaceArea = 50.0,
        buildingBaseArea = 0.0,
        thermalEnvelopeElements = setOf(
            BuildingShapeResult.ThermalEnvelopeElement(
                type = ThermalEnvelopeElementType.DOOR,
                area = 1.8
            ),
            BuildingShapeResult.ThermalEnvelopeElement(
                type = ThermalEnvelopeElementType.FACADE_WALLS,
                area = 89.25
            ),
            BuildingShapeResult.ThermalEnvelopeElement(
                type = ThermalEnvelopeElementType.WINDOWS,
                area = 15.75
            ),
            BuildingShapeResult.ThermalEnvelopeElement(
                type = ThermalEnvelopeElementType.CEILING,
                area = 50.0
            ),
            BuildingShapeResult.ThermalEnvelopeElement(
                type = ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS,
                area = 36.75
            ),
            BuildingShapeResult.ThermalEnvelopeElement(
                type = ThermalEnvelopeElementType.BASEMENT_FLOOR,
                area = 50.0
            )
        ),
        roofFloorVolume = 0.0,
        buildingPerimeter = 100.0
    )

fun createFullyInitializedBuildingDto(): BuildingDto =
    BuildingDto(
        id = UUID.randomUUID(),
        shape = BuildingShape.COMPACT,
        position = BuildingPosition.STAND_ALONE,
        street = "Hallerstraße",
        no = "57",
        zipCode = "20146",
        city = "Hamburg",
        constructionYear = 1990,
        area = 120.0,
        floors = 10,
        floorHeight = BuildingFloorHeight.REGULAR,
        tenants = 20,
        heating = BuildingHeatingDto(
            id = UUID.randomUUID(),
            primaryHeating = BuildingHeating.GAS_BOILER,
            waterHeating = BuildingHeating.ELECTRIC,
            hasFloorHeating = false,
            hasSolarThermalPlant = false,
            primaryHeatingInstallationYear = 2005,
        ),
        facade = BuildingFacadeDto(
            id = UUID.randomUUID(),
            insulated = true,
            insulationYear = 2010,
        ),
        roof = BuildingRoofDto(
            id = UUID.randomUUID(),
            floor = BuildingRoofFloor.FLAT_ROOF,
            hasSolarPlant = false,
            eligibleForSolar = true,
            ceilingOrRoofInsulated = true,
            insulationYear = 2018,
            solarPlantInstallationYear = null,
            solarPlantPower = 15.5,
        ),
        windows = BuildingWindowsDto(
            id = UUID.randomUUID(),
            windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
            glazing = BuildingWindowsGlazing.DOUBLE,
            shutters = BuildingWindowsShutters.MANUAL,
            installationYear = 2016,
        ),
        electricalEquipment = listOf(
            BuildingElectricalEquipmentDto(
                id = UUID.randomUUID(),
                type = BuildingElectricalEquipment.AC,
                installationYear = 2015,
            )
        ),
        basement = BuildingBasementDto(
            id = UUID.randomUUID(),
            heated = false,
            insulated = true,
            insulationYear = null
        ),
        groundFloorInsulated = true,
        groundFloorInsulationYear = null
    )

fun createFullyInitializedBuildingEntity(): BuildingEntity =
    BuildingEntity(
        id = UUID.randomUUID(),
        shape = BuildingShape.COMPACT,
        position = BuildingPosition.STAND_ALONE,
        street = "Hallerstraße",
        no = "57",
        zipCode = "20146",
        city = "Hamburg",
        addressKey = "20146_hamburg_hallerstrasse_57",
        constructionYear = 1990,
        area = 120.0,
        floors = 2,
        floorHeight = BuildingFloorHeight.REGULAR,
        tenants = 2,
        heating = BuildingHeatingEntity(
            id = UUID.randomUUID(),
            primaryHeating = BuildingHeating.GAS_BOILER,
            waterHeating = BuildingHeating.ELECTRIC,
            hasFloorHeating = false,
            hasSolarThermalPlant = false,
            primaryHeatingInstallationYear = 2005,
            building = mockk<BuildingEntity>()
        ),
        facade = BuildingFacadeEntity(
            id = UUID.randomUUID(),
            insulated = true,
            insulationYear = 2010,
            building = mockk<BuildingEntity>()
        ),
        roof = BuildingRoofEntity(
            id = UUID.randomUUID(),
            floor = BuildingRoofFloor.FLAT_ROOF,
            hasSolarPlant = false,
            eligibleForSolar = true,
            ceilingOrRoofInsulated = true,
            insulationYear = 2018,
            solarPlantInstallationYear = null,
            solarPlantPower = 15.5,
            building = mockk<BuildingEntity>()
        ),
        windows = BuildingWindowsEntity(
            id = UUID.randomUUID(),
            windowsToWallRatio = BuildingWindowsToWallRatio.MEDIUM,
            glazing = BuildingWindowsGlazing.DOUBLE,
            shutters = BuildingWindowsShutters.MANUAL,
            installationYear = 2016,
            building = mockk<BuildingEntity>()
        ),
        electricalEquipment = emptyList(),
        basement = BuildingBasementEntity(
            id = UUID.randomUUID(),
            heated = false,
            insulated = true,
            insulationYear = 1990,
            building = mockk<BuildingEntity>()
        ),
        groundFloorInsulated = true,
        groundFloorInsulationYear = null
    )

fun createFullyInitializedUValuesCalculationResult() = UValueCalculationResult(
    setOf(
        UValueCalculationResult.UValuesThermalEnvelopeElement(
            thermalEnvelopeElementType = ThermalEnvelopeElementType.DOOR,
            uValue = 2.4
        ),
        UValueCalculationResult.UValuesThermalEnvelopeElement(
            thermalEnvelopeElementType = ThermalEnvelopeElementType.FACADE_WALLS,
            uValue = 0.24
        ),
        UValueCalculationResult.UValuesThermalEnvelopeElement(
            thermalEnvelopeElementType = ThermalEnvelopeElementType.WINDOWS,
            uValue = 1.5
        ),
        UValueCalculationResult.UValuesThermalEnvelopeElement(
            thermalEnvelopeElementType = ThermalEnvelopeElementType.CEILING,
            uValue = 0.25
        ),
        UValueCalculationResult.UValuesThermalEnvelopeElement(
            thermalEnvelopeElementType = ThermalEnvelopeElementType.BASEMENT_FLOOR,
            uValue = 0.24
        ),
        UValueCalculationResult.UValuesThermalEnvelopeElement(
            thermalEnvelopeElementType = ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS,
            uValue = 0.23
        )
    )
)

fun createFullyInitializedUValuesEntitySet(buildingId: UUID) =
    setOf(
        UValueEntity(
            id = UUID.randomUUID(),
            thermalEnvelopeElementType = ThermalEnvelopeElementType.DOOR,
            uValue = 2.4,
            material = "STEEL",
            insulation = null,
            insulationThickness = null,
            type1 = null,
            type2 = null,
            materialThickness = null
        ),
        UValueEntity(
            id = UUID.randomUUID(),
            thermalEnvelopeElementType = ThermalEnvelopeElementType.FACADE_WALLS,
            uValue = 0.24,
            material = "BRICK",
            insulation = BuildingInsulationType.MINERAL_WOOL,
            insulationThickness = 10,
            type1 = null,
            type2 = null,
            materialThickness = 20
        ),
        UValueEntity(
            id = UUID.randomUUID(),
            thermalEnvelopeElementType = ThermalEnvelopeElementType.WINDOWS,
            uValue = 1.5,
            material = BuildingWindowFrameMaterial.PVC.name,
            insulation = null,
            insulationThickness = null,
            type1 = BuildingWindowsGlazing.DOUBLE.name,
            type2 = BuildingWindowCoatingType.LOW_E_COATING.name,
            materialThickness = null
        ),
        UValueEntity(
            id = UUID.randomUUID(),
            thermalEnvelopeElementType = ThermalEnvelopeElementType.CEILING,
            uValue = 0.25,
            material = "BRICK",
            insulation = BuildingInsulationType.MINERAL_WOOL,
            insulationThickness = 20,
            type1 = null,
            type2 = null,
            materialThickness = 20
        )
    )


fun createFullyInitializedClimateDataResult(): ClimateDataResult =
    ClimateDataResult(
        heatingDegreeDays = 3000,
        amountOfDaysContributingToHeatingDegreeDays = 250,
        coolingDegreeDays = 50,
        amountOfDaysContributingToCoolingDegreeDays = 20,
        averageAnnualSolarRadiation = 1200000.0,
    )

fun createFullyInitializedCalculationResultEntity(buildingId: UUID = UUID.randomUUID()) = CalculationResultEntity(
    id = UUID.randomUUID(),
    category = CalculationResultCategory.CURRENT,
    primaryEnergyDemand = 123.0,
    primaryEnergyDemandUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
    primaryEnergyDemandScaleGrade = EPCScaleGrade.D,
    finalEnergyDemandPerArea = 100.0,
    finalEnergyDemandPerAreaUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
    finalEnergyDemandPerAreaScaleGrade = EPCScaleGrade.C,
    co2Emissions = 123.0,
    co2EmissionsUnit = Co2EmissionUnit.KG,
    solarEnergyProduction = 0.0,
    solarEnergyProductionUnit = EnergyOverTimeUnit.KWH_YEAR,
    finalElectricityDemand = 4000.0,
    finalHotWaterDemand = 2000.0,
    finalHeatingEnergyDemand = 4500.0,
    finalCoolingEnergyDemand = 0.0,
    buildingId = buildingId,
)

fun createFullyInitializedCalculationResultDto() = CalculationResultDto(
    id = UUID.randomUUID(),
    category = CalculationResultCategory.CURRENT,
    primaryEnergyDemand = 123.0,
    primaryEnergyDemandUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
    primaryEnergyDemandScaleGrade = EPCScaleGrade.D,
    finalEnergyDemandPerArea = 100.0,
    finalEnergyDemandPerAreaUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
    finalEnergyDemandPerAreaScaleGrade = EPCScaleGrade.C,
    co2Emissions = 123.0,
    co2EmissionsUnit = Co2EmissionUnit.KG,
    solarEnergyProduction = 0.0,
    solarEnergyProductionUnit = EnergyOverTimeUnit.KWH_YEAR,
    finalElectricityDemand = 4000.0,
    finalHotWaterDemand = 2000.0,
    finalHeatingEnergyDemand = 4500.0,
    finalCoolingEnergyDemand = 0.0
)