package de.finacte.epc.dto.integration.europace

import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import de.finacte.epc.entity.EPCScaleGrade
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import java.math.BigDecimal

class ImportKundenangabenRequestTest : StringSpec({
    val mapper = jacksonObjectMapper()
    mapper.enable(SerializationFeature.INDENT_OUTPUT)

    "convert object with Einfamilienhaus to JSON" {
        val apiObject = ImportKundenangabenRequest(
            importMetadaten = ImportMetadaten(
                datenkontext = Datenkontext.TEST_MODUS
            ),
            kundenangaben = Kundenangaben(
                finanzierungsobjekt = Finanzierungsobjekt(
                    immobilie = Immobilie(
                        adresse = Adresse(
                            strasse = "Street name",
                            hausnummer = "16A",
                            plz = "82784",
                            ort = "Hamburg"
                        ),
                        typ = Einfamilienhaus(
                            gebaeude = Gebaeude(
                                baujahr = 2018,
                                anzahlGeschosse = 2,
                                nutzung = Nutzung(
                                    wohnen = Wohnen(
                                        gesamtflaeche = 150.0
                                    )
                                ),
                                energieEffizienzAusweis = EnergieEffizienzAusweis(
                                    primaerEnergieTraeger = PrimaerEnergieTraeger.HOLZ,
                                    endEnergie = 120,
                                    energieEffizienzklasse = EPCScaleGrade.B,
                                    co2Emissionen = 1200
                                ),
                                modernisierung = Modernisierung(
                                    jahr = 2015,
                                    grad = Grad.MITTEL
                                )
                            ),
                            nutzflaechen = Nutzflaechen(
                                dachgeschoss = Dachgeschoss.FLACHDACH,
                                unterkellerung = Unterkellerung.UNTERKELLERT
                            )
                        ),
                    )
                ),
                finanzierungsbedarf = Finanzierungsbedarf(
                    finanzierungszweck = ModernisierungUmbauAnbau(
                        modernisierung = Modernisierungskosten(
                            modernisierungskostenInklEigenleistungen = 2345.0,
                            endEnergieGeplant = BigDecimal.valueOf(120.0),
                            co2WertGeplant = BigDecimal.valueOf(1000.0)
                        )
                    )
                )
            )
        )


        val result = mapper.writeValueAsString(apiObject)

        result.trimIndent() shouldBe """
                                {
                                  "importMetadaten" : {
                                    "datenkontext" : "TEST_MODUS"
                                  },
                                  "kundenangaben" : {
                                    "finanzierungsobjekt" : {
                                      "immobilie" : {
                                        "adresse" : {
                                          "strasse" : "Street name",
                                          "hausnummer" : "16A",
                                          "plz" : "82784",
                                          "ort" : "Hamburg"
                                        },
                                        "typ" : {
                                          "@type" : "EINFAMILIENHAUS",
                                          "gebaeude" : {
                                            "baujahr" : 2018,
                                            "bauweise" : "MASSIV",
                                            "anzahlGeschosse" : 2,
                                            "zustand" : "MITTEL",
                                            "ausstattung" : "MITTEL",
                                            "nutzung" : {
                                              "wohnen" : {
                                                "gesamtflaeche" : 150.0
                                              }
                                            },
                                            "energieEffizienzAusweis" : {
                                              "ausweistyp" : "ENDENERGIEBEDARF",
                                              "endEnergie" : 120,
                                              "primaerEnergieTraeger" : "HOLZ",
                                              "energieEffizienzklasse" : "B",
                                              "co2Emissionen" : 1200
                                            },
                                            "modernisierung" : {
                                              "jahr" : 2015,
                                              "grad" : "MITTEL"
                                            }
                                          },
                                          "nutzflaechen" : {
                                            "dachgeschoss" : "FLACHDACH",
                                            "unterkellerung" : "UNTERKELLERT"
                                          }
                                        }
                                      }
                                    },
                                    "finanzierungsbedarf" : {
                                      "finanzierungszweck" : {
                                        "@type" : "MODERNISIERUNG_UMBAU_ANBAU",
                                        "modernisierung" : {
                                          "modernisierungskostenInklEigenleistungen" : 2345.0,
                                          "endEnergieGeplant" : 120.0,
                                          "co2WertGeplant" : 1000.0
                                        }
                                      }
                                    }
                                  }
                                }
        """.trimIndent()
    }

    "convert object with Reihenhaus to JSON" {
        val apiObject = ImportKundenangabenRequest(
            importMetadaten = ImportMetadaten(
                datenkontext = Datenkontext.TEST_MODUS
            ),
            kundenangaben = Kundenangaben(
                finanzierungsobjekt = Finanzierungsobjekt(
                    immobilie = Immobilie(
                        adresse = Adresse(
                            strasse = "Street name",
                            hausnummer = "16A",
                            plz = "82784",
                            ort = "Hamburg"
                        ),
                        typ = Reihenhaus(
                            gebaeude = Gebaeude(
                                baujahr = 2018,
                                anzahlGeschosse = 2,
                                nutzung = Nutzung(
                                    wohnen = Wohnen(
                                        gesamtflaeche = 150.0
                                    )
                                ),
                                energieEffizienzAusweis = EnergieEffizienzAusweis(
                                    primaerEnergieTraeger = PrimaerEnergieTraeger.HOLZ,
                                    endEnergie = 120,
                                    energieEffizienzklasse = EPCScaleGrade.B,
                                    co2Emissionen = 1200
                                ),
                                modernisierung = Modernisierung(
                                    jahr = 2015,
                                    grad = Grad.MITTEL
                                )
                            ),
                            nutzflaechen = Nutzflaechen(
                                dachgeschoss = Dachgeschoss.FLACHDACH,
                                unterkellerung = Unterkellerung.UNTERKELLERT
                            ),
                            haustyp = ReihenhausHausTyp.KOPFHAUS
                        ),
                    )
                ),
                finanzierungsbedarf = Finanzierungsbedarf(
                    finanzierungszweck = ModernisierungUmbauAnbau(
                        modernisierung = Modernisierungskosten(
                            modernisierungskostenInklEigenleistungen = 2345.0,
                            endEnergieGeplant = BigDecimal.valueOf(120.0),
                            co2WertGeplant = BigDecimal.valueOf(1000.0)
                        )
                    )
                )

            )
        )


        val result = mapper.writeValueAsString(apiObject)

        result.trimIndent() shouldBe """
                                {
                                  "importMetadaten" : {
                                    "datenkontext" : "TEST_MODUS"
                                  },
                                  "kundenangaben" : {
                                    "finanzierungsobjekt" : {
                                      "immobilie" : {
                                        "adresse" : {
                                          "strasse" : "Street name",
                                          "hausnummer" : "16A",
                                          "plz" : "82784",
                                          "ort" : "Hamburg"
                                        },
                                        "typ" : {
                                          "@type" : "REIHENHAUS",
                                          "gebaeude" : {
                                            "baujahr" : 2018,
                                            "bauweise" : "MASSIV",
                                            "anzahlGeschosse" : 2,
                                            "zustand" : "MITTEL",
                                            "ausstattung" : "MITTEL",
                                            "nutzung" : {
                                              "wohnen" : {
                                                "gesamtflaeche" : 150.0
                                              }
                                            },
                                            "energieEffizienzAusweis" : {
                                              "ausweistyp" : "ENDENERGIEBEDARF",
                                              "endEnergie" : 120,
                                              "primaerEnergieTraeger" : "HOLZ",
                                              "energieEffizienzklasse" : "B",
                                              "co2Emissionen" : 1200
                                            },
                                            "modernisierung" : {
                                              "jahr" : 2015,
                                              "grad" : "MITTEL"
                                            }
                                          },
                                          "nutzflaechen" : {
                                            "dachgeschoss" : "FLACHDACH",
                                            "unterkellerung" : "UNTERKELLERT"
                                          },
                                          "haustyp" : "KOPFHAUS"
                                        }
                                      }
                                    },
                                    "finanzierungsbedarf" : {
                                      "finanzierungszweck" : {
                                        "@type" : "MODERNISIERUNG_UMBAU_ANBAU",
                                        "modernisierung" : {
                                          "modernisierungskostenInklEigenleistungen" : 2345.0,
                                          "endEnergieGeplant" : 120.0,
                                          "co2WertGeplant" : 1000.0
                                        }
                                      }
                                    }
                                  }
                                }
        """.trimIndent()
    }
})
