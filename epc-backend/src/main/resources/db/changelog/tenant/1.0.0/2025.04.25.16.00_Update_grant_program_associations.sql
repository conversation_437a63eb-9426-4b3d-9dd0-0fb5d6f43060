-- 1. Remove all associations between renovation_templates and grant_program where renovation_template has version equal to 2
DELETE FROM renovation_template_grant_program
WHERE renovation_template_id IN (
    SELECT id FROM renovation_template WHERE renovation_measure_version = 2
);

-- 2. Add new grant program with KFW_159 type
INSERT INTO grant_program (id, organisation_name, created_by, creation_date, last_modified_by, last_modified_date, link, type)
VALUES ('f206d166-4f2e-4f65-89d6-f0832b50a2d3', 'KfW', 'Migration', CURRENT_TIMESTAMP, null, null, 'https://www.kfw.de/inlandsfoerderung/Privatpersonen/Bestehende-Immobilie/F%C3%B6rderprodukte/Altersgerecht-Umbauen-(159)/', 'KFW_159');


-- 3. Associate renovation templates with grant programs based on category
-- a. Associate AGE_APPROPRIATE_LIVING templates with KFW_159
INSERT INTO renovation_template_grant_program (id, renovation_template_id, grant_program_id, created_by, creation_date, last_modified_by, last_modified_date)
SELECT 
    gen_random_uuid(),
    rt.id,
    gp.id,
    'Migration',
    CURRENT_TIMESTAMP,
    null,
    null
FROM 
    renovation_template rt,
    grant_program gp
WHERE 
    rt.renovation_measure_version = 2
    AND rt.renovation_measure_category = 'AGE_APPROPRIATE_LIVING'
    AND gp.type = 'KFW_159';

-- b. Associate SOLAR_PANELS templates with KFW_270
INSERT INTO renovation_template_grant_program (id, renovation_template_id, grant_program_id, created_by, creation_date, last_modified_by, last_modified_date)
SELECT 
    gen_random_uuid(),
    rt.id,
    gp.id,
    'Migration',
    CURRENT_TIMESTAMP,
    null,
    null
FROM 
    renovation_template rt,
    grant_program gp
WHERE 
    rt.renovation_measure_version = 2
    AND rt.renovation_measure_category = 'SOLAR_PANELS'
    AND gp.type = 'KFW_270';

-- c. Associate all other version 2 templates with KFW_261
INSERT INTO renovation_template_grant_program (id, renovation_template_id, grant_program_id, created_by, creation_date, last_modified_by, last_modified_date)
SELECT 
    gen_random_uuid(),
    rt.id,
    gp.id,
    'Migration',
    CURRENT_TIMESTAMP,
    null,
    null
FROM 
    renovation_template rt,
    grant_program gp
WHERE 
    rt.renovation_measure_version = 2
    AND rt.renovation_measure_category NOT IN ('AGE_APPROPRIATE_LIVING', 'SOLAR_PANELS')
    AND gp.type = 'KFW_261';
