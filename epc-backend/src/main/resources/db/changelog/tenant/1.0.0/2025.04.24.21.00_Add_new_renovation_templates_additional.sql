insert into renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)

VALUES -- Solar Panels
       ('a1b2c3d4-e5f6-4a7b-8c9d-1e2f3a4b5c6d', 'SOLAR_PANELS',
        '{"solarPanelsExist": true, "installationYear": 2025, "batteryType": "LITHIUM_ION", "immersionHeater": false}',
        'Migration', CURRENT_TIMESTAMP, null, null, 60, 'BATTERY_STORAGE',
        'LITHIUM_ION_BATTERY', 2, 'DEFAULT', 'Battery storage - Lithium-ion'),
       ('b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e', 'SOLAR_PANELS',
        '{"solarPanelsExist": true, "installationYear": 2025, "batteryType": "SALTWATER", "immersionHeater": false}',
        'Migration', CURRENT_TIMESTAMP, null, null, 61, 'BATTERY_STORAGE',
        'SALTWATER_BATTERY', 2, 'DEFAULT', 'Battery storage - Saltwater batteries'),
       ('c3d4e5f6-a7b8-4c9d-1e2f-3a4b5c6d7e8f', 'SOLAR_PANELS',
        '{"solarPanelsExist": true, "installationYear": 2025, "batteryType": "NONE", "immersionHeater": true}',
        'Migration', CURRENT_TIMESTAMP, null, null, 62, 'BUFFER_STORAGE',
        'WITH_IMMERSION_HEATER', 2, 'DEFAULT', 'Buffer storage with immersion heater'),
       ('d4e5f6a7-b8c9-4d1e-2f3a-4b5c6d7e8f9a', 'SOLAR_PANELS',
        '{"solarPanelsExist": true, "installationYear": 2025, "batteryType": "NONE", "immersionHeater": false}',
        'Migration', CURRENT_TIMESTAMP, null, null, 63, 'BUFFER_STORAGE',
        'WITHOUT_IMMERSION_HEATER', 2, 'DEFAULT', 'Buffer storage without immersion heater'),

       -- Smart Home Energy Management
       ('e5f6a7b8-c9d1-4e2f-3a4b-5c6d7e8f9a0b', 'SMART_HOME_ENERGY_MANAGEMENT',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 64, 'INTELLIGENT_HEATING_CONTROLS',
        'DEFAULT', 2, 'DEFAULT', 'Intelligent heating controls'),
       ('f6a7b8c9-d1e2-4f3a-4b5c-6d7e8f9a0b1c', 'SMART_HOME_ENERGY_MANAGEMENT',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 65, 'LIGHTING_AUTOMATION',
        'DEFAULT', 2, 'DEFAULT', 'Lighting automation'),
       ('a7b8c9d1-e2f3-4a4b-5c6d-7e8f9a0b1c2d', 'SMART_HOME_ENERGY_MANAGEMENT',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 66, 'SENSORS',
        'DEFAULT', 2, 'DEFAULT', 'Sensors'),

       -- Electric Vehicle Charging Station
       ('b8c9d1e2-f3a4-4b5c-6d7e-8f9a0b1c2d3e', 'ELECTRIC_VEHICLE_CHARGING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 67, 'WALLBOX_INSTALLATION',
        'DEFAULT', 2, 'DEFAULT', 'Wallbox installation'),
       ('c9d1e2f3-a4b5-4c6d-7e8f-9a0b1c2d3e4f', 'ELECTRIC_VEHICLE_CHARGING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 68, 'GRID_CONNECTION_UPGRADE',
        'DEFAULT', 2, 'DEFAULT', 'Grid connection upgrade'),

       -- Age Appropriate Living
       ('d1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a', 'AGE_APPROPRIATE_LIVING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 69, 'BARRIER_FREE_BATHROOM',
        'DEFAULT', 2, 'DEFAULT', 'Barrier-free bathroom'),
       ('e2f3a4b5-c6d7-4e8f-9a0b-1c2d3e4f5a6b', 'AGE_APPROPRIATE_LIVING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 70, 'STAIRLIFT',
        'DEFAULT', 2, 'DEFAULT', 'Stairlift'),
       ('f3a4b5c6-d7e8-4f9a-0b1c-2d3e4f5a6b7c', 'AGE_APPROPRIATE_LIVING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 71, 'WIDENING_DOORS',
        'DEFAULT', 2, 'DEFAULT', 'Widening doors'),
       ('a4b5c6d7-e8f9-4a0b-1c2d-3e4f5a6b7c8d', 'AGE_APPROPRIATE_LIVING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 72, 'EMERGENCY_SYSTEMS',
        'DEFAULT', 2, 'DEFAULT', 'Emergency systems');


UPDATE renovation_template
SET renovation_measure_object = '{"solarPanelsExist": true, "installationYear": 2025, "batteryType": "SALTWATER", "immersionHeater": false}'
    WHERE id = '4d406150-894b-4b07-85b1-830e4254a430'; -- Old renovation


update renovation_template
set renovation_measure_category = 'SOLAR_PANELS'
where renovation_measure_category = 'SOLAR_PLANT';