databaseChangeLog:
  - include:
      relativeToChangelogFile: true
      file: 2024.12.24.13.05_Initial_squashed.sql
  - include:
      relativeToChangelogFile: true
      file: 2024.12.30.14.30_Insert_german_renovation_translations.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.20.10.30_Add_europace_case.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.21.15.30_Add_construction_material_properties_conductivity.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.27.09.10_Add_links_to_grants.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.30.10.30_Fix_construction_material_properties_roof.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.31.13.30_Add_order_to_renovation_template.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.06.15.25_Update_calculation_profitability_costs.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.07.10.30_Add_address_key.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.13.10.30_Update_heating_options.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.14.10.30_Update_renovation_templates.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.24.10.00_Change_area_type.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.13.09.00_Add_date_to_system_efficiency.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.17.12.30_Add_installation_to_solar.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.18.13.30_Fix_solar_panel_renovation_templates.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.19.10.00_Add_roof_insulation_bool.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.21.15.00_Add_air_gap_thermal_resistance.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.24.15.00_Add_basement_insulation_bool.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.27.15.10_Add_ground_floor_insulation_year.sql
  - changeSet:
      id: Update renovation structure
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.01.10.00_Update_renovation_template.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.01.10.00_Update_renovation_template_rollback.sql
  - changeSet:
      id: Add new renovation templates and update structure
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.03.13.10_Add_new_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.03.13.10_Add_new_renovation_templates_rollback.sql
  - changeSet:
      id: Add objects (JSON) to new renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.10.10.00_Add_new_objects_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.10.10.00_Add_new_objects_renovation_templates_rollback.sql
  - changeSet:
      id: Add type (enum) to grant programs
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.15.21.00_Add_grant_enum.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.15.21.00_Add_grant_enum_rollback.sql
  - changeSet:
      id: Fix objects (JSON) for new renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.16.12.00_Fix_new_renovation_template_objects.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.16.12.00_Fix_new_renovation_template_objects_rollback.sql
  - changeSet:
      id: Fix OIL to OIL_BOILER heating options
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.22.11.00_Fix_heating_options.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.22.11.00_Fix_heating_options_rollback.sql