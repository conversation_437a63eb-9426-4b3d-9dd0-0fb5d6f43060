databaseChangeLog:
  - include:
      relativeToChangelogFile: true
      file: 2024.12.24.13.05_Initial_squashed.sql
  - include:
      relativeToChangelogFile: true
      file: 2024.12.30.14.30_Insert_german_renovation_translations.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.20.10.30_Add_europace_case.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.21.15.30_Add_construction_material_properties_conductivity.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.27.09.10_Add_links_to_grants.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.30.10.30_Fix_construction_material_properties_roof.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.01.31.13.30_Add_order_to_renovation_template.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.06.15.25_Update_calculation_profitability_costs.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.07.10.30_Add_address_key.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.13.10.30_Update_heating_options.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.14.10.30_Update_renovation_templates.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.02.24.10.00_Change_area_type.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.13.09.00_Add_date_to_system_efficiency.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.17.12.30_Add_installation_to_solar.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.18.13.30_Fix_solar_panel_renovation_templates.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.19.10.00_Add_roof_insulation_bool.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.21.15.00_Add_air_gap_thermal_resistance.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.24.15.00_Add_basement_insulation_bool.sql
  - include:
      relativeToChangelogFile: true
      file: 2025.03.27.15.10_Add_ground_floor_insulation_year.sql
  - changeSet:
      id: Update renovation structure
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.01.10.00_Update_renovation_template.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.01.10.00_Update_renovation_template_rollback.sql
  - changeSet:
      id: Add new renovation templates and update structure
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.03.13.10_Add_new_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.03.13.10_Add_new_renovation_templates_rollback.sql
  - changeSet:
      id: Add objects (JSON) to new renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.10.10.00_Add_new_objects_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.10.10.00_Add_new_objects_renovation_templates_rollback.sql
  - changeSet:
      id: Add type (enum) to grant programs
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.15.21.00_Add_grant_enum.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.15.21.00_Add_grant_enum_rollback.sql
  - changeSet:
      id: Fix objects (JSON) for new renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.16.12.00_Fix_new_renovation_template_objects.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.16.12.00_Fix_new_renovation_template_objects_rollback.sql
  - changeSet:
      id: Fix OIL to OIL_BOILER heating options
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.22.11.00_Fix_heating_options.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.22.11.00_Fix_heating_options_rollback.sql
  - changeSet:
      id: Add new renovation templates for heating systems
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.23.09.00_Add_new_renovation_templates_heating.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.23.09.00_Add_new_renovation_templates_heating_rollback.sql
  - changeSet:
      id: Add new renovation templates for additional categories
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.24.21.00_Add_new_renovation_templates_additional.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.24.21.00_Add_new_renovation_templates_additional_rollback.sql
  - changeSet:
      id: Fix roof renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.25.13.00_Fix_roof_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.25.13.00_Fix_roof_renovation_templates_rollback.sql
  - changeSet:
      id: Update grant program associations
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.25.16.00_Update_grant_program_associations.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.25.16.00_Update_grant_program_associations_rollback.sql
  - changeSet:
      id: Update renovation descriptions
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.28.11.00_Update_renovation_descriptions.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.28.11.00_Update_renovation_descriptions_rollback.sql
  - changeSet:
      id: Add KFW_159 translations
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.28.13.20_Add_KFW_159_translations.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.28.13.20_Add_KFW_159_translations_rollback.sql
  - changeSet:
      id: Update heating renovation templates with deliverySystem
      author: AI
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.29.11.00_Update_heating_delivery_system_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.29.11.00_Update_heating_delivery_system_renovation_templates_rollback.sql
  - changeSet:
      id: Update heating renovation templates with smart systems and remove sensor and window frame
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.29.15.00_Update_smart_systems_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.29.15.00_Update_smart_systems_renovation_templates_rollback.sql
  - changeSet:
      id: Update solar panels renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.30.08.00_Update_solar_panels_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.30.08.00_Update_solar_panels_renovation_templates_rollback.sql
  - changeSet:
      id: Add old types of insulations to construction_material_properties
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.04.30.15.00_Add_old_insulation_types.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.04.30.15.00_Add_old_insulation_types_rollback.sql
  - changeSet:
      id: Add pure (without additions) solar panels system
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.02.12.30_Add_solar_panels_renovation_template.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.02.12.30_Add_solar_panels_renovation_template_rollback.sql
  - changeSet:
      id: Update solar panels buffer renovation template
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.04.18.30_Update_solar_panels_buffer_renovation.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.04.18.30_Update_solar_panels_buffer_renovation_rollback.sql
  - changeSet:
      id: Update aerogel renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.05.11.30_Update_aerogel_renovation_templates.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.05.11.30_Update_aerogel_renovation_templates_rollback.sql
  - changeSet:
      id: Update solar panels renovation templates description
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.05.13.30_Update_solar_panels_renovation_template_description.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.05.13.30_Update_solar_panels_renovation_template_description_rollback.sql
  - changeSet:
      id: Update renovation templates description (part 1)
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.05.15.30_Update_renovation_template_description.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.05.15.30_Update_renovation_template_description_rollback.sql
  - changeSet:
      id: Update construction material properties thickness ranges
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.06.09.30_Update_construction_material_properties_thickness_ranges.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.06.09.30_Update_construction_material_properties_thickness_ranges_rollback.sql
  - changeSet:
      id: Update construction material properties thickness ranges (part 2 - using name and type)
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.06.12.30_Update_construction_material_properties_thickness_ranges.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.06.12.30_Update_construction_material_properties_thickness_ranges_rollback.sql
  - changeSet:
      id: Update renovation template order
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.06.13.30_Update_renovation_order.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.06.13.30_Update_renovation_order_rollback.sql
  - changeSet:
      id: Remove grant from EV renovation templates
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.06.15.00_Remove_grant_from_EV.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.06.15.00_Remove_grant_from_EV_rollback.sql
  - changeSet:
      id: Update renovation template description for problematic renovations
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.09.11.00_Update_problematic_renovation_description.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.09.11.00_Update_problematic_renovation_description_rollback.sql
  - changeSet:
      id: Update renovation template description for problematic renovations - PV batteries
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.09.15.00_Update_problematic_renovation_description_part2.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.09.15.00_Update_problematic_renovation_description_part2_rollback.sql
  - changeSet:
      id: Remove translation entities
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.10.10.00_Remove_translation_entities.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.10.10.00_Remove_translation_entities_rollback.sql
  - changeSet:
      id: Rename energy efficiency columns to primary energy demand
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.09.10.00_Rename_energy_efficiency_columns.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.09.10.00_Rename_energy_efficiency_columns_rollback.sql
  - changeSet:
      id: Create surface heating category
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.***********_Create_surface_heating_category.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.***********_Create_surface_heating_category_rollback.sql
  - changeSet:
      id: Update solar panels buffer renovation
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.14.09.00_Update_buffer_renovation.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.14.09.00_Update_buffer_renovation_rollback.sql
  - changeSet:
      id: Remove renovation measure version 1
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.14.14.00_Remove_renovation_measure_version_1.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.14.14.00_Remove_renovation_measure_version_1_rollback.sql
  - changeSet:
      id: Update solar panels renovation templates - improve battery costs
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.15.15.00_Update_solar_panels_renovation.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.15.15.00_Update_solar_panels_renovation_rollback.sql
  - changeSet:
      id: Remove saltwater battery renovation
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.16.09.00_Remove_saltwater_battery_renovation.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.16.09.00_Remove_saltwater_battery_renovation_rollback.sql
  - changeSet:
      id: Add screed thermal conductivity
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.20.10.00_Add_screed_thermal_conductivity.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.20.10.00_Add_screed_thermal_conductivity_rollback.sql
  - changeSet:
      id: Add case_type column to europace_case table
      author: MK
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: 2025.05.26.08.45_Add_case_type_to_europace_case.sql
      rollback:
        - sqlFile:
            relativeToChangelogFile: true
            path: rollback/2025.05.26.08.45_Add_case_type_to_europace_case_rollback.sql

