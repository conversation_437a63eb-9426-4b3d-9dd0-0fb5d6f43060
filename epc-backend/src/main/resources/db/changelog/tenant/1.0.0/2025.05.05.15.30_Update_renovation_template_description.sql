UPDATE renovation_template SET renovation_measure_description = 'Wet wall heating system incl. wall & paint reconstruction. This heating system is to be installed on internal walls in all rooms.' WHERE id = 'c8d9e0f1-a2b3-4c4d-5e6f-7a8b9c0d1e2f';
UPDATE renovation_template SET renovation_measure_description = 'Dry wall heating system (electric mat) incl. wall & paint reconstruction. This heating system is to be installed on internal walls in all rooms.' WHERE id = 'd9e0f1a2-b3c4-4d5e-6f7a-8b9c0d1e2f3a';
UPDATE renovation_template SET renovation_measure_description = 'Wet ceiling heating system incl. ceiling & paint reconstruction. This heating system is to be installed on internal ceilings in all rooms.' WHERE id = 'e0f1a2b3-c4d5-4e6f-7a8b-9c0d1e2f3a4b';
UPDATE renovation_template SET renovation_measure_description = 'Roof insulation using 30 CM thick fiberglass.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0103';
UPDATE renovation_template SET renovation_measure_description = 'Dry ceiling heating system incl. ceiling & paint reconstruction. This heating system is to be installed on internal ceilings in all rooms.' WHERE id = 'f1a2b3c4-d5e6-4f7a-8b9c-0d1e2f3a4b5c';
UPDATE renovation_template SET renovation_measure_description = 'Basic efficiency radiators (economy radiators) upgrade.' WHERE id = 'a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_description = 'High Performance, top quality radiators upgrade.' WHERE id = 'b3c4d5e6-f7a8-4b9c-0d1e-2f3a4b5c6d7e';
UPDATE renovation_template SET renovation_measure_description = 'Windows replacement using modern PVC double glazing windows.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e11';
UPDATE renovation_template SET renovation_measure_description = 'Windows replacement using modern PVC triple glazing windows.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e12';

UPDATE renovation_template
SET renovation_measure_description = 'Security enhancements will be retrofitted to an existing external door (one unit). This includes upgrading locks, reinforcing frames and hinges without full door replacement.'
WHERE id = '97371634-ee6d-4156-84cc-908549164529';

UPDATE renovation_template
SET renovation_measure_description = 'This measure involves the full replacement of one external door with a high-performance composite door.'
WHERE id = 'f7052a59-59b5-47b2-bc48-5aa7ebc1f5b8';

UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 10 CM thick mineral wool. Removing existing facade cladding and insulation is required. Minor repairs of existing facade structures is required.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d471';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 15 CM thick wood fiber. Removing existing facade cladding and insulation is required. Minor repairs of existing facade structures is required.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d472';
UPDATE renovation_template SET renovation_measure_description = 'Rooftop solar panel system.' WHERE id = 'd44995bf-c670-4c61-be6f-3cd066bf3c0a';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 10 CM thick extruded polystyrene. Removing existing facade cladding and insulation is required. Minor repairs of existing facade structures is required.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d473';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 4 CM thick aerogel. Removing existing facade cladding and insulation is required. Minor repairs of existing facade structures is required.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d475';
UPDATE renovation_template SET renovation_measure_description = 'Basement floor and walls insulation with 1 CM thick aerogel. ' WHERE id = 'd4ce63cb-d17a-4e51-a916-ae7ac51b091d';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using high quality plastic foil. Removing existing coverage is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0111';
UPDATE renovation_template SET renovation_measure_description = 'Facade exterior cladding using render (plaster) cladding. Removing existing facade cladding is required.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d476';
UPDATE renovation_template SET renovation_measure_description = 'Ceiling insulation using 30 CM thick fibreglass.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0101';
UPDATE renovation_template SET renovation_measure_description = 'Ceiling insulation using 5 CM thick vacuum insulated panels.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0102';
UPDATE renovation_template SET renovation_measure_description = 'Roof insulation using 5 CM thick vacuum insulated panels.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0104';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using clay tiles. Removing existing coverage is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0105';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using natural slate. Removing existing coverage is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0106';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using concrete tiles. Removing existing coverage is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0971';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using clay tiles. Removing existing coverage is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0107';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using metal tiles. Removing existing coverage is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0109';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using bitumen membrane. Removing existing coverage is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0110';
UPDATE renovation_template SET renovation_measure_description = 'Roof reinforcement renewal. Removing existing reinforcement is required.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0112';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 5 CM thick vacuum insulated panels. Removing existing facade cladding and insulation is required. Minor repairs of existing facade structures is required.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d474';
UPDATE renovation_template SET renovation_measure_description = 'EV charging station - wallbox installation.' WHERE id = 'b8c9d1e2-f3a4-4b5c-6d7e-8f9a0b1c2d3e';
UPDATE renovation_template SET renovation_measure_description = 'EV charging station - grid connection upgrade.' WHERE id = 'c9d1e2f3-a4b5-4c6d-7e8f-9a0b1c2d3e4f';
UPDATE renovation_template SET renovation_measure_description = 'Saltwater battery storage for solar panels (solar panels installation is not included).' WHERE id = 'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e';
UPDATE renovation_template SET renovation_measure_description = 'Installation of manual shutters.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e14';
UPDATE renovation_template SET renovation_measure_description = 'Installation of automatic shutters.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e15';
UPDATE renovation_template SET renovation_measure_description = 'Adding security features to windows.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e16';
UPDATE renovation_template SET renovation_measure_description = 'Ground floor insulation with 10 CM thick extruded polystyrene.' WHERE id = 'f326253b-8b8a-462c-9c05-e846ebc2a4d9';
UPDATE renovation_template SET renovation_measure_description = 'Basement floor and walls insulation with 10 CM thick extruded polystyrene.' WHERE id = '59789365-9ab2-438c-82af-a6ffd3594a48';
UPDATE renovation_template SET renovation_measure_description = 'Basement external walls moisture protection.' WHERE id = 'fa176e67-923a-4547-a53d-9bddb308a42e';
UPDATE renovation_template SET renovation_measure_description = 'District heating connection.' WHERE id = 'a0b1c2d3-e4f5-4a6b-7c8d-9e0f1a2b3c4d';
UPDATE renovation_template SET renovation_measure_description = 'Ground to water heat pump installation.' WHERE id = 'b1c2d3e4-f5a6-4b7c-8d9e-0f1a2b3c4d5e';
UPDATE renovation_template SET renovation_measure_description = 'Biomass heating system installation.' WHERE id = 'c2d3e4f5-a6b7-4c8d-9e0f-1a2b3c4d5e6f';
UPDATE renovation_template SET renovation_measure_description = 'Air to water heat pump installation.' WHERE id = 'd3e4f5a6-b7c8-4d9e-0f1a-2b3c4d5e6f7a';
UPDATE renovation_template SET renovation_measure_description = 'Ventilation system with heat exchanger' WHERE id = 'e4f5a6b7-c8d9-4e0f-1a2b-3c4d5e6f7a8b';

UPDATE renovation_template
SET renovation_measure_description = 'A comprehensive wet underfloor heating system will be integrated throughout all interior rooms. The installation includes laying water-carrying pipes. The scope also includes removing old and applying new flooring.'
WHERE id = 'f5a6b7c8-d9e0-4f1a-2b3c-4d5e6f7a8b9c';

UPDATE renovation_template
SET renovation_measure_description = 'This renovation involves installing a dry underfloor heating system across all interior rooms. The system features prefabricated electric heating elements without requiring a screed layer. The scope also includes applying new flooring.'
WHERE id = 'a6b7c8d9-e0f1-4a2b-3c4d-5e6f7a8b9c0d';

UPDATE renovation_template
SET renovation_measure_description = 'Dry underfloor heating elements will be added beneath the surface of internal floors using lightweight, screed-free modules. This version excludes the supply and installation of final floor coverings.'
WHERE id = 'b7c8d9e0-f1a2-4b3c-4d5e-6f7a8b9c0d1e';

UPDATE renovation_template SET renovation_measure_description = 'Ground floor insulation with 4 CM thick aerogel.' WHERE id = 'df9c0c58-8fd2-49e7-8b73-f8581a96f54d';

UPDATE renovation_template
SET renovation_measure_description = 'Installation of smart heating control systems, including app-connected thermostats, room-by-room temperature zoning, and adaptive heating schedules.'
WHERE id = 'e5f6a7b8-c9d1-4e2f-3a4b-5c6d7e8f9a0b';

UPDATE renovation_template
SET renovation_measure_description = 'Implementation of smart lighting automation, including motion sensors, dimmable LED fixtures, and app- or voice-controlled switches.'
WHERE id = 'f6a7b8c9-d1e2-4f3a-4b5c-6d7e8f9a0b1c';

UPDATE renovation_template SET renovation_measure_description = 'Lithium-ion battery storage for solar panels (solar panels installation is not included).' WHERE id = 'a1b2c3d4-e5f6-4a7b-8c9d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_description = 'Buffer storage and immersion heater for solar panels (solar panels installation is not included).' WHERE id = 'c3d4e5f6-a7b8-4c9d-1e2f-3a4b5c6d7e8f';
UPDATE renovation_template SET renovation_measure_description = 'Buffer storage without immersion heater for solar panels (solar panels installation is not included).' WHERE id = 'd4e5f6a7-b8c9-4d1e-2f3a-4b5c6d7e8f9a';

UPDATE renovation_template
SET renovation_measure_description = 'Complete transformation of the existing bathroom into a barrier-free, age-appropriate space. This includes replacing the bathtub with a walk-in shower, installing grab bars, a height-adjustable sink, and a raised toilet seat to ensure safety and comfort.'
WHERE id = 'd1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a';

UPDATE renovation_template
SET renovation_measure_description = 'Installation of a motorized stairlift system along an indoor staircase to provide safe and independent access between floors for elderly or mobility-impaired residents. Includes electrical setup, track mounting, and user controls.'
WHERE id = 'e2f3a4b5-c6d7-4e8f-9a0b-1c2d3e4f5a6b';

UPDATE renovation_template
SET renovation_measure_description = 'Structural modification of all existing door frames and hinges to increase doorway width, allowing unobstructed access for wheelchairs, walkers, or other mobility aids. Includes removal and replacement of affected doors.'
WHERE id = 'f3a4b5c6-d7e8-4f9a-0b1c-2d3e4f5a6b7c';

UPDATE renovation_template
SET renovation_measure_description = 'Installation of emergency response systems in living areas and bathrooms, such as wall-mounted call buttons, wearable panic alarms, and voice-activated emergency services.'
WHERE id = 'a4b5c6d7-e8f9-4a0b-1c2d-3e4f5a6b7c8d';
