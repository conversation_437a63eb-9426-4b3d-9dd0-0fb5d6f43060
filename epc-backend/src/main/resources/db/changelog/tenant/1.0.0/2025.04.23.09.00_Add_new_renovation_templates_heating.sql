insert into renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)

VALUES -- Heating System
       ('a0b1c2d3-e4f5-4a6b-7c8d-9e0f1a2b3c4d', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "DISTRICT", "hotWaterEnergySource": "DISTRICT", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 45, 'HEATING_PRIMARY_SOURCE', 'DISTRICT_HEATING_2025', 2,
        'DEFAULT', 'District heating connection (2025)'),
       ('b1c2d3e4-f5a6-4b7c-8d9e-0f1a2b3c4d5e', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "HEAT_PUMP", "hotWaterEnergySource": "HEAT_PUMP", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 46, 'HEATING_PRIMARY_SOURCE',
        'GROUND_TO_WATER_HEAT_PUMP_2025', 2, 'DEFAULT', 'Ground to water heat pump installation (2025)'),
       ('c2d3e4f5-a6b7-4c8d-9e0f-1a2b3c4d5e6f', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "BIOMASS", "hotWaterEnergySource": "BIOMASS", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 47, 'HEATING_PRIMARY_SOURCE', 'BIOMASS_HEATING_2025', 2,
        'DEFAULT', 'Biomass heating system installation (2025)'),
       ('d3e4f5a6-b7c8-4d9e-0f1a-2b3c4d5e6f7a', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "HEAT_PUMP", "hotWaterEnergySource": "HEAT_PUMP", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 48, 'HEATING_PRIMARY_SOURCE',
        'AIR_TO_WATER_HEAT_PUMP_2025', 2, 'DEFAULT', 'Air to water heat pump installation (2025)'),
       ('e4f5a6b7-c8d9-4e0f-1a2b-3c4d5e6f7a8b', 'HEATING_SYSTEM', '{"ventHeatExchangeInstalled": true}', 'Migration',
        CURRENT_TIMESTAMP, null, null, 49, 'HEATING_VENTILATION_HEAT_EXCHANGE', 'HEAT_EXCHANGER', 2,
        'DEFAULT', 'Ventilation system with heat exchanger'),

       -- Underfloor Heating Installation
       ('f5a6b7c8-d9e0-4f1a-2b3c-4d5e6f7a8b9c', 'UNDERFLOOR_HEATING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 50, 'WET_SYSTEM_WITH_SCREED',
        'DEFAULT', 2, 'DEFAULT', 'Wet system incl floor screed and flooring'),
       ('a6b7c8d9-e0f1-4a2b-3c4d-5e6f7a8b9c0d', 'UNDERFLOOR_HEATING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 51, 'DRY_SYSTEM_WITH_FLOORING',
        'DEFAULT', 2, 'DEFAULT', 'Dry system incl. new flooring'),
       ('b7c8d9e0-f1a2-4b3c-4d5e-6f7a8b9c0d1e', 'UNDERFLOOR_HEATING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 52, 'DRY_SYSTEM_WITHOUT_FLOORING',
        'DEFAULT', 2, 'DEFAULT', 'Dry systems excl. new flooring'),

       -- Wall Heating
       ('c8d9e0f1-a2b3-4c4d-5e6f-7a8b9c0d1e2f', 'WALL_HEATING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 53, 'WET_SYSTEM_WITH_RECONSTRUCTION',
        'DEFAULT', 2, 'DEFAULT', 'Wet system incl. wall & paint reconstruction'),
       ('d9e0f1a2-b3c4-4d5e-6f7a-8b9c0d1e2f3a', 'WALL_HEATING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 54, 'DRY_SYSTEM_WITH_RECONSTRUCTION',
        'DEFAULT', 2, 'DEFAULT', 'Dry system incl. wall & paint reconstruction'),

       -- Ceiling Heating
       ('e0f1a2b3-c4d5-4e6f-7a8b-9c0d1e2f3a4b', 'CEILING_HEATING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 55, 'WET_SYSTEM_WITH_RECONSTRUCTION',
        'DEFAULT', 2, 'DEFAULT', 'Wet system incl. ceiling & paint reconstruction'),
       ('f1a2b3c4-d5e6-4f7a-8b9c-0d1e2f3a4b5c', 'CEILING_HEATING',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 56, 'DRY_SYSTEM_WITH_RECONSTRUCTION',
        'DEFAULT', 2, 'DEFAULT', 'Dry system incl. ceiling & paint reconstruction'),

       -- Radiator Replacement / Upgrade
       ('a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d', 'RADIATOR_UPGRADE',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 57, 'BASIC_EFFICIENCY',
        'DEFAULT', 2, 'DEFAULT', 'Basic Efficiency'),
       ('b3c4d5e6-f7a8-4b9c-0d1e-2f3a4b5c6d7e', 'RADIATOR_UPGRADE',
        '{}', 'Migration', CURRENT_TIMESTAMP, null, null, 58, 'HIGH_PERFORMANCE',
        'DEFAULT', 2, 'DEFAULT', 'High Performance Radiators')
