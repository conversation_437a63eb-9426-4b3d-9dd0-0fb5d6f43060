-- First, delete renovation costs related to saltwater renovation
DELETE FROM renovation_cost
WHERE renovation_id IN (
    SELECT r.id FROM renovation r
    JOIN renovation_template rt ON r.renovation_template_id = rt.id
    WHERE rt.id = 'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e'
);

-- Then, delete related records in renovation table
DELETE FROM renovation
WHERE renovation_template_id  = 'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e';

-- Next, delete related records in renovation_template_grant_program table
DELETE FROM renovation_template_grant_program
WHERE renovation_template_id =  'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e';

-- Finally, delete the renovation_templates of saltwater battery renovation
DELETE FROM renovation_template
WHERE id = 'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e';


