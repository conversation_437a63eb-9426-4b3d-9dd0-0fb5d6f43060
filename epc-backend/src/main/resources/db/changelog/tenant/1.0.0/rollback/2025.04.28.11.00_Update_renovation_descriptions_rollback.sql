-- Rollback fix radiator templates by adding BASIC and ADVANCED variants

UPDATE renovation_template SET renovation_measure_variant = 'DEFAULT'
WHERE id = 'a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_variant = 'DEFAULT'
WHERE id = 'b3c4d5e6-f7a8-4b9c-0d1e-2f3a4b5c6d7e';


-- Rollback renovation_measure_description updates for specific renovation_template IDs
-- Facade related
UPDATE renovation_template SET renovation_measure_description = 'Standard FACADE EXTERNAL WALL INSULATION' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d471';
UPDATE renovation_template SET renovation_measure_description = 'Standard FACADE EXTERNAL WALL INSULATION' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d472';
UPDATE renovation_template SET renovation_measure_description = 'Standard FACADE EXTERNAL WALL INSULATION' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d473';
UPDATE renovation_template SET renovation_measure_description = 'Standard FACADE EXTERNAL WALL INSULATION' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d475';
UPDATE renovation_template SET renovation_measure_description = 'Standard FACADE EXTERNAL WALL INSULATION' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d474';
UPDATE renovation_template SET renovation_measure_description = 'Standard FACADE EXTERIOR CLADDING' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d476';

-- Roof related
UPDATE renovation_template SET renovation_measure_description = 'Standard CEILING INSULATION' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0101';
UPDATE renovation_template SET renovation_measure_description = 'Standard CEILING INSULATION' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0102';
UPDATE renovation_template SET renovation_measure_description = 'Roof insulation with vacuum insulated panels, 30cm thickness (2025)' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0104';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF NEW COVERAGE' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0105';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF NEW COVERAGE' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0106';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF NEW COVERAGE' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0971';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF NEW COVERAGE' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0107';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF NEW COVERAGE' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0109';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF NEW COVERAGE' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0110';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF NEW COVERAGE' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0111';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF RE ENFORCEMENT' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0112';
UPDATE renovation_template SET renovation_measure_description = 'Standard ROOF INSULATION' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0103';

-- Windows related
UPDATE renovation_template SET renovation_measure_description = 'Standard WINDOWS GLAZING TYPE' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e11';
UPDATE renovation_template SET renovation_measure_description = 'PVC triple glazed windows with low-E coating (2025)' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e12';
UPDATE renovation_template SET renovation_measure_description = 'Standard WINDOWS FRAME MATERIAL UPGRADE' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e13';
UPDATE renovation_template SET renovation_measure_description = 'Standard WINDOWS SHUTTERS' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e14';
UPDATE renovation_template SET renovation_measure_description = 'Standard WINDOWS SHUTTERS' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e15';
UPDATE renovation_template SET renovation_measure_description = 'Standard WINDOWS SECURITY FEATURES' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e16';

-- Basement related
UPDATE renovation_template SET renovation_measure_description = 'Ground floor insulation with extruded polystyrene, 10cm thickness' WHERE id = 'f326253b-8b8a-462c-9c05-e846ebc2a4d9';
UPDATE renovation_template SET renovation_measure_description = 'Standard BASEMENT INSULATION' WHERE id = '59789365-9ab2-438c-82af-a6ffd3594a48';
UPDATE renovation_template SET renovation_measure_description = 'Standard BASEMENT MOISTURE PROTECTION' WHERE id = 'fa176e67-923a-4547-a53d-9bddb308a42e';
UPDATE renovation_template SET renovation_measure_description = 'Standard GROUND FLOOR INSULATION' WHERE id = 'df9c0c58-8fd2-49e7-8b73-f8581a96f54d';
UPDATE renovation_template SET renovation_measure_description = 'Standard BASEMENT INSULATION' WHERE id = 'd4ce63cb-d17a-4e51-a916-ae7ac51b091d';

-- Door related
UPDATE renovation_template SET renovation_measure_description = 'Standard DOORS REPLACEMENT' WHERE id = 'f7052a59-59b5-47b2-bc48-5aa7ebc1f5b8';
UPDATE renovation_template SET renovation_measure_description = 'Standard DOORS SECURITY FEATURES' WHERE id = '97371634-ee6d-4156-84cc-908549164529';

-- Heating system related
UPDATE renovation_template SET renovation_measure_description = 'District heating connection (2025)' WHERE id = 'a0b1c2d3-e4f5-4a6b-7c8d-9e0f1a2b3c4d';
UPDATE renovation_template SET renovation_measure_description = 'Ground to water heat pump installation (2025)' WHERE id = 'b1c2d3e4-f5a6-4b7c-8d9e-0f1a2b3c4d5e';
UPDATE renovation_template SET renovation_measure_description = 'Biomass heating system installation (2025)' WHERE id = 'c2d3e4f5-a6b7-4c8d-9e0f-1a2b3c4d5e6f';
UPDATE renovation_template SET renovation_measure_description = 'Air to water heat pump installation (2025)' WHERE id = 'd3e4f5a6-b7c8-4d9e-0f1a-2b3c4d5e6f7a';
UPDATE renovation_template SET renovation_measure_description = 'Ventilation system with heat exchanger' WHERE id = 'e4f5a6b7-c8d9-4e0f-1a2b-3c4d5e6f7a8b';

-- Underfloor heating related
UPDATE renovation_template SET renovation_measure_description = 'Wet system incl floor screed and flooring' WHERE id = 'f5a6b7c8-d9e0-4f1a-2b3c-4d5e6f7a8b9c';
UPDATE renovation_template SET renovation_measure_description = 'Dry system incl. new flooring' WHERE id = 'a6b7c8d9-e0f1-4a2b-3c4d-5e6f7a8b9c0d';
UPDATE renovation_template SET renovation_measure_description = 'Dry systems excl. new flooring' WHERE id = 'b7c8d9e0-f1a2-4b3c-4d5e-6f7a8b9c0d1e';

-- Wall heating related
UPDATE renovation_template SET renovation_measure_description = 'Wet system incl. wall & paint reconstruction' WHERE id = 'c8d9e0f1-a2b3-4c4d-5e6f-7a8b9c0d1e2f';
UPDATE renovation_template SET renovation_measure_description = 'Dry system incl. wall & paint reconstruction' WHERE id = 'd9e0f1a2-b3c4-4d5e-6f7a-8b9c0d1e2f3a';

-- Ceiling heating related
UPDATE renovation_template SET renovation_measure_description = 'Wet system incl. ceiling & paint reconstruction' WHERE id = 'e0f1a2b3-c4d5-4e6f-7a8b-9c0d1e2f3a4b';
UPDATE renovation_template SET renovation_measure_description = 'Dry system incl. ceiling & paint reconstruction' WHERE id = 'f1a2b3c4-d5e6-4f7a-8b9c-0d1e2f3a4b5c';

-- Radiator upgrade related
UPDATE renovation_template SET renovation_measure_description = 'Basic Efficiency' WHERE id = 'a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_description = 'High Performance Radiators' WHERE id = 'b3c4d5e6-f7a8-4b9c-0d1e-2f3a4b5c6d7e';

-- Solar panels related
UPDATE renovation_template SET renovation_measure_description = 'Battery storage - Lithium-ion' WHERE id = 'a1b2c3d4-e5f6-4a7b-8c9d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_description = 'Battery storage - Saltwater batteries' WHERE id = 'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e';
UPDATE renovation_template SET renovation_measure_description = 'Buffer storage with immersion heater' WHERE id = 'c3d4e5f6-a7b8-4c9d-1e2f-3a4b5c6d7e8f';
UPDATE renovation_template SET renovation_measure_description = 'Buffer storage without immersion heater' WHERE id = 'd4e5f6a7-b8c9-4d1e-2f3a-4b5c6d7e8f9a';

-- Smart home energy management related
UPDATE renovation_template SET renovation_measure_description = 'Intelligent heating controls' WHERE id = 'e5f6a7b8-c9d1-4e2f-3a4b-5c6d7e8f9a0b';
UPDATE renovation_template SET renovation_measure_description = 'Lighting automation' WHERE id = 'f6a7b8c9-d1e2-4f3a-4b5c-6d7e8f9a0b1c';
UPDATE renovation_template SET renovation_measure_description = 'Sensors' WHERE id = 'a7b8c9d1-e2f3-4a4b-5c6d-7e8f9a0b1c2d';

-- Electric vehicle charging related
UPDATE renovation_template SET renovation_measure_description = 'Wallbox installation' WHERE id = 'b8c9d1e2-f3a4-4b5c-6d7e-8f9a0b1c2d3e';
UPDATE renovation_template SET renovation_measure_description = 'Grid connection upgrade' WHERE id = 'c9d1e2f3-a4b5-4c6d-7e8f-9a0b1c2d3e4f';

-- Age appropriate living related
UPDATE renovation_template SET renovation_measure_description = 'Barrier-free bathroom' WHERE id = 'd1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a';
UPDATE renovation_template SET renovation_measure_description = 'Stairlift' WHERE id = 'e2f3a4b5-c6d7-4e8f-9a0b-1c2d3e4f5a6b';
UPDATE renovation_template SET renovation_measure_description = 'Widening doors' WHERE id = 'f3a4b5c6-d7e8-4f9a-0b1c-2d3e4f5a6b7c';
UPDATE renovation_template SET renovation_measure_description = 'Emergency systems' WHERE id = 'a4b5c6d7-e8f9-4a0b-1c2d-3e4f5a6b7c8d';
