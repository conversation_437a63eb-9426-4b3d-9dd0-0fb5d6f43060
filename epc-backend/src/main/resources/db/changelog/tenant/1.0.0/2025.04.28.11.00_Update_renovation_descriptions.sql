-- Fix radiator templates by adding BASIC and ADVANCED variants
UPDATE renovation_template SET renovation_measure_variant = 'BASIC'
                        WHERE id = 'a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_variant = 'ADVANCED'
                        WHERE id = 'b3c4d5e6-f7a8-4b9c-0d1e-2f3a4b5c6d7e';


-- Update renovation_measure_description for specific renovation_template IDs
-- Facade related
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 10 CM thick mineral wool.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d471';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 15 CM thick wood fiber.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d472';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 10 CM thick extruded polystyrene.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d473';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 1 CM thick aerogel.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d475';
UPDATE renovation_template SET renovation_measure_description = 'Facade external wall insulation using 5 CM thick vacuum insulated panels.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d474';
UPDATE renovation_template SET renovation_measure_description = 'Facade exterior cladding using render (plaster) cladding.' WHERE id = 'f47ac10b-58cc-4372-a567-0e02b2c3d476';

-- Roof related
UPDATE renovation_template SET renovation_measure_description = 'Ceiling insulation using 30 CM thick fibreglass.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0101';
UPDATE renovation_template SET renovation_measure_description = 'Ceiling insulation using 5 CM thick vacuum insulated panels.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0102';
UPDATE renovation_template SET renovation_measure_description = 'Roof insulation using 5 CM thick vacuum insulated panels.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0104';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using clay tiles.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0105';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using natural slate.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0106';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using concrete tiles.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0971';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using clay tiles.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0107';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using metal tiles.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0109';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using bitumen membrane.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0110';
UPDATE renovation_template SET renovation_measure_description = 'Roof coverage using high quality plastic foil.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0111';
UPDATE renovation_template SET renovation_measure_description = 'Roof reinforcement renewal.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0112';
UPDATE renovation_template SET renovation_measure_description = 'Roof insulation using 30 CM thick fiberglass.' WHERE id = 'c9bf9e57-1685-4c89-a004-2b2a2a3c0103';

-- Windows related
UPDATE renovation_template SET renovation_measure_description = 'Windows replacement using modern PVC double glazing windows.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e11';
UPDATE renovation_template SET renovation_measure_description = 'Windows replacement using modern PVC triple glazing windows.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e12';
UPDATE renovation_template SET renovation_measure_description = 'Standard WINDOWS FRAME MATERIAL UPGRADE' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e13'; -- They will be removed in separate branch so no change is done
UPDATE renovation_template SET renovation_measure_description = 'Installation of manual shutters.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e14';
UPDATE renovation_template SET renovation_measure_description = 'Installation of automatic shutters.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e15';
UPDATE renovation_template SET renovation_measure_description = 'Adding security features to windows.' WHERE id = '7815696e-30b2-4a90-9a9e-ed3b81c92e16';

-- Basement related
UPDATE renovation_template SET renovation_measure_description = 'Ground floor insulation with 10 CM thick extruded polystyrene.' WHERE id = 'f326253b-8b8a-462c-9c05-e846ebc2a4d9';
UPDATE renovation_template SET renovation_measure_description = 'Basement floor and walls insulation with 10 CM thick extruded polystyrene.' WHERE id = '59789365-9ab2-438c-82af-a6ffd3594a48';
UPDATE renovation_template SET renovation_measure_description = 'Basement external walls moisture protection.' WHERE id = 'fa176e67-923a-4547-a53d-9bddb308a42e';
UPDATE renovation_template SET renovation_measure_description = 'Ground floor insulation with 1 CM thick aerogel.' WHERE id = 'df9c0c58-8fd2-49e7-8b73-f8581a96f54d';
UPDATE renovation_template SET renovation_measure_description = 'Basement floor and walls insulation with 1 CM thick aerogel.' WHERE id = 'd4ce63cb-d17a-4e51-a916-ae7ac51b091d';

-- Door related
UPDATE renovation_template SET renovation_measure_description = 'Door replacement using composite doors.' WHERE id = 'f7052a59-59b5-47b2-bc48-5aa7ebc1f5b8';
UPDATE renovation_template SET renovation_measure_description = 'Adding security features to door.' WHERE id = '97371634-ee6d-4156-84cc-908549164529';

-- Heating system related
UPDATE renovation_template SET renovation_measure_description = 'District heating connection.' WHERE id = 'a0b1c2d3-e4f5-4a6b-7c8d-9e0f1a2b3c4d';
UPDATE renovation_template SET renovation_measure_description = 'Ground to water heat pump installation.' WHERE id = 'b1c2d3e4-f5a6-4b7c-8d9e-0f1a2b3c4d5e';
UPDATE renovation_template SET renovation_measure_description = 'Biomass heating system installation.' WHERE id = 'c2d3e4f5-a6b7-4c8d-9e0f-1a2b3c4d5e6f';
UPDATE renovation_template SET renovation_measure_description = 'Air to water heat pump installation.' WHERE id = 'd3e4f5a6-b7c8-4d9e-0f1a-2b3c4d5e6f7a';
UPDATE renovation_template SET renovation_measure_description = 'Ventilation system with heat exchanger' WHERE id = 'e4f5a6b7-c8d9-4e0f-1a2b-3c4d5e6f7a8b';

-- Underfloor heating related
UPDATE renovation_template SET renovation_measure_description = 'Underfloor heating system. Wet system incl floor screed and flooring.' WHERE id = 'f5a6b7c8-d9e0-4f1a-2b3c-4d5e6f7a8b9c';
UPDATE renovation_template SET renovation_measure_description = 'Underfloor heating system. Dry system incl. new flooring' WHERE id = 'a6b7c8d9-e0f1-4a2b-3c4d-5e6f7a8b9c0d';
UPDATE renovation_template SET renovation_measure_description = 'Underfloor heating system. Dry systems excl. new flooring' WHERE id = 'b7c8d9e0-f1a2-4b3c-4d5e-6f7a8b9c0d1e';

-- Wall heating related
UPDATE renovation_template SET renovation_measure_description = 'Wall heating system. Wall heating system. Wet system incl. wall & paint reconstruction' WHERE id = 'c8d9e0f1-a2b3-4c4d-5e6f-7a8b9c0d1e2f';
UPDATE renovation_template SET renovation_measure_description = 'Wall heating system. Dry system incl. wall & paint reconstruction' WHERE id = 'd9e0f1a2-b3c4-4d5e-6f7a-8b9c0d1e2f3a';

-- Ceiling heating related
UPDATE renovation_template SET renovation_measure_description = 'Ceiling heating system. Wet system incl. ceiling & paint reconstruction' WHERE id = 'e0f1a2b3-c4d5-4e6f-7a8b-9c0d1e2f3a4b';
UPDATE renovation_template SET renovation_measure_description = 'Ceiling heating system. Dry system incl. ceiling & paint reconstruction' WHERE id = 'f1a2b3c4-d5e6-4f7a-8b9c-0d1e2f3a4b5c';

-- Radiator upgrade related
UPDATE renovation_template SET renovation_measure_description = 'Radiators upgrade. Basic Efficiency radiators.' WHERE id = 'a2b3c4d5-e6f7-4a8b-9c0d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_description = 'Radiators upgrade. High Performance radiators.' WHERE id = 'b3c4d5e6-f7a8-4b9c-0d1e-2f3a4b5c6d7e';

-- Solar panels related
UPDATE renovation_template SET renovation_measure_description = 'Solar panels system with Lithium-ion battery storage.' WHERE id = 'a1b2c3d4-e5f6-4a7b-8c9d-1e2f3a4b5c6d';
UPDATE renovation_template SET renovation_measure_description = 'Solar panels system with Saltwater battery storage.' WHERE id = 'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e';
UPDATE renovation_template SET renovation_measure_description = 'Solar panels system with buffer storage and immersion heater.' WHERE id = 'c3d4e5f6-a7b8-4c9d-1e2f-3a4b5c6d7e8f';
UPDATE renovation_template SET renovation_measure_description = 'Solar panels system with buffer storage without immersion heater.' WHERE id = 'd4e5f6a7-b8c9-4d1e-2f3a-4b5c6d7e8f9a';

-- Smart home energy management related
UPDATE renovation_template SET renovation_measure_description = 'Smart home installation - intelligent heating control.s' WHERE id = 'e5f6a7b8-c9d1-4e2f-3a4b-5c6d7e8f9a0b';
UPDATE renovation_template SET renovation_measure_description = 'Smart home installation - lighting automation.' WHERE id = 'f6a7b8c9-d1e2-4f3a-4b5c-6d7e8f9a0b1c';
UPDATE renovation_template SET renovation_measure_description = 'Sensors' WHERE id = 'a7b8c9d1-e2f3-4a4b-5c6d-7e8f9a0b1c2d'; -- They will be removed in separate branch so no change is done

-- Electric vehicle charging related
UPDATE renovation_template SET renovation_measure_description = 'EV charging station - wallbox installation.' WHERE id = 'b8c9d1e2-f3a4-4b5c-6d7e-8f9a0b1c2d3e';
UPDATE renovation_template SET renovation_measure_description = 'EV charging station - grid connection upgrade.' WHERE id = 'c9d1e2f3-a4b5-4c6d-7e8f-9a0b1c2d3e4f';

-- Age appropriate living related
UPDATE renovation_template SET renovation_measure_description = 'Age-appropriate renovations - barrier-free bathroom.' WHERE id = 'd1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a';
UPDATE renovation_template SET renovation_measure_description = 'Age-appropriate renovations - stairlift.' WHERE id = 'e2f3a4b5-c6d7-4e8f-9a0b-1c2d3e4f5a6b';
UPDATE renovation_template SET renovation_measure_description = 'Age-appropriate renovations - widening doors.' WHERE id = 'f3a4b5c6-d7e8-4f9a-0b1c-2d3e4f5a6b7c';
UPDATE renovation_template SET renovation_measure_description = 'Age-appropriate renovations - emergency systems.' WHERE id = 'a4b5c6d7-e8f9-4a0b-1c2d-3e4f5a6b7c8d';
