-- Rollback for 2025.04.24.21.00_Add_new_renovation_templates_additional.sql

-- Delete all new renovation templates added in the migration
DELETE FROM renovation_template WHERE id IN (
    'a1b2c3d4-e5f6-4a7b-8c9d-1e2f3a4b5c6d',
    'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e',
    'c3d4e5f6-a7b8-4c9d-1e2f-3a4b5c6d7e8f',
    'd4e5f6a7-b8c9-4d1e-2f3a-4b5c6d7e8f9a',
    'e5f6a7b8-c9d1-4e2f-3a4b-5c6d7e8f9a0b',
    'f6a7b8c9-d1e2-4f3a-4b5c-6d7e8f9a0b1c',
    'a7b8c9d1-e2f3-4a4b-5c6d-7e8f9a0b1c2d',
    'b8c9d1e2-f3a4-4b5c-6d7e-8f9a0b1c2d3e',
    'c9d1e2f3-a4b5-4c6d-7e8f-9a0b1c2d3e4f',
    'd1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a',
    'e2f3a4b5-c6d7-4e8f-9a0b-1c2d3e4f5a6b',
    'f3a4b5c6-d7e8-4f9a-0b1c-2d3e4f5a6b7c',
    'a4b5c6d7-e8f9-4a0b-1c2d-3e4f5a6b7c8d'
);

UPDATE renovation_template
SET renovation_measure_object = '{"solarPlantExist": true, "solarPlantInstallationYear": 2025}'
WHERE id = '4d406150-894b-4b07-85b1-830e4254a430'; -- Old renovation

update renovation_template
set renovation_measure_category = 'SOLAR_PLANT'
where renovation_measure_category = 'SOLAR_PANELS';
