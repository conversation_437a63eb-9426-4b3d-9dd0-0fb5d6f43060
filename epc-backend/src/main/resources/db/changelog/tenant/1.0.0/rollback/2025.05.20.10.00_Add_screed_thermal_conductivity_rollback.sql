-- Rollback for 2025.05.20.10.00_Add_screed_thermal_conductivity.sql

-- Delete the screed thermal conductivity values added in the migration
DELETE FROM construction_material_properties
WHERE type = 'SCREED';

-- Delete the floor finish thermal conductivity values added in the migration
DELETE FROM construction_material_properties
WHERE type = 'FLOOR_FINISH';

-- Add back the columns that were dropped
ALTER TABLE building_missing_attributes_generated
    ADD COLUMN ground_floor_thickness INT,
    ADD COLUMN ground_floor_type VARCHAR(255),
    ADD COLUMN basement_floor_thickness INT,
    ADD COLUMN basement_floor_type VARCHAR(255);

-- Update the columns with default values
UPDATE building_missing_attributes_generated
SET ground_floor_thickness = 10,
    ground_floor_type = 'CONCRETE',
    basement_floor_thickness = 10,
    basement_floor_type = 'CONCRETE';

-- Make the columns NOT NULL
ALTER TABLE building_missing_attributes_generated
    ALTER COLUMN ground_floor_thickness SET NOT NULL,
    ALTER COLUMN ground_floor_type SET NOT NULL,
    ALTER COLUMN basement_floor_thickness SET NOT NULL,
    ALTER COLUMN basement_floor_type SET NOT NULL;
