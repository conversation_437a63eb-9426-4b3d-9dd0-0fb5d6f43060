-- Add the renovation_measure_version column back
ALTER TABLE renovation_template
    ADD COLUMN renovation_measure_version INT;

-- Update all existing entries to set renovation_measure_version = 2
UPDATE renovation_template
SET renovation_measure_version = 2;

-- Make the column not null
ALTER TABLE renovation_template
    ALTER COLUMN renovation_measure_version SET NOT NULL;

-- Insert the old templates back (V1)
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('07d0adda-e9b0-4c7b-8fd9-9d85067d37bc', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "HEAT_PUMP", "hotWaterEnergySource": "HEAT_PUMP", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 66, 'HEATING_PRIMARY_SOURCE',
        'AIR_TO_WATER_HEAT_PUMP_2025', 1, 'DEFAULT', 'Air to water heat pump installation (2025)');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('9777c561-0f9f-4fba-ae21-00fca51d1e05', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "BIOMASS", "hotWaterEnergySource": "BIOMASS", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 69, 'HEATING_PRIMARY_SOURCE', 'BIOMASS_HEATING_2025', 1,
        'DEFAULT', 'Biomass heating system installation (2025)');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('7ad7708c-b164-4e64-8b41-d641262d71ee', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "DISTRICT", "hotWaterEnergySource": "DISTRICT", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 64, 'HEATING_PRIMARY_SOURCE', 'DISTRICT_HEATING_2025', 1,
        'DEFAULT', 'District heating connection (2025)');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('cb32c9c4-18ac-44b4-a8f3-aa1c58c2c4f3', 'FACADE',
        '{"facadeWallInsulationType": "EXPANDED_POLYSTYRENE", "facadeWallInsulationYear": 2025, "facadeWallInsulationRenewed": true, "facadeWallInsulationThickness": 30}',
        'Migration', CURRENT_TIMESTAMP, null, null, 60, 'FACADE_EXTERNAL_WALL_INSULATION',
        'EXPANDED_POLYSTYRENE_30_CM_2025', 1, 'DEFAULT',
        'Facade insulation with expanded polystyrene, 30cm thickness (2025). Price includes material, labor, removal of old cladding, disposal, and minor surface prep—no major repairs. Apply typical German market price per sqm for this insulation.');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('9059efb1-b22c-4758-8626-b075409fefc5', 'BASEMENT',
        '{"groundFloorInsulationType": "EXTRUDED_POLYSTYRENE", "groundFloorInsulationYear": 2025, "groundFloorInsulationThickness": 10}',
        'Migration', CURRENT_TIMESTAMP, null, null, 63, 'GROUND_FLOOR_INSULATION',
        'EXTRUDED_POLYSTYRENE_10_CM_2025', 1, 'DEFAULT',
        'Ground floor insulation with extruded polystyrene, 10cm thickness');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('d3bd7b8e-6384-472f-a83a-c4c2b1e7d542', 'ROOF',
        '{"ceilingWallInsulationType": "EXTRUDED_POLYSTYRENE", "ceilingWallInsulationThickness": 10}', 'Migration',
        CURRENT_TIMESTAMP, null, null, 62, 'CEILING_INSULATION', 'EXTRUDED_POLYSTYRENE_10_CM_2025', 1,
        'DEFAULT', 'Ceiling insulation with extruded polystyrene, 10cm thickness');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('837f3dd1-e2a0-4d4b-9879-b998aeac1575', 'HEATING_SYSTEM',
        '{"heatingEnergySource": "HEAT_PUMP", "hotWaterEnergySource": "HEAT_PUMP", "heatingInstallationYear": "2025"}',
        'Migration', CURRENT_TIMESTAMP, null, null, 67, 'HEATING_PRIMARY_SOURCE',
        'GROUND_TO_WATER_HEAT_PUMP_2025', 1, 'DEFAULT', 'Ground to water heat pump installation (2025)');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('f148dc00-114b-4a09-80b7-b643286bfa54', 'HEATING_SYSTEM', '{"ventHeatExchangeInstalled": true}', 'Migration',
        CURRENT_TIMESTAMP, null, null, 65, 'HEATING_VENTILATION_HEAT_EXCHANGE', 'HEAT_EXCHANGER', 1,
        'DEFAULT', 'Ventilation system with heat exchanger');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('42d236f2-38f2-480b-9ed4-d4abc2bd7e17', 'WINDOWS',
        '{"windowsGlazing": "TRIPLE", "windowsRenewed": true, "windowsCoatingType": "LOW_E_COATING", "windowsInstallationYear": 2025}',
        'Migration', CURRENT_TIMESTAMP, null, null, 59, 'WINDOWS_GLAZING_TYPE',
        'PVC_TRIPLE_GLAZING_LOW_E_COATING_2025', 1, 'DEFAULT', 'PVC triple glazed windows with low-E coating (2025)');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('4d406150-894b-4b07-85b1-830e4254a430', 'SOLAR_PANELS', '{"installationYear": 2025, "solarPanelsExist": true}',
        'Migration', CURRENT_TIMESTAMP, null, null, 58, 'SOLAR_PLANT_PANELS', 'SOLAR_PANELS_2025', 1,
        'DEFAULT', 'Solar PV panels installation (2025)');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('c19756c0-6b1a-475e-ae9e-247c9e2d1b51', 'SOLAR_THERMAL', '{"solarThermalExist": true}', 'Migration',
        CURRENT_TIMESTAMP, null, null, 68, 'SOLAR_THERMAL', 'SOLAR_THERMAL_2025', 1, 'DEFAULT',
        'Solar thermal collectors installation (2025)');
INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by, creation_date,
                                 last_modified_by, last_modified_date, list_order, renovation_measure_type,
                                 renovation_measure_value, renovation_measure_version, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('b7fafcc3-2136-4248-b822-11bcd0635792', 'ROOF',
        '{"roofInsulationType": "VACUUM_INSULATED_PANEL", "roofInsulationYear": 2025, "roofInsulationRenewed": true, "roofInsulationThickness": 30}',
        'Migration', CURRENT_TIMESTAMP, null, null, 61, 'ROOF_INSULATION',
        'VACUUM_INSULATED_PANEL_30_CM_2025', 1, 'DEFAULT',
        'Roof insulation with vacuum insulated panels, 30cm thickness (2025)');

-- Insert grant program relations for V1 renovation templates
UPDATE renovation_template_grant_program
SET renovation_template_id = '4d406150-894b-4b07-85b1-830e4254a430',
    grant_program_id       = 'b7445524-9512-44f8-a47b-f35fa2206d11',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '9fe1568c-23ef-440b-9050-2dcc467de4b5';
UPDATE renovation_template_grant_program
SET renovation_template_id = 'cb32c9c4-18ac-44b4-a8f3-aa1c58c2c4f3',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '6b4f9d82-3f31-431f-9e48-1564eaf6b59d';
UPDATE renovation_template_grant_program
SET renovation_template_id = '9059efb1-b22c-4758-8626-b075409fefc5',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '65a3a2ff-2055-49a6-8505-eea21643bae6';
UPDATE renovation_template_grant_program
SET renovation_template_id = 'b7fafcc3-2136-4248-b822-11bcd0635792',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = 'fe4f27d4-2dae-4a1f-a46f-c9c033e1a29c';
UPDATE renovation_template_grant_program
SET renovation_template_id = 'c19756c0-6b1a-475e-ae9e-247c9e2d1b51',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '1a696d0f-ae2b-4708-baa0-97f4f057ee92';
UPDATE renovation_template_grant_program
SET renovation_template_id = '42d236f2-38f2-480b-9ed4-d4abc2bd7e17',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = 'fa1a966d-361c-42c8-b792-82073c204b26';
UPDATE renovation_template_grant_program
SET renovation_template_id = 'f148dc00-114b-4a09-80b7-b643286bfa54',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '05d12bbc-c6b5-4dda-94bd-0229012eb8df';
UPDATE renovation_template_grant_program
SET renovation_template_id = 'd3bd7b8e-6384-472f-a83a-c4c2b1e7d542',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '0ea42881-9f9e-467d-9361-980683a3e7be';
UPDATE renovation_template_grant_program
SET renovation_template_id = '07d0adda-e9b0-4c7b-8fd9-9d85067d37bc',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '23fa83be-3c60-43e7-a32d-8b4685aab6b0';
UPDATE renovation_template_grant_program
SET renovation_template_id = '837f3dd1-e2a0-4d4b-9879-b998aeac1575',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = 'fce197c0-bdba-412f-a75b-71a6d8610b7b';
UPDATE renovation_template_grant_program
SET renovation_template_id = '7ad7708c-b164-4e64-8b41-d641262d71ee',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = 'bac7fb3a-34cc-49cc-bd55-d38e54155c34';
UPDATE renovation_template_grant_program
SET renovation_template_id = '9777c561-0f9f-4fba-ae21-00fca51d1e05',
    grant_program_id       = '64ce949c-6faa-4dd2-af35-6cd04ca3848c',
    created_by             = 'Migration',
    creation_date          = CURRENT_TIMESTAMP,
    last_modified_by       = null,
    last_modified_date     = null
WHERE id = '9fdb87b9-2126-4364-a0fd-0e8a430be2b5';
