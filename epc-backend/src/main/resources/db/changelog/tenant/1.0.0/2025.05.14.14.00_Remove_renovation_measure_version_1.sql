-- First, delete related records in renovation_cost table
DELETE FROM renovation_cost
WHERE renovation_id IN (
    SELECT r.id FROM renovation r
    JOIN renovation_template rt ON r.renovation_template_id = rt.id
    WHERE rt.renovation_measure_version = 1
);

-- Then, delete related records in renovation table
DELETE FROM renovation
WHERE renovation_template_id IN (
    SELECT id FROM renovation_template
    WHERE renovation_measure_version = 1
);

-- Next, delete related records in renovation_template_grant_program table
DELETE FROM renovation_template_grant_program
WHERE renovation_template_id IN (
    SELECT id FROM renovation_template
    WHERE renovation_measure_version = 1
);

-- Finally, delete the renovation_templates with renovation_measure_version = 1
DELETE FROM renovation_template
WHERE renovation_measure_version = 1;

-- Drop the renovation_measure_version column
ALTER TABLE renovation_template DROP COLUMN renovation_measure_version;
