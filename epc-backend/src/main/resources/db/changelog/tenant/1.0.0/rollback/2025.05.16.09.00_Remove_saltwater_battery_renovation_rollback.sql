INSERT INTO renovation_template (id, renovation_measure_category, renovation_measure_object, created_by,
                                 creation_date, last_modified_by, last_modified_date, list_order,
                                 renovation_measure_type, renovation_measure_value, renovation_measure_variant,
                                 renovation_measure_description)
VALUES ('b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e', 'SOLAR_PANELS', '{"batteryType": "SALTWATER"}', 'Migration',
        CURRENT_TIMESTAMP, null, null, 46, 'BATTERY_STORAGE', 'SALTWATER_BATTERY', 'DEFAULT',
        'Saltwater battery storage for solar panels (solar panels installation is not included).');


INSERT INTO renovation_template_grant_program (id, renovation_template_id, grant_program_id, created_by,
                                                       creation_date, last_modified_by, last_modified_date)
VALUES ('d978b276-9e3d-4d4d-96e1-9ddaa4de94e3', 'b2c3d4e5-f6a7-4b8c-9d1e-2f3a4b5c6d7e',
        'b7445524-9512-44f8-a47b-f35fa2206d11', 'Migration', CURRENT_TIMESTAMP, null, null);
