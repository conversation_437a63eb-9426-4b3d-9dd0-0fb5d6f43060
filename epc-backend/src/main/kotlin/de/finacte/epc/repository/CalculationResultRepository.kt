package de.finacte.epc.repository

import de.finacte.epc.entity.CalculationResultListItemProjection
import de.finacte.epc.entity.CalculationResultEntity
import de.finacte.epc.entity.CalculationResultCategory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository
import java.util.UUID


@Repository
interface CalculationResultRepository : CrudRepository<CalculationResultEntity, UUID> {
    fun findByBuildingId(buildingId: UUID): List<CalculationResultEntity>
    fun findByBuildingIdAndCategory(buildingId: UUID, category: CalculationResultCategory): CalculationResultEntity?

    @Query(
        """
            select 
            building.id as id,
            building.street as street,
            building.no as no,
            building.zipCode as zipCode,
            building.city as city,
            building.creationDate as creationDate,
            current.energyEfficiency as currentEnergyEfficiency,
            current.energyEfficiencyUnit as currentEnergyEfficiencyUnit,
            current.energyEfficiencyScaleGrade as currentEnergyEfficiencyScaleGrade,
            alternative.energyEfficiency as alternativeEnergyEfficiency,
            alternative.energyEfficiencyUnit as alternativeEnergyEfficiencyUnit,
            alternative.energyEfficiencyScaleGrade as alternativeEnergyEfficiencyScaleGrade
        from BuildingEntity building
        left join CalculationResultEntity current with current.buildingId = building.id and
             current.category = 'CURRENT'
        left join CalculationResultEntity alternative with alternative.buildingId = building.id and
             alternative.category = 'ALTERNATIVE'
        where building.creationDate = (
                select max(sub.creationDate) 
                from BuildingEntity sub 
                where sub.addressKey = building.addressKey
            )
        and current.id is not null and (:search is null or
                                            (
                                             lower(building.street) like %:search% or 
                                             lower(building.no) like %:search% or 
                                             lower(building.zipCode) like %:search% or
                                             lower(building.city) like %:search%
                                            )
                                         )
         """
    )
    fun findAllCalculationResultListItems(search: String?, pageable: Pageable): Page<CalculationResultListItemProjection>
}
