package de.finacte.epc.repository.renovation

import de.finacte.epc.entity.renovation.RenovationEntity
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface RenovationRepository : CrudRepository<RenovationEntity, UUID> {
    @Query(
        """
        select r from RenovationEntity r 
            join fetch r.renovationTemplate as rt
            join fetch  r.renovationTemplate.grantPrograms 
        where r.buildingId = :buildingId and rt.renovationMeasureVersion = 1 order by r.creationDate
    """
    )
    fun findAllByBuildingIdEqualsOrderByCreationDate(buildingId: UUID): List<RenovationEntity>
    @Query(
        """
        select r from RenovationEntity r 
            join fetch r.renovationTemplate as rt
            join fetch  r.renovationTemplate.grantPrograms 
        where r.buildingId = :buildingId and rt.renovationMeasureVersion = 2 order by r.creationDate
    """
    )
    fun findAllByBuildingIdEqualsOrderByCreationDateV2(buildingId: UUID): List<RenovationEntity>
    fun findAllByBuildingId(buildingId: UUID): List<RenovationEntity>
}