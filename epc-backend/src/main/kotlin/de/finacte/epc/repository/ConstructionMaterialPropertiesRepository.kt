package de.finacte.epc.repository

import de.finacte.epc.config.CacheKey
import de.finacte.epc.entity.ConstructionMaterialPropertiesEntity
import de.finacte.epc.entity.ConstructionMaterialType
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ConstructionMaterialPropertiesRepository : JpaRepository<ConstructionMaterialPropertiesEntity, UUID> {
    @Cacheable(CacheKey.CONSTRUCTION_MATERIAL_PROPERTIES)
    fun findByNameAndType(name: String, type: ConstructionMaterialType): ConstructionMaterialPropertiesEntity?

    @Query("SELECT c FROM ConstructionMaterialPropertiesEntity c WHERE c.name = :name AND c.type = :type AND :thickness >= c.thicknessInMetersFrom AND :thickness < c.thicknessInMetersTo")
    fun findByNameAndTypeBetweenThickness(
        name: String,
        type: ConstructionMaterialType,
        thickness: Double
    ): ConstructionMaterialPropertiesEntity?
}