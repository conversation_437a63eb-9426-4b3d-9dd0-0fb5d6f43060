package de.finacte.epc.controller

import de.finacte.epc.dto.calculation.CalculationProfitabilityDto
import de.finacte.epc.dto.calculation.RenovationCostGenerationDto
import de.finacte.epc.dto.calculation.RenovationDto
import de.finacte.epc.service.calculation.renovation.RenovationService
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import java.util.*

@PreAuthorize("isAuthenticated()")
@Controller
@RequestMapping("/api/renovation")
class RenovationController(
    private val renovationService: RenovationService
) {
    companion object {
        private val log by LoggerDelegate()
    }

    @ResponseBody
    @PostMapping("/{buildingId}")
    fun generateRenovations(@PathVariable buildingId: UUID): List<RenovationDto> {
        log.info("Run calculation for $buildingId")
        return renovationService.generateRenovations(buildingId)
    }

    @ResponseBody
    @GetMapping("/{buildingId}")
    fun getRenovationMeasures(@PathVariable buildingId: UUID): List<RenovationDto> {
        log.info("Get renovation measures for building $buildingId")
        return renovationService.getRenovations(buildingId)
            .sortedBy { it.renovatedElementListOrder }
    }

    @ResponseBody
    @GetMapping("/{buildingId}/v2")
    fun getRenovationMeasuresV2(@PathVariable buildingId: UUID): List<RenovationDto> {
        log.info("Get renovation measures V2 for building $buildingId")
        return renovationService.getRenovationsV2(buildingId)
            .sortedBy { it.renovatedElementListOrder }
    }

    @ResponseBody
    @GetMapping("/{buildingId}/profitability")
    fun getProfitability(@PathVariable buildingId: UUID): CalculationProfitabilityDto {
        log.info("Get profitability for building $buildingId")
        return renovationService.getProfitability(buildingId)
    }

    @ResponseBody
    @PostMapping("/{buildingId}/costs")
    fun generateCosts(@PathVariable buildingId: UUID): List<RenovationCostGenerationDto> {
        log.info("Generate renovation costs for building $buildingId")
        return renovationService.generateCosts(buildingId)
    }

    @ResponseBody
    @GetMapping("/{buildingId}/costs")
    fun getCosts(@PathVariable buildingId: UUID): List<RenovationCostGenerationDto> {
        log.info("Get renovation costs for building $buildingId")
        return renovationService.getRenovationCosts(buildingId)
    }

}