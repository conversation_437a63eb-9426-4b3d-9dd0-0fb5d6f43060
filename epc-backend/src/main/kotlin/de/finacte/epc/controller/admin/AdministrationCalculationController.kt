package de.finacte.epc.controller.admin

import de.finacte.epc.dto.building.BuildingMissingAttributesGenerated
import de.finacte.epc.service.admin.AdministrationCalculationResultsService
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@PreAuthorize("hasAnyAuthority('ADMIN')")
@RestController
@RequestMapping("/api/administration/calculation")
class AdministrationCalculationController(
    val administrationCalculationResultsService: AdministrationCalculationResultsService
) {
    companion object {
        private val log by LoggerDelegate()
    }

    @ResponseBody
    @GetMapping("/building-missing-attributes/{buildingId}")
    fun getBuildingMissingAttributesGenerated(@PathVariable buildingId: UUID): BuildingMissingAttributesGenerated? {
        log.info("Getting building missing attributes generated for $buildingId")
        return administrationCalculationResultsService.getBuildingMissingAttributesGenerated(buildingId)
    }

    @ResponseBody
    @GetMapping("/climate-data/{zipCode}")
    fun getClimateData(@PathVariable zipCode: String): ClimateDataResult? {
        log.info("Getting climate data for $zipCode")
        return administrationCalculationResultsService.getClimateData(zipCode)
    }
}