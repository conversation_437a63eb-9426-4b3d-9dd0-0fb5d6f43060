package de.finacte.epc.controller

import de.finacte.epc.dto.integration.europace.CreateEuropaceCaseRequest
import de.finacte.epc.dto.integration.europace.EuropaceCaseDto
import de.finacte.epc.service.integration.europace.EuropaceIntegrationService
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import java.util.UUID

@PreAuthorize("isAuthenticated()")
@Controller
@RequestMapping("/api/europace")
class EuropaceIntegrationController(
    val europaceIntegrationService: EuropaceIntegrationService
) {
    companion object {
        private val log by LoggerDelegate()
    }

    @ResponseBody
    @PostMapping("/kundenangaben/{buildingId}")
    fun createKundenangaben(@PathVariable buildingId: UUID,
                            @RequestBody createEuropaceCaseRequest: CreateEuropaceCaseRequest
    ): EuropaceCaseDto {
        log.info("Creating Europace Case for building ID: {}", buildingId)
        return europaceIntegrationService.createKundenangaben(buildingId, createEuropaceCaseRequest.caseType)
    }

    @ResponseBody
    @GetMapping("/kundenangaben/{buildingId}")
    fun getKundenangabenCase(@PathVariable buildingId: UUID): EuropaceCaseDto {
        log.info("Get Europace Case for Building ID: {}", buildingId)
        return europaceIntegrationService.getKundenangabenCase(buildingId)
    }

    @ResponseBody
    @GetMapping("/kundenangaben/exist/{buildingId}")
    fun getKundenangabenCaseExist(@PathVariable buildingId: UUID): Boolean {
        log.info("Checking europace case existence for Building ID: {}", buildingId)
        return europaceIntegrationService.hasKundenangabenCaseExist(buildingId)
    }
}