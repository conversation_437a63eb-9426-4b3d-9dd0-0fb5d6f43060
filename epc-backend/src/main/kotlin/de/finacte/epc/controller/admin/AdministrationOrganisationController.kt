package de.finacte.epc.controller.admin

import de.finacte.epc.config.dto.OrganisationApiKeysDto
import de.finacte.epc.config.dto.OrganisationDto
import de.finacte.epc.config.multitenancy.AdministrationOrganisationService
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController

@PreAuthorize("hasAnyAuthority('ADMIN')")
@RestController
@RequestMapping("/api/administration/organisation")
class AdministrationOrganisationController(
    val administrationOrganisationService: AdministrationOrganisationService
) {
    companion object {
        private val log by LoggerDelegate()
    }

    @ResponseBody
    @GetMapping("/{businessId}")
    fun getOrganisation(@PathVariable businessId: String): OrganisationDto? {
        log.info("Get organisation with business ID: {}", businessId)
        return administrationOrganisationService.findOrganisation(businessId)
    }

    @ResponseBody
    @GetMapping
    fun getOrganisationList(): List<OrganisationDto> {
        log.info("Get organisation list")
        return administrationOrganisationService.getOrganisationList()
    }

    @ResponseBody
    @PostMapping
    fun createOrganisation(@RequestBody organisationDto: OrganisationDto): OrganisationDto {
        log.info("Creating organisation {}", organisationDto)
        return administrationOrganisationService.createOrganisation(organisationDto)
    }

    @ResponseBody
    @PatchMapping("/{businessId}")
    fun updateOrganisationApiKeys(
        @RequestBody organisationApiKeysDto: OrganisationApiKeysDto,
        @PathVariable businessId: String
    ): OrganisationDto {
        log.info("Updating organisation with business ID: {} API keys", businessId)
        return administrationOrganisationService.updateOrganisationApiKeys(businessId,organisationApiKeysDto)
    }

    @ResponseBody
    @DeleteMapping("/{businessId}")
    fun scheduleOrganisationRemoval(@PathVariable businessId: String): Boolean {
        log.info("Schedule organisation removal with business ID: {}", businessId)
        return administrationOrganisationService.scheduleDeleteOrganisation(businessId)
    }
}