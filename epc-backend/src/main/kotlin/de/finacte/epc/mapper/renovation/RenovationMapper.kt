package de.finacte.epc.mapper.renovation

import de.finacte.epc.dto.calculation.RenovationCostDto
import de.finacte.epc.dto.calculation.RenovationDto
import de.finacte.epc.dto.calculation.RenovationGrantDto
import de.finacte.epc.entity.Language
import de.finacte.epc.entity.renovation.GrantProgramEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.entity.renovation.RenovationTemplateTranslationEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import java.util.*

@Mapper(componentModel = "spring")
abstract class RenovationMapper {

    @Mapping(target = "cost", source = ".")
    @Mapping(
        target = "measure",
        source = "renovationTemplate.renovationTemplateTranslations",
        qualifiedByName = ["getMeasure"]
    )
    @Mapping(target = "measureDsc", source = "renovationTemplate", qualifiedByName = ["getMeasureDsc"])
    @Mapping(target = "renovatedElementListOrder", source = "renovationTemplate.listOrder", )
    @Mapping(target = "renovationMeasureCategory", source = "renovationTemplate.renovationMeasureCategory")
    @Mapping(target = "renovationMeasureType", source = "renovationTemplate.renovationMeasureType")
    @Mapping(target = "renovationMeasureVariant", source = "renovationTemplate.renovationMeasureVariant")
    @Mapping(
        target = "grantPrograms",
        source = "renovationTemplate.grantPrograms.",
        qualifiedByName = ["getGrantPrograms"]
    )
    abstract fun toDto(renovationEntity: RenovationEntity): RenovationDto

    fun getCost(renovationEntity: RenovationEntity): RenovationCostDto? {
         if (renovationEntity.renovationCost != null) {
            val costRange = Pair(renovationEntity.renovationCost.minCost, renovationEntity.renovationCost.maxCost)
             return RenovationCostDto(
                 range = costRange,
                 //TODO Workaround for now is to use max, in the future we can get back to most probable value
                 mostProbableValue = renovationEntity.renovationCost.maxCost
             )
        } else return null
    }

    @Named(value = "getGrantPrograms")
    fun getGrantPrograms(grantPrograms: List<GrantProgramEntity>): List<EnumMap<Language, RenovationGrantDto>> {
        return grantPrograms.map {
            it.grantProgramTranslation.associateTo(EnumMap(Language::class.java)) { translation ->
                translation.lang to RenovationGrantDto(
                    name = translation.name,
                    description = translation.description,
                    link = it.link,
                    type = it.type,
                )
            }
        }
    }

    @Named("getMeasure")
    fun getMeasure(renovationTemplateTranslations: List<RenovationTemplateTranslationEntity>): EnumMap<Language, String> {
        return renovationTemplateTranslations.associateTo(EnumMap(Language::class.java))
        { it.lang to it.measure }
    }

    @Named("getMeasureDsc")
    fun getMeasureDsc(renovationTemplate: RenovationTemplateEntity): EnumMap<Language, String> {
        return renovationTemplate.renovationTemplateTranslations.associateTo(EnumMap(Language::class.java))
        { it.lang to it.measureDsc }
    }
}