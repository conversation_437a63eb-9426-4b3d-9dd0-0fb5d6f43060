package de.finacte.epc.mapper.integration.europace

import de.finacte.epc.dto.building.BuildingDto
import de.finacte.epc.dto.calculation.CalculationProfitabilityDto
import de.finacte.epc.dto.integration.europace.EuropaceCaseType
import de.finacte.epc.dto.integration.europace.ImportKundenangabenRequest
import de.finacte.epc.entity.CalculationResultEntity

interface ImportKundenangabenRequestMapper {
    fun toRequest(
        buildingDto: BuildingDto,
        calculationProfitabilityDto: CalculationProfitabilityDto,
        calculationResults: List<CalculationResultEntity>,
        caseType: EuropaceCaseType
    ): ImportKundenangabenRequest
}