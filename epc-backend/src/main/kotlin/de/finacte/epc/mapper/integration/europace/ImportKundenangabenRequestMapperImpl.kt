package de.finacte.epc.mapper.integration.europace

import de.finacte.epc.config.integration.europace.EuropaceConfig
import de.finacte.epc.dto.building.BuildingDto
import de.finacte.epc.dto.calculation.CalculationProfitabilityDto
import de.finacte.epc.dto.integration.europace.*
import de.finacte.epc.entity.CalculationResultCategory
import de.finacte.epc.entity.CalculationResultEntity
import de.finacte.epc.entity.building.BuildingHeating
import de.finacte.epc.entity.building.BuildingPosition
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.exception.integration.EPCEuropaceRequestException
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class ImportKundenangabenRequestMapperImpl(
    private val europaceConfig: EuropaceConfig
) : ImportKundenangabenRequestMapper {
    companion object {
        private val log by LoggerDelegate()
        const val FAMILY_SIZE = 2
    }

    override fun toRequest(
        buildingDto: BuildingDto,
        calculationProfitabilityDto: CalculationProfitabilityDto,
        calculationResults: List<CalculationResultEntity>,
        caseType: EuropaceCaseType
    ): ImportKundenangabenRequest {
        log.info("[EUROPACE] Mapping buildingDto: {} with type {} to Europace request", buildingDto, caseType)

        return ImportKundenangabenRequest(
            importMetadaten = ImportMetadaten(
                datenkontext = europaceConfig.datenkontext
            ),
            kundenangaben = Kundenangaben(
                finanzierungsobjekt = Finanzierungsobjekt(
                    immobilie = Immobilie(
                        adresse = Adresse(
                            strasse = buildingDto.street,
                            hausnummer = buildingDto.no,
                            plz = buildingDto.zipCode,
                            ort = buildingDto.city
                        ),
                        typ = mapImmobilieType(buildingDto, calculationResults),
                    )
                ),
                finanzierungsbedarf = Finanzierungsbedarf(
                    finanzierungszweck = mapFinanzierungszweck(
                        calculationProfitabilityDto,
                        calculationResults,
                        caseType
                    )
                )
            )
        )
    }

    private fun mapFinanzierungszweck(
        calculationProfitabilityDto: CalculationProfitabilityDto,
        calculationResults: List<CalculationResultEntity>,
        caseType: EuropaceCaseType
    ) = when (caseType) {
        EuropaceCaseType.MODERNIZATION -> ModernisierungUmbauAnbau(
            modernisierung = Modernisierungskosten(
                modernisierungskostenInklEigenleistungen = calculationProfitabilityDto.cost,
                endEnergieGeplant = getAlternativeFinalEnergyDemandPerArea(calculationResults),
                co2WertGeplant = getAlternativeCO2EmissionPerArea(calculationResults)
            )
        )

        EuropaceCaseType.PURCHASE -> Kauf(
            modernisierung = Modernisierungskosten(
                modernisierungskostenInklEigenleistungen = calculationProfitabilityDto.cost,
                endEnergieGeplant = getAlternativeFinalEnergyDemandPerArea(calculationResults),
                co2WertGeplant = getAlternativeCO2EmissionPerArea(calculationResults)
            ),
            modernisierungskostenErfassung = VorhandeneModernisierungskosten(
                modernisierungskostenInklEigenleistungen = calculationProfitabilityDto.cost,
                endEnergieGeplant = getAlternativeFinalEnergyDemandPerArea(calculationResults),
                co2WertGeplant = getAlternativeCO2EmissionPerArea(calculationResults)
            )
        )

        EuropaceCaseType.PROLONGATION -> Anschlussfinanzierung(
            modernisierung = Modernisierungskosten(
                modernisierungskostenInklEigenleistungen = calculationProfitabilityDto.cost,
                endEnergieGeplant = getAlternativeFinalEnergyDemandPerArea(calculationResults),
                co2WertGeplant = getAlternativeCO2EmissionPerArea(calculationResults)
            ),
            modernisierungskostenErfassung = VorhandeneModernisierungskosten(
                modernisierungskostenInklEigenleistungen = calculationProfitabilityDto.cost,
                endEnergieGeplant = getAlternativeFinalEnergyDemandPerArea(calculationResults),
                co2WertGeplant = getAlternativeCO2EmissionPerArea(calculationResults)
            )
        )
    }

    private fun mapImmobilieType(
        buildingDto: BuildingDto,
        calculationResults: List<CalculationResultEntity>
    ): ImmobilieType {
        if (buildingDto.position == BuildingPosition.TWIN_HOUSE) {
            return Doppelhaushaelfte(
                gebaeude = mapGebaeude(buildingDto, calculationResults),
                nutzflaechen = mapNutzflaechen(buildingDto),
            )
        }
        if (buildingDto.tenants <= FAMILY_SIZE && buildingDto.position == BuildingPosition.STAND_ALONE) {
            return Einfamilienhaus(
                gebaeude = mapGebaeude(buildingDto, calculationResults),
                nutzflaechen = mapNutzflaechen(buildingDto),
            )
        }
        if (buildingDto.tenants <= FAMILY_SIZE && buildingDto.position in listOf(
                BuildingPosition.ROW_MIDDLE,
                BuildingPosition.ROW_END
            )
        ) {
            return Reihenhaus(
                gebaeude = mapGebaeude(buildingDto, calculationResults),
                nutzflaechen = mapNutzflaechen(buildingDto),
                haustyp = mapReihenhausHaustyp(buildingDto)
            )
        }

        return when (buildingDto.tenants) {
            in (FAMILY_SIZE + 1)..(FAMILY_SIZE * 2) -> Zweifamilienhaus(
                gebaeude = mapGebaeude(buildingDto, calculationResults),
                nutzflaechen = mapNutzflaechen(buildingDto),
                haustyp = mapMehrfamilienhausHaustyp(buildingDto)
            )

            in (FAMILY_SIZE * 2)..Int.MAX_VALUE -> Mehrfamilienhaus(
                gebaeude = mapGebaeude(buildingDto, calculationResults),
                nutzflaechen = mapNutzflaechen(buildingDto),
                haustyp = mapMehrfamilienhausHaustyp(buildingDto)
            )

            else -> throw EPCEuropaceRequestException(
                "Can't match tenants amount: ${buildingDto.tenants} and" +
                        " building position: ${buildingDto.position} to Europace buiding type"
            )
        }

    }

    private fun mapNutzflaechen(buildingDto: BuildingDto) =
        Nutzflaechen(
            dachgeschoss = mapRoofFloor(buildingDto),
            unterkellerung = mapBasement(buildingDto),
        )

    private fun mapGebaeude(
        buildingDto: BuildingDto,
        calculationResults: List<CalculationResultEntity>
    ) = Gebaeude(
        baujahr = buildingDto.constructionYear,
        anzahlGeschosse = buildingDto.floors,
        nutzung = Nutzung(
            wohnen = Wohnen(
                gesamtflaeche = buildingDto.area
            )
        ),
        energieEffizienzAusweis = EnergieEffizienzAusweis(
            primaerEnergieTraeger = mapHeating(buildingDto.heating.primaryHeating),
            endEnergie = getCurrentFinalEnergyDemandPerArea(calculationResults),
            co2Emissionen = getCurrentCO2EmissionPerArea(calculationResults)
        ),
        modernisierung = Modernisierung(
            jahr = mapExistingModernisationYear(buildingDto),
            grad = mapExistingModernisationLevel(buildingDto),
        )
    )

    private fun mapMehrfamilienhausHaustyp(buildingDto: BuildingDto): MehrfamilienhausHausTyp {
        return when (buildingDto.position) {
            BuildingPosition.ROW_END -> MehrfamilienhausHausTyp.KOPFHAUS
            BuildingPosition.ROW_MIDDLE -> MehrfamilienhausHausTyp.MITTELHAUS
            BuildingPosition.STAND_ALONE -> MehrfamilienhausHausTyp.FREISTEHEND
            else -> throw EPCEuropaceRequestException(
                "Can't map MehrfamilienhausHaustyp for " +
                        "position: ${buildingDto.position} and tenants: ${buildingDto.tenants}"
            )
        }
    }

    private fun mapReihenhausHaustyp(buildingDto: BuildingDto): ReihenhausHausTyp {
        return when (buildingDto.position) {
            BuildingPosition.ROW_END -> ReihenhausHausTyp.KOPFHAUS
            BuildingPosition.ROW_MIDDLE -> ReihenhausHausTyp.MITTELHAUS
            else -> throw EPCEuropaceRequestException(
                "Can't map ReihenhausHausTyp for " +
                        "position: ${buildingDto.position} and tenants: ${buildingDto.tenants}"
            )
        }
    }

    private fun getCurrentFinalEnergyDemandPerArea(calculationResults: List<CalculationResultEntity>): Int =
        calculationResults.find { it.category == CalculationResultCategory.CURRENT }?.finalEnergyDemandPerArea?.toInt()
            ?: throw EPCEuropaceRequestException("Can't find CURRENT calculation result")

    private fun getCurrentCO2EmissionPerArea(calculationResults: List<CalculationResultEntity>): Int =
        calculationResults.find { it.category == CalculationResultCategory.CURRENT }?.co2Emissions?.toInt()
            ?: throw EPCEuropaceRequestException("Can't find CURRENT calculation result")


    private fun getAlternativeFinalEnergyDemandPerArea(calculationResults: List<CalculationResultEntity>): BigDecimal? =
        calculationResults.find { it.category == CalculationResultCategory.ALTERNATIVE }
            ?.finalEnergyDemandPerArea?.toBigDecimal()

    private fun getAlternativeCO2EmissionPerArea(calculationResults: List<CalculationResultEntity>): BigDecimal? =
        calculationResults.find { it.category == CalculationResultCategory.ALTERNATIVE }
            ?.co2Emissions?.toBigDecimal()


    private fun mapBasement(buildingDto: BuildingDto): Unterkellerung {
        return if (buildingDto.basement != null) {
            Unterkellerung.UNTERKELLERT
        } else Unterkellerung.NICHT_UNTERKELLERT
    }

    private fun mapRoofFloor(buildingDto: BuildingDto): Dachgeschoss {
        return when (buildingDto.roof.floor) {
            BuildingRoofFloor.FLAT_ROOF -> Dachgeschoss.FLACHDACH
            BuildingRoofFloor.COLD_STORAGE -> Dachgeschoss.NICHT_AUSGEBAUT
            BuildingRoofFloor.PARTIALLY_LIVEABLE -> Dachgeschoss.TEILWEISE_AUSGEBAUT
            BuildingRoofFloor.FULLY_LIVEABLE_REDUCED -> Dachgeschoss.VOLL_AUSGEBAUT
            BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED -> Dachgeschoss.VOLL_AUSGEBAUT
        }
    }

    private fun mapExistingModernisationLevel(buildingDto: BuildingDto): Grad? {
        val heatingModernised = buildingDto.heating.primaryHeatingInstallationYear != null
        val facadeModernised = buildingDto.facade.insulationYear != null
        val roofModernised = buildingDto.roof.insulationYear != null
        val windowsModernised = buildingDto.windows.installationYear != null

        val modernisationsCount = listOf(heatingModernised, facadeModernised, roofModernised, windowsModernised).count()
        return when (modernisationsCount) {
            0 -> null
            1 -> Grad.GERING
            in 2..3 -> Grad.MITTEL
            else -> Grad.HOCH
        }
    }

    private fun mapExistingModernisationYear(buildingDto: BuildingDto): Int? {
        val heatingModernised = buildingDto.heating.primaryHeatingInstallationYear
        val facadeModernised = buildingDto.facade.insulationYear
        val roofModernised = buildingDto.roof.insulationYear
        val windowsModernised = buildingDto.windows.installationYear

        return listOfNotNull(heatingModernised, facadeModernised, roofModernised, windowsModernised)
            .maxOrNull()
    }

    private fun mapHeating(heatingEnergySource: BuildingHeating): PrimaerEnergieTraeger {
        return when (heatingEnergySource) {
            BuildingHeating.GAS_BOILER -> PrimaerEnergieTraeger.OEL_GAS
            BuildingHeating.OIL_BOILER -> PrimaerEnergieTraeger.OEL_GAS
            BuildingHeating.DISTRICT -> PrimaerEnergieTraeger.SONSTIGES
            BuildingHeating.BIOMASS_BOILER -> PrimaerEnergieTraeger.HOLZ
            BuildingHeating.NIGHT_STORAGE -> PrimaerEnergieTraeger.NETZSTROM
            BuildingHeating.HEAT_PUMP -> PrimaerEnergieTraeger.NETZSTROM
            BuildingHeating.ELECTRIC -> PrimaerEnergieTraeger.NETZSTROM
        }
    }
}