package de.finacte.epc.mapper

import de.finacte.epc.dto.building.BuildingMissingAttributesGenerated
import de.finacte.epc.entity.building.BuildingMissingAttributesGeneratedEntity
import org.mapstruct.Mapper
import java.util.*

@Mapper(componentModel = "spring")
interface BuildingMissingAttributesGeneratedMapper {
    fun toEntity(
        dto: BuildingMissingAttributesGenerated,
        buildingId: UUID
    ): BuildingMissingAttributesGeneratedEntity

    fun toDto(entity: BuildingMissingAttributesGeneratedEntity): BuildingMissingAttributesGenerated
}