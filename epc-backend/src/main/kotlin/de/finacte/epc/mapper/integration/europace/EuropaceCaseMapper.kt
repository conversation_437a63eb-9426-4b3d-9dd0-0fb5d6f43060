package de.finacte.epc.mapper.integration.europace

import de.finacte.epc.dto.integration.europace.EuropaceCaseDto
import de.finacte.epc.dto.integration.europace.EuropaceCaseType
import de.finacte.epc.dto.integration.europace.ImportKundenangabenResponse
import de.finacte.epc.entity.integration.europace.EuropaceCaseEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import java.util.UUID

@Mapper(componentModel = "spring")
interface EuropaceCaseMapper {
    fun toDto(europaceCase: EuropaceCaseEntity): EuropaceCaseDto

    @Mapping(target = "buildingId", source = "buildingId")
    @Mapping(target = "caseNo", source = "importKundenangabenResponse.vorgangsnummer")
    @Mapping(target = "caseType", source = "caseType")
    fun apiResponseToEntity(importKundenangabenResponse: ImportKundenangabenResponse, buildingId: UUID, caseType: EuropaceCaseType): EuropaceCaseEntity

    fun toEntity(europaceCaseDto: EuropaceCaseDto): EuropaceCaseEntity
}