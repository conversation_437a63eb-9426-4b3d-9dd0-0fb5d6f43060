package de.finacte.epc.entity.building

import de.finacte.epc.entity.Auditable
import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "building_missing_attributes_generated")
data class BuildingMissingAttributesGeneratedEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    val id: UUID?,

    val roofInsulationThickness: Int,
    @Enumerated(EnumType.STRING)
    val roofInsulationType: BuildingInsulationType,
    @Enumerated(EnumType.STRING)
    val roofMaterial: BuildingRoofMaterial,


    @Enumerated(EnumType.STRING)
    val facadeWallType: BuildingWallType,
    val facadeWallThickness: Int,
    @Enumerated(EnumType.STRING)
    val facadeWallInsulationType: BuildingInsulationType,
    val facadeWallInsulationThickness: Int,

    @Enumerated(EnumType.STRING)
    val windowsFrameMaterial: BuildingWindowFrameMaterial,
    @Enumerated(EnumType.STRING)
    val windowsCoatingType: BuildingWindowCoatingType,

    @Enumerated(EnumType.STRING)
    val basementFloorInsulationType: BuildingInsulationType,
    val basementFloorInsulationThickness: Int,

    @Enumerated(EnumType.STRING)
    val basementExternalWallsInsulationType: BuildingInsulationType,
    val basementExternalWallsInsulationThickness: Int,
    @Enumerated(EnumType.STRING)
    val basementExternalWallsType: BuildingWallType,

    @Enumerated(EnumType.STRING)
    val ceilingWallInsulationType: BuildingInsulationType,
    val ceilingWallInsulationThickness: Int,
    @Enumerated(EnumType.STRING)
    val ceilingWallType: BuildingWallType,
    val ceilingWallThickness: Int,

    @Enumerated(EnumType.STRING)
    val groundFloorInsulationType: BuildingInsulationType,
    val groundFloorInsulationThickness: Int,


    @Enumerated(EnumType.STRING)
    val entranceDoorMaterial: BuildingDoorMaterial,

    val buildingId: UUID
): Auditable<String>()
