package de.finacte.epc.entity.integration.europace

import de.finacte.epc.dto.integration.europace.EuropaceCaseType
import de.finacte.epc.entity.Auditable
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "europace_case")
data class EuropaceCaseEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    val id: UUID?,
    val caseNo: String,
    val buildingId: UUID,
    @Enumerated(EnumType.STRING)
    val caseType: EuropaceCaseType
) : Auditable<String>()