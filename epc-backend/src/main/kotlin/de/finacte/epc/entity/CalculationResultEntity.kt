package de.finacte.epc.entity


import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "calculation_result")
data class CalculationResultEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    val id: UUID?,
    @Enumerated(EnumType.STRING)
    val category: CalculationResultCategory,
    val primaryEnergyDemand: Double,
    @Enumerated(EnumType.STRING)
    val primaryEnergyDemandUnit: EnergyEfficiencyUnit,
    @Enumerated(EnumType.STRING)
    val primaryEnergyDemandScaleGrade: EPCScaleGrade,
    val finalEnergyDemandPerArea: Double,
    @Enumerated(EnumType.STRING)
    val finalEnergyDemandPerAreaUnit: EnergyEfficiencyUnit,
    @Enumerated(EnumType.STRING)
    val finalEnergyDemandPerAreaScaleGrade: EPCScaleGrade,
    @Column(name = "co2_emissions")
    val co2Emissions: Double,
    @Column(name = "co2_emissions_unit")
    @Enumerated(EnumType.STRING)
    val co2EmissionsUnit: Co2EmissionUnit,
    val solarEnergyProduction: Double,
    @Enumerated(EnumType.STRING)
    val solarEnergyProductionUnit: EnergyOverTimeUnit,
    val finalElectricityDemand: Double,
    val finalHotWaterDemand: Double,
    val finalHeatingEnergyDemand: Double,
    val finalCoolingEnergyDemand: Double,
    val buildingId: UUID
): Auditable<String>()