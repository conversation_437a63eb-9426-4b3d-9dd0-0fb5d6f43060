package de.finacte.epc.entity.renovation

import de.finacte.epc.entity.Auditable
import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.JoinTable
import jakarta.persistence.ManyToMany
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "renovation_template")
data class RenovationTemplateEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    val id: UUID?,
    @Enumerated(EnumType.STRING)
    val renovationMeasureCategory: RenovationMeasureCategory,
    @Enumerated(EnumType.STRING)
    val renovationMeasureType: RenovationMeasureType,
    @Enumerated(EnumType.STRING)
    val renovationMeasureValue: RenovationMeasureValue,
    @Enumerated(EnumType.STRING)
    val renovationMeasureVariant: RenovationMeasureVariant,
    val renovationMeasureObject: String,
    val listOrder: Int,

    @OneToMany(
        mappedBy = "renovationTemplate", cascade = [CascadeType.ALL],
        fetch = FetchType.EAGER
    )
    val renovationTemplateTranslations: List<RenovationTemplateTranslationEntity>,

    @ManyToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinTable(
        name = "renovation_template_grant_program",
        joinColumns = [JoinColumn(name = "renovation_template_id")],
        inverseJoinColumns = [JoinColumn(name = "grant_program_id")]
    )
    val grantPrograms: List<GrantProgramEntity>,
    val renovationMeasureDescription: String,
    val renovationMeasureVersion: Int
) : Auditable<String>() {
    override fun toString(): String {
        return StringBuilder().apply {
            append("RenovationTemplateEntity(")
            append("id=").append(id).append(", ")
            append("renovationMeasureCategory=").append(renovationMeasureCategory).append(", ")
            append("renovationMeasureType=").append(renovationMeasureType).append(", ")
            append("renovationMeasureObject=").append(renovationMeasureObject).append(", ")
            append("renovationMeasureValue=").append(renovationMeasureValue)
            append("renovationMeasureDescription=").append(renovationMeasureDescription)
            append("renovationMeasureVariant=").append(renovationMeasureVariant)
            append(")")
        }.toString()
    }
}