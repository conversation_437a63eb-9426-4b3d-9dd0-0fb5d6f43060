package de.finacte.epc.entity

import java.util.*

interface CalculationResultListItemProjection {
    val id: UUID
    val street: String
    val no: String
    val zipCode: String
    val city: String
    val creationDate: Date
    val currentEnergyEfficiency: Double
    val currentEnergyEfficiencyUnit: EnergyEfficiencyUnit
    val currentEnergyEfficiencyScaleGrade: EPCScaleGrade
    val alternativeEnergyEfficiency: Double?
    val alternativeEnergyEfficiencyUnit: EnergyEfficiencyUnit?
    val alternativeEnergyEfficiencyScaleGrade: EPCScaleGrade?
}