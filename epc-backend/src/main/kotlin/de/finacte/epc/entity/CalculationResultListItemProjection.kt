package de.finacte.epc.entity

import java.util.*

interface CalculationResultListItemProjection {
    val id: UUID
    val street: String
    val no: String
    val zipCode: String
    val city: String
    val creationDate: Date
    val currentPrimaryEnergyDemand: Double
    val currentPrimaryEnergyDemandUnit: EnergyEfficiencyUnit
    val currentPrimaryEnergyDemandScaleGrade: EPCScaleGrade
    val alternativePrimaryEnergyDemand: Double?
    val alternativePrimaryEnergyDemandUnit: EnergyEfficiencyUnit?
    val alternativePrimaryEnergyDemandScaleGrade: EPCScaleGrade?
}