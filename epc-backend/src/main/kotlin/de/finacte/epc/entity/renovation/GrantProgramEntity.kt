package de.finacte.epc.entity.renovation

import de.finacte.epc.entity.Auditable
import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "grant_program")
data class GrantProgramEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    val id: UUID?,
    val link: String?,

    @OneToMany(
        mappedBy = "grantProgram", cascade = [CascadeType.ALL],
        fetch = FetchType.EAGER
    )
    val grantProgramTranslation: List<GrantProgramTranslationEntity>,

    @ManyToMany(mappedBy = "grantPrograms", fetch = FetchType.LAZY)
    val renovationTemplates: List<RenovationTemplateEntity>,
    @Enumerated(EnumType.STRING)
    val type: GrantType
): Auditable<String>()