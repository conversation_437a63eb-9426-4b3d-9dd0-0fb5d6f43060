package de.finacte.epc.entity.renovation

enum class RenovationMeasureType{
    // Facade related
    FACADE_EXTERNAL_WALL_INSULATION,
    FACADE_EXTERIOR_CLADDING,

    // Roof related
    ROOF_INSULATION,
    ROOF_NEW_COVERAGE,
    ROOF_RE_ENFORCEMENT,

    // Ceiling related
    CEILING_INSULATION,

    // Windows related
    WINDOWS_GLAZING_TYPE,
    WINDOWS_SHUTTERS,
    WINDOWS_SECURITY_FEATURES,

    // Ground floor related
    GROUND_FLOOR_INSULATION,
    GROUND_FLOOR_MOISTURE_PROTECTION,

    // Basement related
    BASEMENT_INSULATION,
    BASEMENT_MOISTURE_PROTECTION,

    // Doors related
    DOORS_REPLACEMENT,
    DOORS_SECURITY_FEATURES,

    // Heating related
    HEATING_PRIMARY_SOURCE,
    HEATING_VENTILATION_HEAT_EXCHANGE,

    // Surface heating
    UNDERFLOOR_HEATING,
    WALL_HEATING,
    CEILING_HEATING,

    // Radiator related
    BASIC_EFFICIENCY,
    HIGH_PERFORMANCE,

    // Solar panels related
    BATTERY_STORAGE,
    IMMERSION_HEATER,
    SOLAR_PANELS,

    // Smart home energy management related
    INTELLIGENT_HEATING_CONTROLS,
    LIGHTING_AUTOMATION,

    // Electric vehicle charging related
    WALLBOX_INSTALLATION,
    GRID_CONNECTION_UPGRADE,

    // Age appropriate living related
    BARRIER_FREE_BATHROOM,
    STAIRLIFT,
    WIDENING_DOORS,
    EMERGENCY_SYSTEMS
}
