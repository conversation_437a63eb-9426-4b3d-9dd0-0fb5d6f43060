package de.finacte.epc.entity

import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "u_value")
data class UValueEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    val id: UUID?,
    @Enumerated(EnumType.STRING)
    val thermalEnvelopeElementType: ThermalEnvelopeElementType,

    val type1: String?,
    val type2: String?,

    val material: String?, //TODO think about one common enum for materials
    val materialThickness: Int?,

    @Enumerated(EnumType.STRING)
    val insulation: BuildingInsulationType?,
    val insulationThickness: Int?,

    val uValue: Double
) : Auditable<String>()