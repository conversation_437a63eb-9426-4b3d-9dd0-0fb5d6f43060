package de.finacte.epc.entity

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "construction_material_properties")
data class ConstructionMaterialPropertiesEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    val id: UUID?,
    val name: String,
    @Enumerated(EnumType.STRING)
    val type: ConstructionMaterialType,
    val thermalConductivity: Double?,
    val thermalResistance: Double?,
    val thicknessInMetersFrom: Double?,
    val thicknessInMetersTo: Double?,
    val uValue: Double?
) : Auditable<String>()

enum class ConstructionMaterialType {
    INSULATION, ROOFING, WALL, WINDOW, DOOR, FLOOR_FINISH, SCREED
}