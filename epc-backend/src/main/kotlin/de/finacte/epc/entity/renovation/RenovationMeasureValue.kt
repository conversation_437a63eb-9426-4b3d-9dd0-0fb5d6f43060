package de.finacte.epc.entity.renovation

enum class RenovationMeasureValue {
    SOLAR_PANELS_2025,
    // windows ver 1
    PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,

    // windows glazing new
    TRIPLE_GLAZING,
    DOUBLE_GLAZING,

    // windows shutters
    MANUAL_SHUTTERS,
    E<PERSON><PERSON><PERSON><PERSON>_SHUTTERS,

    // facade ver 1
    EXPANDED_POLYSTYRENE_30_CM_2025,

    // facade, ground floor, basement new
    MINERAL_WOOL_10_CM_2025,
    WOOD_FIBER_15_CM_2025,
    EXTRUDED_POLYSTYRENE_10_CM_2025,
    VACUUM_INSULATED_PANEL_30_CM_2025,
    VACUUM_INSULATED_PANEL_5_CM_2025,
    AEROGEL_1_CM_2025,

    //ceiling insulation
    FIBERGLASS_INSULATION_30_CM_2025,

    //roof coverage
    CLAY_TILES,
    NATURAL_SLATE,
    CONCRETE_TILES,
    METAL_TILES,
    BITUMEN_MEMBRANE,
    HIGH_QUALITY_PLASTIC_FOIL,

    DISTRICT_HEATING_2025,
    HEAT_EXCHANGER,
    AIR_TO_WATER_HEAT_PUMP_2025,
    GROUND_TO_WATER_HEAT_PUMP_2025,
    SOLAR_THERMAL_2025,
    BIOMASS_HEATING_2025,

    //new
    DEFAULT
}
