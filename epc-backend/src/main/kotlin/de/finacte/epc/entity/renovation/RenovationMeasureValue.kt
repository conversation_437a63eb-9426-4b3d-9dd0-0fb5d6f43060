package de.finacte.epc.entity.renovation

enum class RenovationMeasureValue {
    // Default value
    DEFAULT,

    // Surface heating
    WET_SYSTEM_WITH_SCREED,
    DRY_SYSTEM_WITH_FLOORING,
    DRY_SYSTEM_WITHOUT_FLOORING,
    WET_SYSTEM_WITH_RECONSTRUCTION,
    DRY_SYSTEM_WITH_RECONSTRUCTION,

    // Heating system values
    DISTRICT_HEATING_2025,
    GROUND_TO_WATER_HEAT_PUMP_2025,
    BIOMASS_HEATING_2025,
    AIR_TO_WATER_HEAT_PUMP_2025,
    HEAT_EXCHANGER,

    // Solar panels battery storage values
    LITHIUM_ION_BATTERY,

    // Solar panels immersion heater values
    WITH_BUFFER,
    WITHOUT_BUFFER,

    // windows glazing new
    TRIPLE_GLAZING,
    DOUBLE_GLAZING,

    // windows shutters
    MANUAL_SHUTTERS,
    ELECTRIC_SHUTTERS,

    // facade, ground floor, basement new
    MINERAL_WOOL_10_CM_2025,
    WOOD_FIBER_15_CM_2025,
    EXTRUDED_POLYSTYRENE_10_CM_2025,
    VACUUM_INSULATED_PANEL_5_CM_2025,
    AEROGEL_4_CM_2025,

    //ceiling insulation
    FIBERGLASS_INSULATION_30_CM_2025,

    //roof coverage
    CLAY_TILES,
    NATURAL_SLATE,
    CONCRETE_TILES,
    METAL_TILES,
    BITUMEN_MEMBRANE,
    HIGH_QUALITY_PLASTIC_FOIL,
}
