package de.finacte.epc.config

import org.springframework.ai.openai.OpenAiChatModel
import org.springframework.ai.openai.OpenAiChatOptions
import org.springframework.ai.openai.api.OpenAiApi
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary

@Configuration
class ChatModelConfig {

    @Bean(name = ["openAi4oMiniChatModel"])
    @Primary
    fun openAi4oMiniChatModel(
        @Value("\${spring.ai.openai.api-key}") apiKey: String,
    ): OpenAiChatModel {
        val openAiApi = OpenAiApi.builder()
            .apiKey(apiKey)
            .build()
        val openAiDefaultOptions = OpenAiChatOptions.builder()
            .model("gpt-4o-mini")
            .temperature(0.7)
            .build()
        return OpenAiChatModel.builder()
            .openAiApi(openAiApi)
            .defaultOptions(openAiDefaultOptions)
            .build()
    }

    @Bean(name = ["openAi41ChatModel"])
    @Primary
    fun openAi41ChatModel(
        @Value("\${spring.ai.openai.api-key}") apiKey: String,
    ): OpenAiChatModel {
        val openAiApi = OpenAiApi.builder()
            .apiKey(apiKey)
            .build()
        val openAiDefaultOptions = OpenAiChatOptions.builder()
            .model("gpt-4.1")
            .temperature(0.0)
            .build()
        return OpenAiChatModel.builder()
            .openAiApi(openAiApi)
            .defaultOptions(openAiDefaultOptions)
            .build()
    }

}
