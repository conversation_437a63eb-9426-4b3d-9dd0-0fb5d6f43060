package de.finacte.epc.dto.integration.europace

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import de.finacte.epc.entity.EPCScaleGrade

data class Finanzierungsobjekt(
    val immobilie: Immobilie
)

data class Immobilie(
    val adresse: <PERSON>ress<PERSON>,
    val typ: ImmobilieType
)

data class Adresse(
    val strasse: String,
    val hausnummer: String,
    val plz: String,
    val ort: String
)

@JsonDeserialize(using = ImmobilieTypeCustomDeserializer::class)
interface ImmobilieType {
    @get:JsonProperty("@type")
    val type: ImmobilieObjectType
    val gebaeude: Gebaeude
    val nutzflaechen: Nutzflaechen
}

enum class ImmobilieObjectType {
    EINFAMILIENHAUS,
    DOPPELHAUSHAELFTE,
    MEHRFAMILIENHAUS,
    ZWEIFAMILIENHAUS,
    REIHENHAUS
}

data class Einfamilienhaus(
    override val type: ImmobilieObjectType = ImmobilieObjectType.EINFAMILIENHAUS,
    override val gebaeude: Gebaeude,
    override val nutzflaechen: Nutzflaechen
) : ImmobilieType

data class Doppelhaushaelfte(
    override val type: ImmobilieObjectType = ImmobilieObjectType.DOPPELHAUSHAELFTE,
    override val gebaeude: Gebaeude,
    override val nutzflaechen: Nutzflaechen
) : ImmobilieType

data class Mehrfamilienhaus(
    override val type: ImmobilieObjectType = ImmobilieObjectType.MEHRFAMILIENHAUS,
    override val gebaeude: Gebaeude,
    override val nutzflaechen: Nutzflaechen,
    val haustyp: MehrfamilienhausHausTyp
) : ImmobilieType

data class Zweifamilienhaus(
    override val type: ImmobilieObjectType = ImmobilieObjectType.ZWEIFAMILIENHAUS,
    override val gebaeude: Gebaeude,
    override val nutzflaechen: Nutzflaechen,
    val haustyp: MehrfamilienhausHausTyp
) : ImmobilieType

enum class MehrfamilienhausHausTyp {
    FREISTEHEND,
    KOPFHAUS,
    MITTELHAUS
}

data class Reihenhaus(
    override val type: ImmobilieObjectType = ImmobilieObjectType.REIHENHAUS,
    override val gebaeude: Gebaeude,
    override val nutzflaechen: Nutzflaechen,
    val haustyp: ReihenhausHausTyp
) : ImmobilieType

enum class ReihenhausHausTyp {
    KOPFHAUS,
    MITTELHAUS
}

data class Gebaeude(
    val baujahr: Int,
    val bauweise: Bauweise = Bauweise.MASSIV,
    val anzahlGeschosse: Int,
    val zustand: Zustand = Zustand.MITTEL,
    val ausstattung: Ausstattung = Ausstattung.MITTEL,
    val nutzung: Nutzung,
    val energieEffizienzAusweis: EnergieEffizienzAusweis,
    val modernisierung: Modernisierung?
)

enum class Bauweise {
    MASSIV
}

enum class Zustand {
    MITTEL
}

enum class Ausstattung {
    MITTEL
}

data class Modernisierung(
    val jahr: Int?,
    val grad: Grad?
)

enum class Grad {
    HOCH,
    MITTEL,
    GERING
}

data class EnergieEffizienzAusweis(
    val ausweistyp: Ausweistyp = Ausweistyp.ENDENERGIEBEDARF,
    val endEnergie: Int,
    val primaerEnergieTraeger: PrimaerEnergieTraeger,
    val energieEffizienzklasse: EPCScaleGrade? = null,  //This value is ignored when passed because it's calculated by Europace (readonly)
    val co2Emissionen: Int
)

enum class Ausweistyp {
    ENDENERGIEBEDARF
}

enum class PrimaerEnergieTraeger {
    OEL_GAS,
    PHOTOVOLTAIK_WINDKRAFT,
    WAERME_KAELTE,
    STEINKOHLE,
    BRAUNKOHLE,
    NETZSTROM,
    VERDRAENGUNGSSTROM,
    HOLZ,
    ABFAELLE,
    SONSTIGES
}

data class Nutzung(
    val wohnen: Wohnen
)

data class Wohnen(
    val gesamtflaeche: Double,
)

data class Nutzflaechen(
    val dachgeschoss: Dachgeschoss,
    val unterkellerung: Unterkellerung
)

enum class Unterkellerung {
    UNTERKELLERT,
    TEILWEISE_UNTERKELLERT,
    NICHT_UNTERKELLERT
}

enum class Dachgeschoss {
    VOLL_AUSGEBAUT,
    TEILWEISE_AUSGEBAUT,
    NICHT_AUSGEBAUT,
    FLACHDACH
}
