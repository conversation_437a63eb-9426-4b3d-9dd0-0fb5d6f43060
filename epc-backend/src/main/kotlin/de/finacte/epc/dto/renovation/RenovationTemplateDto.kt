package de.finacte.epc.dto.renovation

import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import java.util.UUID

data class RenovationTemplateDto(
    val id: UUID?,
    val renovationMeasureCategory: RenovationMeasureCategory,
    val renovationMeasureType: RenovationMeasureType,
    val renovationMeasureValue: RenovationMeasureValue,
    val renovationMeasureObject: String,
    val renovationTemplateTranslations: Set<RenovationTemplateTranslationDto>,
    val renovationMeasureVariant: RenovationMeasureVariant,
)