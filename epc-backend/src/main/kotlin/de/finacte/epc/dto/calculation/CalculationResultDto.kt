package de.finacte.epc.dto.calculation

import de.finacte.epc.entity.*
import java.util.UUID


data class CalculationResultDto(
    val id: UUID?,
    val category: CalculationResultCategory,
    val energyEfficiency: Double,
    val energyEfficiencyUnit: EnergyEfficiencyUnit,
    val energyEfficiencyScaleGrade: EPCScaleGrade,
    val finalEnergyDemandPerArea: Double,
    val finalEnergyDemandPerAreaUnit: EnergyEfficiencyUnit,
    val finalEnergyDemandPerAreaScaleGrade: EPCScaleGrade,
    val co2Emissions: Double,
    val co2EmissionsUnit: Co2EmissionUnit,
    val solarEnergyProduction: Double,
    val solarEnergyProductionUnit: EnergyOverTimeUnit,
    val finalElectricityDemand: Double,
    val finalHotWaterDemand: Double,
    val finalHeatingEnergyDemand: Double,
    val finalCoolingEnergyDemand: Double
)