package de.finacte.epc.dto.building.generated

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonPropertyDescription
import de.finacte.epc.dto.building.generated.insulation.*
import de.finacte.epc.entity.building.BuildingRoofMaterial
import de.finacte.epc.entity.building.*
import java.util.*

data class BuildingMissingAttributesGenerated(
    @JsonIgnore //This class is used in GPT communication and can't have this field in JSON
    val id: UUID? = null,
    @JsonPropertyDescription("Roof insulation thickness in centimeters")
    val roofInsulationThickness: Int,
    @JsonPropertyDescription("Roof insulation type")
    val roofInsulationType: MissingAttributesRoofInsulationType,
    @JsonPropertyDescription("The material from which the roof is made")
    val roofMaterial: BuildingRoofMaterial,

    @JsonPropertyDescription("Facade wall type")
    val facadeWallType: BuildingWallType,
    @JsonPropertyDescription("Facade wall thickness in centimeters")
    val facadeWallThickness: Int,
    @JsonPropertyDescription("Facade wall insulation type")
    val facadeWallInsulationType: MissingAttributesFacadeInsulationType,
    @JsonPropertyDescription("Facade wall insulation thickness in centimeters")
    val facadeWallInsulationThickness: Int,

    @JsonPropertyDescription("Windows frame material")
    val windowsFrameMaterial: BuildingWindowFrameMaterial,
    @JsonPropertyDescription("Windows coating type")
    val windowsCoatingType: BuildingWindowCoatingType,

    @JsonPropertyDescription("Basement floor insulation type")
    val basementFloorInsulationType: MissingAttributesBasementInsulationType,
    @JsonPropertyDescription("Basement floor insulation thickness in centimeters")
    val basementFloorInsulationThickness: Int,

    @JsonPropertyDescription("Basement external wall insulation type")
    val basementExternalWallsInsulationType: MissingAttributesBasementInsulationType,
    @JsonPropertyDescription("Basement external walls insulation thickness in centimeters")
    val basementExternalWallsInsulationThickness: Int,
    @JsonPropertyDescription("Basement external wall type")
    val basementExternalWallsType: BuildingWallType,

    @JsonPropertyDescription("Ceiling insulation type")
    val ceilingWallInsulationType: MissingAttributesCeilingInsulationType,
    @JsonPropertyDescription("Ceiling wall insulation thickness in centimeters")
    val ceilingWallInsulationThickness: Int,
    @JsonPropertyDescription("Ceiling wall type")
    val ceilingWallType: BuildingWallType,
    @JsonPropertyDescription("Ceiling wall thickness in centimeters")
    val ceilingWallThickness: Int,

    @JsonPropertyDescription("Ground floor insulation type")
    val groundFloorInsulationType: MissingAttributesGroundFloorInsulationType,
    @JsonPropertyDescription("Ground floor insulation thickness in centimeters")
    val groundFloorInsulationThickness: Int,

    @JsonPropertyDescription("Entrance door material")
    val entranceDoorMaterial: BuildingDoorMaterial
    )