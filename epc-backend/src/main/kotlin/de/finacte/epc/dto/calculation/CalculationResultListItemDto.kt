package de.finacte.epc.dto.calculation

import de.finacte.epc.entity.EPCScaleGrade
import de.finacte.epc.entity.EnergyEfficiencyUnit
import java.util.*

data class CalculationResultListItemDto (
    val id: UUID,
    val street: String,
    val no: String,
    val zipCode: String,
    val city: String,
    val creationDate: Date,
    val currentEnergyEfficiency: Double,
    val currentEnergyEfficiencyUnit: EnergyEfficiencyUnit,
    val currentEnergyEfficiencyScaleGrade: EPCScaleGrade,
    val alternativeEnergyEfficiency: Double?,
    val alternativeEnergyEfficiencyUnit: EnergyEfficiencyUnit?,
    val alternativeEnergyEfficiencyScaleGrade: EPCScaleGrade?
)