package de.finacte.epc.dto.calculation

import de.finacte.epc.entity.EPCScaleGrade
import de.finacte.epc.entity.EnergyEfficiencyUnit
import java.util.*

data class CalculationResultListItemDto (
    val id: UUID,
    val street: String,
    val no: String,
    val zipCode: String,
    val city: String,
    val creationDate: Date,
    val currentPrimaryEnergyDemand: Double,
    val currentPrimaryEnergyDemandUnit: EnergyEfficiencyUnit,
    val currentPrimaryEnergyDemandScaleGrade: EPCScaleGrade,
    val alternativePrimaryEnergyDemand: Double?,
    val alternativePrimaryEnergyDemandUnit: EnergyEfficiencyUnit?,
    val alternativePrimaryEnergyDemandScaleGrade: EPCScaleGrade?
)