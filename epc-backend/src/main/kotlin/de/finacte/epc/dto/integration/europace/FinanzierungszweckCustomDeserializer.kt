package de.finacte.epc.dto.integration.europace

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import de.finacte.epc.exception.integration.EPCEuropaceResponseException

class FinanzierungszweckCustomDeserializer : StdDeserializer<Finanzierungszweck>(Finanzierungszweck::class.java) {

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Finanzierungszweck {
        val node: JsonNode = p.codec.readTree(p)
        return when (val type = node.get("@type").textValue()) {
            FinanzierungszweckType.MODERNISIERUNG_UMBAU_ANBAU.name -> ModernisierungUmbauAnbau(
                type = FinanzierungszweckType.MODERNISIERUNG_UMBAU_ANBAU,
                modernisierung = deserializeModernisierungskosten(node),
            )

            FinanzierungszweckType.ANSCHLUSSFINANZIERUNG.name -> ModernisierungUmbauAnbau(
                type = FinanzierungszweckType.ANSCHLUSSFINANZIERUNG,
                modernisierung = deserializeModernisierungskosten(node),
            )

            FinanzierungszweckType.KAUF.name -> ModernisierungUmbauAnbau(
                type = FinanzierungszweckType.ANSCHLUSSFINANZIERUNG,
                modernisierung = deserializeModernisierungskosten(node),
            )

            else -> {
                throw EPCEuropaceResponseException("Europace is returning ImmobilieType which is not supported by EPC application. Type: $type")
            }
        }
    }

    private fun deserializeModernisierungskosten(node: JsonNode): Modernisierungskosten =
        Modernisierungskosten(
            modernisierungskostenInklEigenleistungen = node.get("modernisierung")
                .get("modernisierungskostenInklEigenleistungen").doubleValue(),
            endEnergieGeplant = node.get("modernisierung").get("endEnergieGeplant").decimalValue(),
            co2WertGeplant = node.get("modernisierung").get("co2WertGeplant").decimalValue()
        )

}