package de.finacte.epc.dto.integration.europace

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import java.math.BigDecimal

data class Finanzierungsbedarf(
    val finanzierungszweck: Finanzierungszweck
)

enum class FinanzierungszweckType {
    MODERNISIERUNG_UMBAU_ANBAU, ANSCHLUSSFINANZIERUNG, KAUF
}

data class ModernisierungUmbauAnbau(
    @JsonProperty("@type")
    override val type: FinanzierungszweckType = FinanzierungszweckType.MODERNISIERUNG_UMBAU_ANBAU,
    override val modernisierung: Modernisierungskosten
) : Finanzierungszweck

data class Anschlussfinanzierung(
    @JsonProperty("@type")
    override val type: FinanzierungszweckType = FinanzierungszweckType.ANSCHLUSSFINANZIERUNG,
    override val modernisierung: Modernisierungskosten,
    val modernisierungskostenErfassung: VorhandeneModernisierungskosten
) : Finanzierungszweck

data class Kauf(
    @JsonProperty("@type")
    override val type: FinanzierungszweckType = FinanzierungszweckType.KAUF,
    override val modernisierung: Modernisierungskosten,
    val modernisierungskostenErfassung: VorhandeneModernisierungskosten
) : Finanzierungszweck

@JsonDeserialize(using = FinanzierungszweckCustomDeserializer::class)
interface Finanzierungszweck {
    @get:JsonProperty("@type")
    val type: FinanzierungszweckType
    val modernisierung: Modernisierungskosten
}

data class Modernisierungskosten(
    val modernisierungskostenInklEigenleistungen: Double,
    val endEnergieGeplant: BigDecimal?,
    val co2WertGeplant: BigDecimal?
)

enum class ModernisierungKostenErfassungType {
    VORHANDENE_MODERNISIERUNGSKOSTEN
}

data class VorhandeneModernisierungskosten(
    @JsonProperty("@type")
    val type: ModernisierungKostenErfassungType = ModernisierungKostenErfassungType.VORHANDENE_MODERNISIERUNGSKOSTEN,
    val modernisierungskostenInklEigenleistungen: Double,
    val endEnergieGeplant: BigDecimal?,
    val co2WertGeplant: BigDecimal?
)