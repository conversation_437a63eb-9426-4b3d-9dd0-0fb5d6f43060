package de.finacte.epc.dto.calculation

import de.finacte.epc.entity.Language
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import java.util.*


data class RenovationDto(
    val id: UUID,
    val measure: EnumMap<Language, String>,
    val measureDsc: EnumMap<Language, String>,
    val recommended: <PERSON><PERSON><PERSON>,
    val selected: <PERSON>olean,
    val renovationMeasureCategory: RenovationMeasureCategory,
    val renovationMeasureType: RenovationMeasureType,
    val renovationMeasureVariant: RenovationMeasureVariant,
    val renovatedElementListOrder: Int,
    val grantPrograms: List<EnumMap<Language, RenovationGrantDto>>,
    val cost: RenovationCostDto?,
)