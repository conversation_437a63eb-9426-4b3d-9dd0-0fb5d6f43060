package de.finacte.epc.dto.integration.europace

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import de.finacte.epc.entity.EPCScaleGrade
import de.finacte.epc.exception.integration.EPCEuropaceResponseException

class ImmobilieTypeCustomDeserializer : StdDeserializer<ImmobilieType>(ImmobilieType::class.java) {

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): ImmobilieType {
        val node: JsonNode = p.codec.readTree(p)
        return when (val type = node.get("@type").textValue()) {
            ImmobilieObjectType.EINFAMILIENHAUS.name -> Einfamilienhaus(
                gebaeude = deserializeGebaude(node),
                nutzflaechen = deserializeNutzflaechen(node),
            )

            ImmobilieObjectType.DOPPELHAUSHAELFTE.name -> <PERSON><PERSON><PERSON>haelf<PERSON>(
                gebaeude = deserializeGebaude(node),
                nutzflaechen = deserializeNutzflaechen(node),
            )

            ImmobilieObjectType.MEHRFAMILIENHAUS.name -> Mehrfamilienhaus(
                gebaeude = deserializeGebaude(node),
                nutzflaechen = deserializeNutzflaechen(node),
                haustyp = MehrfamilienhausHausTyp.valueOf(node.get("haustyp").textValue())
            )

            ImmobilieObjectType.REIHENHAUS.name -> Reihenhaus(
                gebaeude = deserializeGebaude(node),
                nutzflaechen = deserializeNutzflaechen(node),
                haustyp = ReihenhausHausTyp.valueOf(node.get("haustyp").textValue())
            )

            else -> {
                throw EPCEuropaceResponseException("Europace is returning ImmobilieType which is not supported by EPC application. Type: $type")
            }
        }
    }

    private fun deserializeNutzflaechen(node: JsonNode): Nutzflaechen =
        Nutzflaechen(
            dachgeschoss = Dachgeschoss.valueOf(node.get("nutzflaechen").get("dachgeschoss").textValue()),
            unterkellerung = Unterkellerung.valueOf(node.get("nutzflaechen").get("unterkellerung").textValue()),
        )


    private fun deserializeGebaude(node: JsonNode): Gebaeude =
        Gebaeude(
            baujahr = node.get("gebaeude").get("baujahr").intValue(),
            anzahlGeschosse = node.get("gebaeude").get("anzahlGeschosse").intValue(),
            nutzung = Nutzung(
                wohnen = Wohnen(
                    gesamtflaeche = node.get("gebaeude").get("nutzung").get("wohnen").get("gesamtflaeche").doubleValue()
                )
            ),
            energieEffizienzAusweis = EnergieEffizienzAusweis(
                primaerEnergieTraeger = PrimaerEnergieTraeger.valueOf(
                    node.get("gebaeude").get("energieEffizienzAusweis").get("primaerEnergieTraeger").textValue()
                ),
                endEnergie = node.get("gebaeude").get("energieEffizienzAusweis").get("endEnergie").intValue(),
                energieEffizienzklasse = EPCScaleGrade.valueOf(
                    node.get("gebaeude").get("energieEffizienzAusweis").get("energieEffizienzklasse").textValue()
                ),
                co2Emissionen = node.get("gebaeude").get("energieEffizienzAusweis").get("co2Emissionen").intValue()
            ),
            modernisierung = Modernisierung(
                jahr = node.get("gebaeude").get("modernisierung").get("jahr").intValue(),
                grad = Grad.valueOf(node.get("gebaeude").get("modernisierung").get("grad").textValue())
            )
        )

}