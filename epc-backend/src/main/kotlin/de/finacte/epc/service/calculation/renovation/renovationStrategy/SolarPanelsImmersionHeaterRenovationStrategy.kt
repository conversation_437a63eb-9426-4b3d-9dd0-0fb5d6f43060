package de.finacte.epc.service.calculation.renovation.renovationStrategy

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service

@Service
class SolarPanelsImmersionHeaterRenovationStrategy : RenovationStrategy() {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun apply(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateDto,
    ): BuildingCalculationInput {
        log.debug(
            "[RENOVATION APPLY - {}] -Template: {} ",
            getRenovatedElementKeyForStrategy(),
            renovationTemplate
        )

        val mapper = jacksonObjectMapper()
        return try {
            val template =
                mapper.readValue(
                    renovationTemplate.renovationMeasureObject,
                    BuildingCalculationInput.SolarPanelsImmersionHeater::class.java
                )

            return buildingCalculationInput.copy(
                solarPanelsImmersionHeater = template
            )
        } catch (exception: JsonProcessingException) {
            log.warn(
                "[RENOVATION APPLY - {}] -Template: {}. Can't parse renovated element from JSON to class: {} ",
                getRenovatedElementKeyForStrategy(),
                renovationTemplate,
                renovationTemplate.renovationMeasureObject
            )
            buildingCalculationInput
        }
    }

    override fun getRenovatedElementKeyForStrategy(): RenovationMeasureType =
        RenovationMeasureType.IMMERSION_HEATER
}
