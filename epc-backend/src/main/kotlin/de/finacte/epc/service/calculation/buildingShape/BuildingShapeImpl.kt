package de.finacte.epc.service.calculation.buildingShape

import de.finacte.epc.entity.building.BuildingPosition
import de.finacte.epc.entity.building.BuildingWindowsToWallRatio
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.service.calculation.CalculationConstants.STANDARD_AVG_BASEMENT_HEIGHT
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.roofFloorStrategies.RoofFloorCalculation
import de.finacte.epc.service.calculation.buildingShape.roofFloorStrategies.RoofFloorDimensions
import de.finacte.epc.service.calculation.buildingShape.v1.*
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service

@Service
class BuildingShapeImpl(
    val roofFloorCalculation: RoofFloorCalculation
) : BuildingShape {
    companion object {
        private val log by LoggerDelegate()
        const val AVG_DOOR_AREA_IN_GERMANY = 1.8
    }

    data class PositionAdjustedWallsThermalEnvelops(
        val basementWallsThermalEnvelop: Double,
        val regularFloorsWallsThermalEnvelope: Double,
        val roofFloorWallsThermalEnvelope: Double
    )

    override fun calculate(buildingCalculationInput: BuildingCalculationInput): BuildingShapeResult {
        with(buildingCalculationInput) {
            val roofFloorDimensions = roofFloorCalculation.getDimensions(
                liveableArea = fixedAttributes.area,
                floorsWithoutRoofFloor = fixedAttributes.floors,
                roofFloor = fixedAttributes.roofFloor,
                shape = fixedAttributes.shape
            )

            val basementVolume = calculateBasementVolume(
                roofFloorDimensions.buildingBaseArea,
                fixedAttributes.basementExist
            )

            val basementHeatedVolume = calculateBasementHeatedVolume(basementVolume, basementHeating.basementHeated)

            val basementHeatedWallArea = calculateBasementHeatedWallArea(
                roofFloorDimensions.perimeter,
                fixedAttributes.floorHeight,
                basementHeating.basementHeated,
            )

            val regularFloorsVolume = calculateResidentialFloorsVolume(
                roofFloorDimensions.buildingBaseArea,
                fixedAttributes.floors,
                fixedAttributes.floorHeight,
            )

            val regularFloorsHeatedWallArea = calculateRegularFloorsHeatedWallArea(
                roofFloorDimensions.perimeter,
                fixedAttributes.floorHeight,
                fixedAttributes.floors,
            )

            val buildingVolume = basementVolume + regularFloorsVolume + roofFloorDimensions.roofFloorVolume

            val buildingThermalEnvelopeVolume =
                basementHeatedVolume + regularFloorsVolume + roofFloorDimensions.heatedVolume

            val adjustedWallThermalEnvelopAreas = calculatePositionAdjustedWallsThermalEnvelopAreas(
                fixedAttributes.position,
                fixedAttributes.shape,
                regularFloorsHeatedWallArea,
                basementHeatedWallArea,
                roofFloorDimensions
            )

            val buildingHeatedArea = calculateHeatedArea(
                fixedAttributes.floors,
                roofFloorDimensions.buildingBaseArea,
                roofFloorDimensions.heatedFloorArea,
                basementHeating.basementHeated,
                )

            val result = BuildingShapeResult(
                buildingVolume = buildingVolume,
                buildingArea = fixedAttributes.area,
                buildingHeatedArea = buildingHeatedArea,
                buildingThermalEnvelopeVolume = buildingThermalEnvelopeVolume,
                buildingRoofSurfaceArea = roofFloorDimensions.roofSurfaceArea,
                buildingBaseArea = roofFloorDimensions.buildingBaseArea,
                buildingPerimeter = roofFloorDimensions.perimeter,
                thermalEnvelopeElements = calculateThermalEnvelopeElements(
                    adjustedWallThermalEnvelopAreas,
                    roofFloorDimensions.heatedCeilingArea,
                    roofFloorDimensions.heatedRoofArea,
                    roofFloorDimensions.buildingBaseArea,
                    fixedAttributes.windowsToWallRatio,
                ),
                roofFloorVolume = roofFloorDimensions.roofFloorVolume,
            )
            log.debug(
                "[BUILDING SHAPE] building ID: {} , values:\n{}",
                buildingCalculationInput.fixedAttributes.buildingId, result
            )
            return result
        }
    }

    private fun calculatePositionAdjustedWallsThermalEnvelopAreas(
        position: BuildingPosition,
        shape: de.finacte.epc.entity.building.BuildingShape,
        regularFloorsWallArea: Double,
        basementWallArea: Double,
        roofFloorDimensions: RoofFloorDimensions
    ): PositionAdjustedWallsThermalEnvelops {
        val cubicFloorMultiplier = shape.terracedWallFactor * position.terracedWallsAmount

        return PositionAdjustedWallsThermalEnvelops(
            basementWallsThermalEnvelop = basementWallArea - (basementWallArea * cubicFloorMultiplier),
            regularFloorsWallsThermalEnvelope = regularFloorsWallArea - (regularFloorsWallArea * cubicFloorMultiplier),
            roofFloorWallsThermalEnvelope = roofFloorDimensions.heatedWallArea - (roofFloorDimensions.terraceWallArea * position.terracedWallsAmount)
        )
    }

    private fun calculateThermalEnvelopeElements(
        adjustedThermalEnvelops: PositionAdjustedWallsThermalEnvelops,
        ceilingThermalEnvelopeArea: Double,
        roofThermalEnvelopeArea: Double,
        singleFloorArea: Double,
        buildingWindowsToWallRatio: BuildingWindowsToWallRatio
    ): Set<BuildingShapeResult.ThermalEnvelopeElement> {
        val (basementThermalEnvelop,
            regularFloorThermalEnvelope,
            roofFloorThermalEnvelope) = adjustedThermalEnvelops

        val facadeThermalEnvelop = regularFloorThermalEnvelope + roofFloorThermalEnvelope

        val thermalEnvelopeElements = mutableSetOf(
            calculateFacadeWallsThermalEnvelopArea(
                facadeThermalEnvelop, buildingWindowsToWallRatio
            ),
            calculateWindowsThermalEnvelopArea(
                facadeThermalEnvelop, buildingWindowsToWallRatio
            ),
            calculateDoorThermalEnvelopArea()
        )

        if (basementThermalEnvelop > 0.00) {
            thermalEnvelopeElements.add(
                BuildingShapeResult.ThermalEnvelopeElement(
                    basementThermalEnvelop,
                    ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS
                )
            )
            thermalEnvelopeElements.add(calculateBasementFloorThermalEnvelopArea(singleFloorArea))
        } else {
            thermalEnvelopeElements.add(calculateGroundFloorThermalEnvelopArea(singleFloorArea))
        }

        if (roofThermalEnvelopeArea > 0.00) {
            thermalEnvelopeElements.add(
                BuildingShapeResult.ThermalEnvelopeElement(roofThermalEnvelopeArea, ThermalEnvelopeElementType.ROOF)
            )
        }

        if (ceilingThermalEnvelopeArea > 0.00) {
            thermalEnvelopeElements.add(
                BuildingShapeResult.ThermalEnvelopeElement(ceilingThermalEnvelopeArea, ThermalEnvelopeElementType.CEILING)
            )
        }
        return thermalEnvelopeElements
    }

    private fun calculateRegularFloorsHeatedWallArea(perimeter: Double, buildingFloorHeight: Double, floors: Int): Double {
        return perimeter * buildingFloorHeight * floors
    }

    private fun calculateBasementHeatedWallArea(perimeter: Double, buildingFloorHeight: Double, heated: Boolean): Double {
        return if (heated) perimeter * STANDARD_AVG_BASEMENT_HEIGHT else 0.00
    }

    private fun calculateHeatedArea(
        floors: Int,
        buildingBaseArea: Double,
        roofHeatedFloorArea: Double,
        basementHeated: Boolean
    ): Double {
        val heatedFloors = if (basementHeated) floors + 1 else floors
        return (heatedFloors * buildingBaseArea) + roofHeatedFloorArea
    }
}