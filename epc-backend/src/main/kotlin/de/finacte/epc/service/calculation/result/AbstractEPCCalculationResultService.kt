package de.finacte.epc.service.calculation.result

import de.finacte.epc.dto.calculation.CalculationResultDto
import de.finacte.epc.entity.CalculationResultCategory
import de.finacte.epc.entity.Co2EmissionUnit
import de.finacte.epc.entity.EnergyEfficiencyUnit
import de.finacte.epc.entity.EnergyOverTimeUnit
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.repository.CarbonEmissionFactorRepository
import de.finacte.epc.repository.PrimaryEnergyFactorRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateData
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import de.finacte.epc.service.calculation.result.intermediate.electricityDemand.ElectricityDemand
import de.finacte.epc.service.calculation.result.intermediate.finalEffectiveCoolingEnergy.FinalEffectiveCoolingEnergy
import de.finacte.epc.service.calculation.result.intermediate.finalEffectiveHeatingEnergy.FinalEffectiveHeatingEnergy
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.HeatEnergyGainResult
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.appliances.AppliancesHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.light.LightHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.occupants.OccupantsHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.solar.SolarHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatTransferCoefficient.HeatTransferCoefficient
import de.finacte.epc.service.calculation.result.intermediate.hotWaterDemand.HotWaterDemand
import de.finacte.epc.service.calculation.result.intermediate.hotWaterDemand.HotWaterDemandResult
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant.SolarPlantProduction
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant.SolarPlantProductionResult
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarThermal.SolarThermalProduction
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarThermal.SolarThermalProductionResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculation
import de.finacte.epc.service.calculation.result.intermediate.ventilationLossCoefficient.VentilationLossCoefficient
import de.finacte.epc.utils.LoggerDelegate
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

abstract class AbstractEPCCalculationResultService(
    private val climateData: ClimateData,
    private val electricityDemand: ElectricityDemand,
    private val finalEffectiveCoolingEnergy: FinalEffectiveCoolingEnergy,
    private val finalEffectiveHeatingEnergy: FinalEffectiveHeatingEnergy,
    private val appliancesHeatEnergyGain: AppliancesHeatEnergyGain,
    private val lightHeatEnergyGain: LightHeatEnergyGain,
    private val occupantsHeatEnergyGain: OccupantsHeatEnergyGain,
    private val solarHeatEnergyGain: SolarHeatEnergyGain,
    private val heatTransferCoefficient: HeatTransferCoefficient,
    private val hotWaterDemand: HotWaterDemand,
    private val ventilationLossCoefficient: VentilationLossCoefficient,
    private val primaryEnergyFactorRepository: PrimaryEnergyFactorRepository,
    private val uValueCalculation: UValueCalculation,
    private val solarThermalProduction: SolarThermalProduction,
    private val solarPlantProduction: SolarPlantProduction,
    private val scaleGradePicker: ScaleGradePicker,
    private val carbonEmissionFactorRepository: CarbonEmissionFactorRepository
) : EPCCalculationResultService {

    companion object {
        private val log by LoggerDelegate()
    }

    override suspend fun calculate(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeCalculationResult: BuildingShapeResult,
        calculationResultCategory: CalculationResultCategory
    ): CalculationResultDto =
        coroutineScope {
            val climateDataJob = async { climateData.calculate(buildingCalculationInput.fixedAttributes) }
            val electricityDemandJob = async { electricityDemand.calculate(buildingCalculationInput.fixedAttributes) }
            val hotWaterDemandJob = async { hotWaterDemand.calculate(buildingCalculationInput) }

            val ventilationLossCoefficientJob =
                async { ventilationLossCoefficient.calculate(buildingShapeCalculationResult, buildingCalculationInput) }

            val uValuesCalculationResult =
                uValueCalculation.calculate(
                    buildingShapeCalculationResult,
                    buildingCalculationInput
                )
            log(buildingCalculationInput, uValuesCalculationResult, "[U-VALUES]")

            val heatTransferCoefficientJob = async {
                heatTransferCoefficient.calculate(buildingShapeCalculationResult, uValuesCalculationResult)
            }

            val climateDataResult = climateDataJob.await()
            log(buildingCalculationInput, climateDataResult, "[CLIMATE DATA]")

            val solarThermalProductionJob = async {
                solarThermalProduction.calculate(climateDataResult, buildingCalculationInput)
            }
            val solarPlantProductionJob = async {
                solarPlantProduction.calculate(buildingCalculationInput, buildingShapeCalculationResult, climateDataResult)
            }

            val electricityDemandResult = electricityDemandJob.await()
            log(buildingCalculationInput, electricityDemandResult, "[ELECTRICITY DEMAND]")

            val heatEnergyGainsJob =
                async {
                    calculateHeatEnergyGains(
                        buildingCalculationInput,
                        buildingShapeCalculationResult,
                        climateDataResult,
                        electricityDemandResult
                    )
                }

            val heatTransferCoefficientResult = heatTransferCoefficientJob.await()
            log(buildingCalculationInput, heatTransferCoefficientResult, "[HEAT TRANSFER COEFFICIENT]")

            val ventilationLossCoefficientResult = ventilationLossCoefficientJob.await()
            log(buildingCalculationInput, ventilationLossCoefficientResult, "[VENTILATION LOSS COEFFICIENT]")

            val heatEnergyGainsResult = heatEnergyGainsJob.await()
            log(buildingCalculationInput, heatEnergyGainsResult, "[ALL HEAT GAINS]")

            val finalEffectiveHeatingEnergyResult = finalEffectiveHeatingEnergy.calculate(
                climateDataResult,
                buildingCalculationInput.heating,
                heatTransferCoefficientResult,
                ventilationLossCoefficientResult
            )
            log(buildingCalculationInput, finalEffectiveHeatingEnergyResult, "[HEATING ENERGY DEMAND]")

            val finalEffectiveCoolingEnergyResult = finalEffectiveCoolingEnergy.calculate(
                climateDataResult,
                buildingCalculationInput.fixedAttributes,
                heatTransferCoefficientResult,
                ventilationLossCoefficientResult
            )
            log(buildingCalculationInput, finalEffectiveCoolingEnergyResult, "[COOLING ENERGY DEMAND]")

            val solarThermalProductionResult = solarThermalProductionJob.await()
            log(buildingCalculationInput, solarThermalProductionResult, "[SOLAR THERMAL PRODUCTION]")
            val hotWaterDemandResult = hotWaterDemandJob.await()
            log(buildingCalculationInput, hotWaterDemandResult, "[HOT WATER DEMAND]")
            val solarPlantProductionResult = solarPlantProductionJob.await()
            log(buildingCalculationInput, solarPlantProductionResult, "[SOLAR PLANT PRODUCTION]")


            val energyDemandMinusRenewables = deductRenewables(
                hotWaterDemandResult,
                solarThermalProductionResult,
                buildingCalculationInput,
                solarPlantProductionResult,
                electricityDemandResult,
                finalEffectiveCoolingEnergyResult,
                finalEffectiveHeatingEnergyResult
            )

            val finalEnergyDemand = calculateFinalEnergyDemand(
                buildingCalculationInput,
                energyDemandMinusRenewables.electricityDemandResult,
                energyDemandMinusRenewables.hotWaterDemandResult,
                energyDemandMinusRenewables.coolingEnergyDemandResult,
                energyDemandMinusRenewables.heatingEnergyDemandResult,
                heatEnergyGainsResult.summerHeatEnergyGainKWH,
                heatEnergyGainsResult.winterHeatEnergyGainKWH
            )
            log(buildingCalculationInput, finalEnergyDemand, "[FINAL ENERGY DEMAND]")


            val primaryEnergyDemandResult = calculatePrimaryEnergyDemand(
                buildingCalculationInput,
                energyDemandMinusRenewables.electricityDemandResult,
                energyDemandMinusRenewables.hotWaterDemandResult,
                energyDemandMinusRenewables.coolingEnergyDemandResult,

                energyDemandMinusRenewables.heatingEnergyDemandResult,
                heatEnergyGainsResult.summerHeatEnergyGainKWH,
                heatEnergyGainsResult.winterHeatEnergyGainKWH
            )
            log(buildingCalculationInput, primaryEnergyDemandResult, "[PRIMARY ENERGY DEMAND]")

            val co2Emissions = calculateCo2Emissions(
                buildingCalculationInput,
                energyDemandMinusRenewables.electricityDemandResult,
                energyDemandMinusRenewables.hotWaterDemandResult,
                energyDemandMinusRenewables.coolingEnergyDemandResult,

                energyDemandMinusRenewables.heatingEnergyDemandResult,
                heatEnergyGainsResult.summerHeatEnergyGainKWH,
                heatEnergyGainsResult.winterHeatEnergyGainKWH
            )
            log(buildingCalculationInput, primaryEnergyDemandResult, "[PRIMARY ENERGY DEMAND]")

            val primaryEnergyDemandPerArea = primaryEnergyDemandResult / buildingCalculationInput.fixedAttributes.area
            val finalEnergyDemandPerArea = finalEnergyDemand.sum() / buildingCalculationInput.fixedAttributes.area
            val result = CalculationResultDto(
                id = UUID.randomUUID(),
                category = calculationResultCategory,
                energyEfficiency = primaryEnergyDemandPerArea,
                energyEfficiencyUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
                energyEfficiencyScaleGrade = scaleGradePicker.calculateGrade(primaryEnergyDemandPerArea),
                finalEnergyDemandPerArea = finalEnergyDemandPerArea,
                finalEnergyDemandPerAreaUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
                finalEnergyDemandPerAreaScaleGrade = scaleGradePicker.calculateGrade(finalEnergyDemandPerArea),
                co2Emissions = co2Emissions / buildingShapeCalculationResult.buildingHeatedArea,
                co2EmissionsUnit = Co2EmissionUnit.KG_M2,
                solarEnergyProduction = solarPlantProductionResult.annualProducedEnergy,
                solarEnergyProductionUnit = EnergyOverTimeUnit.KWH_YEAR,
                finalElectricityDemand = finalEnergyDemand.electricityDemandResult,
                finalHotWaterDemand = finalEnergyDemand.hotWaterDemandResult,
                finalHeatingEnergyDemand = finalEnergyDemand.heatingEnergyDemandResult,
                finalCoolingEnergyDemand = finalEnergyDemand.coolingEnergyDemandResult,
            )
            log(buildingCalculationInput, result, "[FINAL RESULT]")


            return@coroutineScope result
        }

    private fun calculateCo2Emissions(
        buildingCalculationInput: BuildingCalculationInput,
        electricityDemandResult: Double,
        hotWaterDemandResult: Double,
        finalEffectiveCoolingEnergyResult: Double,
        finalEffectiveHeatingEnergyResult: Double,
        summerHeatEnergyGainKWH: Double,
        winterHeatEnergyGainKWH: Double
    ): Double {
        val electricitySourceCarbonEmissionFactor = findElectricitySourceCarbonEmissionFactor()
        val electricDemandCarbonEmissions = electricityDemandResult * electricitySourceCarbonEmissionFactor
        val hotWaterDemandCarbonEmissions = hotWaterDemandResult *
                findHotWaterSourceCarbonEmissionFactor(buildingCalculationInput.heating)
        val finalEffectiveHeatingEnergyCarbonEmissions =
            maxOf(0.0,(finalEffectiveHeatingEnergyResult - winterHeatEnergyGainKWH)) *
                    findHeatingSourceCarbonEmissionFactor(buildingCalculationInput.heating)
        val finalEffectiveCoolingEnergyCarbonEmissions =
            (finalEffectiveCoolingEnergyResult + summerHeatEnergyGainKWH) * electricitySourceCarbonEmissionFactor

        val co2Emissions =
            electricDemandCarbonEmissions + hotWaterDemandCarbonEmissions + finalEffectiveHeatingEnergyCarbonEmissions
        if(buildingCalculationInput.fixedAttributes.acInstalled){
            return co2Emissions + finalEffectiveCoolingEnergyCarbonEmissions
        }
        return co2Emissions
    }

    private fun deductRenewables(
        hotWaterDemandResult: HotWaterDemandResult,
        solarThermalProductionResult: SolarThermalProductionResult,
        buildingCalculationInput: BuildingCalculationInput,
        solarPlantProductionResult: SolarPlantProductionResult,
        electricityDemandResult: Double,
        finalEffectiveCoolingEnergyResult: Double,
        finalEffectiveHeatingEnergyResult: Double
    ): EnergyDemand {
        val hotWaterDemandResultAfterSolarThermal = solarThermalProduction.deductFromHotWaterDemand(
            hotWaterDemandResult,
            solarThermalProductionResult
        )
        log(
            buildingCalculationInput,
            hotWaterDemandResultAfterSolarThermal,
            "[HOT WATER DEMAND AFTER SOLAR THERMAL]"
        )
        val hotWaterDemandResultAfterSolarPlant = solarPlantProduction.deductFromHotWaterDemand(
            hotWaterDemandResultAfterSolarThermal,
            solarPlantProductionResult,
            buildingCalculationInput.heating
        )
        log(buildingCalculationInput, hotWaterDemandResultAfterSolarPlant, "[HOT WATER DEMAND AFTER SOLAR PLANT]")
        val electricityDemandResultAfterSolarPlant = solarPlantProduction.deductFromElectricityDemand(
            electricityDemandResult,
            hotWaterDemandResultAfterSolarPlant.second
        )
        log(
            buildingCalculationInput,
            electricityDemandResultAfterSolarPlant,
            "[ELECTRICITY DEMAND AFTER SOLAR PLANT]"
        )
        val finalEffectiveCoolingEnergyResultAfterSolarPlant = solarPlantProduction.deductFromCoolingEnergyDemand(
            finalEffectiveCoolingEnergyResult,
            electricityDemandResultAfterSolarPlant.second,
            buildingCalculationInput
        )
        log(
            buildingCalculationInput,
            finalEffectiveCoolingEnergyResultAfterSolarPlant,
            "[COOLING ENERGY DEMAND AFTER SOLAR PLANT]"
        )
        val finalEffectiveHeatingEnergyResultAfterSolarPlant = solarPlantProduction.deductFromHeatingEnergyDemand(
            finalEffectiveHeatingEnergyResult,
            electricityDemandResultAfterSolarPlant.second,
            buildingCalculationInput.heating
        )
        log(
            buildingCalculationInput,
            finalEffectiveHeatingEnergyResultAfterSolarPlant,
            "[HEATING ENERGY DEMAND AFTER SOLAR PLANT]"
        )
        return EnergyDemand(
            electricityDemandResult = electricityDemandResultAfterSolarPlant.first,
            hotWaterDemandResult = hotWaterDemandResultAfterSolarPlant.first.annualWaterDemandKWH,
            coolingEnergyDemandResult = finalEffectiveCoolingEnergyResultAfterSolarPlant.first,
            heatingEnergyDemandResult = finalEffectiveHeatingEnergyResultAfterSolarPlant.first
        )
    }

    private fun calculateFinalEnergyDemand(
        buildingCalculationInput: BuildingCalculationInput,
        electricityDemandResult: Double,
        hotWaterDemandResult: Double,
        finalEffectiveCoolingEnergyResult: Double,
        finalEffectiveHeatingEnergyResult: Double,
        summerHeatEnergyGainKWH: Double,
        winterHeatEnergyGainKWH: Double
    ): EnergyDemand {
        val finalEffectiveHeatingEnergy = maxOf(0.0,finalEffectiveHeatingEnergyResult - winterHeatEnergyGainKWH)
        var finalEffectiveCoolingEnergy = 0.0

        if(buildingCalculationInput.fixedAttributes.acInstalled){
           finalEffectiveCoolingEnergy = (finalEffectiveCoolingEnergyResult + summerHeatEnergyGainKWH)
        }
        return EnergyDemand(
            electricityDemandResult = electricityDemandResult,
            hotWaterDemandResult = hotWaterDemandResult,
            coolingEnergyDemandResult = finalEffectiveCoolingEnergy,
            heatingEnergyDemandResult = finalEffectiveHeatingEnergy
        )
    }

    private fun calculatePrimaryEnergyDemand(
        buildingCalculationInput: BuildingCalculationInput,
        electricityDemandResult: Double,
        hotWaterDemandResult: Double,
        finalEffectiveCoolingEnergyResult: Double,
        finalEffectiveHeatingEnergyResult: Double,
        summerHeatEnergyGainKWH: Double,
        winterHeatEnergyGainKWH: Double
    ): Double {
        val electricitySourcePEF = findElectricitySourcePEF()
        val electricDemandWithPEF = electricityDemandResult * electricitySourcePEF
        val hotWaterDemandWithPEF = hotWaterDemandResult * findHotWaterSourcePEF(buildingCalculationInput.heating)
        val finalEffectiveHeatingEnergyWithPEF =
            maxOf(0.0,(finalEffectiveHeatingEnergyResult - winterHeatEnergyGainKWH)) *
                    findHeatingSourcePEF(buildingCalculationInput.heating)
        val finalEffectiveCoolingEnergyWithPEF =
            (finalEffectiveCoolingEnergyResult + summerHeatEnergyGainKWH) * electricitySourcePEF

        val primaryEnergyDemand =
            electricDemandWithPEF + hotWaterDemandWithPEF + finalEffectiveHeatingEnergyWithPEF
        if(buildingCalculationInput.fixedAttributes.acInstalled){
            return primaryEnergyDemand + finalEffectiveCoolingEnergyWithPEF
        }
        return primaryEnergyDemand
    }

    private suspend fun calculateHeatEnergyGains(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult,
        climateDataResult: ClimateDataResult,
        electricityDemandResult: Double
    ): HeatEnergyGainResult = coroutineScope {
        val appliancesHeatEnergyGainJob = async {
            val result = appliancesHeatEnergyGain.calculate(
                buildingCalculationInput,
                climateDataResult,
                electricityDemandResult
            )
            log(buildingCalculationInput, result, "[APPLIANCES HEAT ENERGY GAINS]")
            result
        }
        val lightHeatEnergyGainJob = async {
            val result = lightHeatEnergyGain.calculate(buildingShapeResult, climateDataResult, electricityDemandResult)
            log(buildingCalculationInput, result, "[LIGHTING HEAT ENERGY GAINS]")
            result
        }
        val occupantsHeatEnergyGainJob = async {
            val result = occupantsHeatEnergyGain.calculate(buildingCalculationInput.fixedAttributes, climateDataResult)
            log(buildingCalculationInput, result, "[OCCUPANTS HEAT ENERGY GAINS]")
            result
        }
        val solarHeatEnergyGainJob = async {
            val result =
                solarHeatEnergyGain.calculate(buildingCalculationInput, buildingShapeResult, climateDataResult)
            log(buildingCalculationInput, result, "[SOLAR HEAT ENERGY GAINS]")
            result
        }

        val appliancesHeatEnergyGainResult = appliancesHeatEnergyGainJob.await()
        val lightHeatEnergyGainResult = lightHeatEnergyGainJob.await()

        val occupantsHeatEnergyGainResult = occupantsHeatEnergyGainJob.await()
        val solarHeatEnergyGainResult = solarHeatEnergyGainJob.await()

        return@coroutineScope HeatEnergyGainResult(
            summerHeatEnergyGainKWH =
            appliancesHeatEnergyGainResult.summerHeatEnergyGainKWH +
                    lightHeatEnergyGainResult.summerHeatEnergyGainKWH +
                    occupantsHeatEnergyGainResult.summerHeatEnergyGainKWH +
                    solarHeatEnergyGainResult.summerHeatEnergyGainKWH,
            winterHeatEnergyGainKWH =
            appliancesHeatEnergyGainResult.winterHeatEnergyGainKWH +
                    lightHeatEnergyGainResult.winterHeatEnergyGainKWH +
                    occupantsHeatEnergyGainResult.winterHeatEnergyGainKWH +
                    solarHeatEnergyGainResult.winterHeatEnergyGainKWH
        )
    }


    private fun findHeatingSourcePEF(buildingCalculationInputHeating: BuildingCalculationInput.Heating): Double {
        return primaryEnergyFactorRepository.findByEnergySource(buildingCalculationInputHeating.heatingEnergySource)?.pef
            ?: throw EPCCalculationException("Can't find PEF for ${buildingCalculationInputHeating.heatingEnergySource}")
    }

    private fun findHotWaterSourcePEF(buildingCalculationInputHeating: BuildingCalculationInput.Heating): Double {
        return primaryEnergyFactorRepository.findByEnergySource(buildingCalculationInputHeating.hotWaterEnergySource)?.pef
            ?: throw EPCCalculationException("Can't find PEF for ${buildingCalculationInputHeating.hotWaterEnergySource}")
    }

    private fun findElectricitySourcePEF(): Double {
        return primaryEnergyFactorRepository.findByEnergySource(SystemEfficiencyType.ELECTRICITY)?.pef
            ?: throw EPCCalculationException("Can't find PEF for ${SystemEfficiencyType.ELECTRICITY}")
    }


    private fun findHeatingSourceCarbonEmissionFactor(buildingCalculationInputHeating: BuildingCalculationInput.Heating): Double {
        return carbonEmissionFactorRepository.findByEnergySource(buildingCalculationInputHeating.heatingEnergySource)?.factor
            ?: throw EPCCalculationException("Can't find carbon emission factor for ${buildingCalculationInputHeating.heatingEnergySource}")
    }

    private fun findHotWaterSourceCarbonEmissionFactor(buildingCalculationInputHeating: BuildingCalculationInput.Heating): Double {
        return carbonEmissionFactorRepository.findByEnergySource(buildingCalculationInputHeating.hotWaterEnergySource)?.factor
            ?: throw EPCCalculationException("Can't find carbon emission factor for ${buildingCalculationInputHeating.hotWaterEnergySource}")
    }

    private fun findElectricitySourceCarbonEmissionFactor(): Double {
        return carbonEmissionFactorRepository.findByEnergySource(SystemEfficiencyType.ELECTRICITY)?.factor
            ?: throw EPCCalculationException("Can't find carbon emission factor for ${SystemEfficiencyType.ELECTRICITY}")
    }

    private fun log(
        buildingCalculationInput: BuildingCalculationInput,
        loggedResult: Any,
        loggedResultName: String
    ) {
        log.debug(
            "{} buildingInfoId: {}. Calculated: {}",
            loggedResultName,
            buildingCalculationInput.fixedAttributes.buildingId,
            loggedResult
        )
    }
}