package de.finacte.epc.service.admin

import de.finacte.epc.dto.building.BuildingMissingAttributesGenerated
import de.finacte.epc.mapper.BuildingMissingAttributesGeneratedMapper
import de.finacte.epc.mapper.ClimateDataMapper
import de.finacte.epc.repository.BuildingMissingAttributesGeneratedRepository
import de.finacte.epc.repository.ClimateDataRepository
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class AdministrationCalculationResultsServiceImpl(
    val buildingMissingAttributesGeneratedRepository: BuildingMissingAttributesGeneratedRepository,
    val buildingMissingAttributesGeneratedMapper: BuildingMissingAttributesGeneratedMapper,
    val climateDataRepository: ClimateDataRepository,
    val climateDataMapper: ClimateDataMapper
) : AdministrationCalculationResultsService {

    override fun getBuildingMissingAttributesGenerated(buildingId: UUID): BuildingMissingAttributesGenerated? {
        return buildingMissingAttributesGeneratedRepository.findByBuildingId(buildingId)
            ?.let { buildingMissingAttributesGeneratedMapper.toDto(it) }
    }

    override fun getClimateData(zipCode: String): ClimateDataResult? {
        return climateDataRepository.findByZipCode(zipCode)
            ?.let { climateDataMapper.toDto(it) }
    }
}