package de.finacte.epc.service.calculation.renovation.renovationStrategy

import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service

@Service
class FacadeExteriorCladdingRenovationStrategy : RenovationStrategy() {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun apply(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateDto,
    ): BuildingCalculationInput {
        log.debug(
            "[RENOVATION APPLY - {}] -Template: {} ",
            getRenovatedElementKeyForStrategy(),
            renovationTemplate
        )
        // Return the same buildingCalculationInput that was received - renovation do not bring any changes to building
        return buildingCalculationInput
    }

    override fun getRenovatedElementKeyForStrategy(): RenovationMeasureType =
        RenovationMeasureType.FACADE_EXTERIOR_CLADDING
}
