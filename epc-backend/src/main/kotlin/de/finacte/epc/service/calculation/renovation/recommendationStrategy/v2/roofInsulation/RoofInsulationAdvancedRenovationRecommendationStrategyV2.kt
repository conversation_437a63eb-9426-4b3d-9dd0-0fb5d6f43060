package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v2.roofInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class RoofInsulationAdvancedRenovationRecommendationStrategyV2 : RenovationRecommendationStrategy() {

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Bo<PERSON>an {
        if (buildingCalculationInput.roofInsulation == null) {
            return true
        }
        return buildingCalculationInput.roofInsulation.roofInsulationYear + 15 < Year.now().value

    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.ROOF_INSULATION,
            RenovationMeasureValue.VACUUM_INSULATED_PANEL_5_CM_2025,
            VERSION_2
        )
}