package de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators

import de.finacte.epc.entity.building.BuildingEntity
import de.finacte.epc.entity.building.BuildingFloorFinishType
import de.finacte.epc.entity.building.BuildingScreedType
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service

@Service
class BuildingMissingAttributesInputStaticGeneratorImpl : BuildingMissingAttributesInputStaticGenerator {

    companion object {
        private val log by LoggerDelegate()

        private const val ANHYDRITE_SCREED_THICKNESS = 7
        private const val CEMENT_SCREED_THICKNESS = 10
        private const val CEMENT_WITH_ADDITIVES_SCREED_THICKNESS = 7

        private const val WOODEN_FINISH_THICKNESS = 2
        private const val CERAMIC_TILES_THICKNESS = 1
    }

    override fun calculateGroundFloor(building: BuildingEntity): BuildingCalculationInput.GroundFloor {
        val constructionYear = building.constructionYear

        val groundFloor = when {
            constructionYear < 1990 -> BuildingCalculationInput.GroundFloor(
                groundFloorThickness = CEMENT_SCREED_THICKNESS,
                groundFloorType = BuildingScreedType.CEMENT,
                groundFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
                groundFloorFinishThickness = WOODEN_FINISH_THICKNESS
            )

            constructionYear in 1990..2000 -> BuildingCalculationInput.GroundFloor(
                groundFloorThickness = ANHYDRITE_SCREED_THICKNESS,
                groundFloorType = BuildingScreedType.ANHYDRITE,
                groundFloorFinishType = BuildingFloorFinishType.CERAMIC_TILES,
                groundFloorFinishThickness = CERAMIC_TILES_THICKNESS
            )

            else -> BuildingCalculationInput.GroundFloor(
                groundFloorThickness = CEMENT_WITH_ADDITIVES_SCREED_THICKNESS,
                groundFloorType = BuildingScreedType.CEMENT_WITH_ADDITIVES,
                groundFloorFinishType = BuildingFloorFinishType.CERAMIC_TILES,
                groundFloorFinishThickness = CERAMIC_TILES_THICKNESS
            )
        }

        log.debug(
            "[GROUND FLOOR CALCULATION] Construction year: {}, Floor type: {}, Floor thickness: {}, " +
                    "Floor finish type: {}, Floor finish thickness: {}",
            constructionYear,
            groundFloor.groundFloorType,
            groundFloor.groundFloorThickness,
            groundFloor.groundFloorFinishType,
            groundFloor.groundFloorFinishThickness
        )

        return groundFloor
    }

    override fun calculateBasementFloor(building: BuildingEntity): BuildingCalculationInput.BasementFloor {
        val constructionYear = building.constructionYear

        val basementFloor = when {
            constructionYear < 1990 -> BuildingCalculationInput.BasementFloor(
                basementFloorThickness = CEMENT_SCREED_THICKNESS,
                basementFloorType = BuildingScreedType.CEMENT,
                basementFloorFinishType = BuildingFloorFinishType.WOODEN_PLANKS,
                basementFloorFinishThickness = WOODEN_FINISH_THICKNESS
            )

            constructionYear in 1990..2000 -> BuildingCalculationInput.BasementFloor(
                basementFloorThickness = ANHYDRITE_SCREED_THICKNESS,
                basementFloorType = BuildingScreedType.ANHYDRITE,
                basementFloorFinishType = BuildingFloorFinishType.CERAMIC_TILES,
                basementFloorFinishThickness = CERAMIC_TILES_THICKNESS
            )

            else -> BuildingCalculationInput.BasementFloor(
                basementFloorThickness = CEMENT_WITH_ADDITIVES_SCREED_THICKNESS,
                basementFloorType = BuildingScreedType.CEMENT_WITH_ADDITIVES,
                basementFloorFinishType = BuildingFloorFinishType.CERAMIC_TILES,
                basementFloorFinishThickness = CERAMIC_TILES_THICKNESS
            )
        }

        log.debug(
            "[BASEMENT FLOOR CALCULATION] Construction year: {}, Floor type: {}, Floor thickness: {}, " +
                    "Floor finish type: {}, Floor finish thickness: {}",
            constructionYear,
            basementFloor.basementFloorType,
            basementFloor.basementFloorThickness,
            basementFloor.basementFloorFinishType,
            basementFloor.basementFloorFinishThickness
        )

        return basementFloor
    }
}