package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v2.facadeWallInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

@Service
class FacadeWallInsulationBasicAfter1979RenovationRecommendationStrategyV2 : RenovationRecommendationStrategy(),
    FacadeWallInsulationRenovationRecommendationStrategyV2 {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        return super.isRecommendedStrategy(buildingCalculationInput, renovationTemplate)
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = buildingCalculationInput.fixedAttributes.constructionYear > 1979

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            RenovationMeasureValue.VACUUM_INSULATED_PANEL_5_CM_2025,
            VERSION_2
        )
}