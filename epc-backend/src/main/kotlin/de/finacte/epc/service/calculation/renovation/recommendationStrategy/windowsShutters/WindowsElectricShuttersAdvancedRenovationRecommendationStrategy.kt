package de.finacte.epc.service.calculation.renovation.recommendationStrategy.windowsShutters

import de.finacte.epc.entity.building.BuildingWindowsShutters
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

@Service
class WindowsElectricShuttersAdvancedRenovationRecommendationStrategy : RenovationRecommendationStrategy() {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean =
        buildingCalculationInput.windowsShutters == null ||
                buildingCalculationInput.windowsShutters.windowsShutters == BuildingWindowsShutters.MANUAL

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, RenovationMeasureVariant> =
        Triple(
            RenovationMeasureType.WINDOWS_SHUTTERS,
            RenovationMeasureValue.ELECTRIC_SHUTTERS,
            RenovationMeasureVariant.ADVANCED
        )
}
