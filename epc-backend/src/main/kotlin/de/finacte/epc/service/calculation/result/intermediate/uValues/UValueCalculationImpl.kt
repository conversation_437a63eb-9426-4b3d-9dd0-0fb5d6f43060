package de.finacte.epc.service.calculation.result.intermediate.uValues

import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.v2.UValueCalculationStrategy
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service

@Service
class UValueCalculationImpl(
    private val uValueCalculationStrategies: Set<UValueCalculationStrategy>
) : UValueCalculation {
    companion object {
        private val log by LoggerDelegate()
    }

    override suspend fun calculate(
        buildingShapeResult: BuildingShapeResult,
        buildingCalculationInput: BuildingCalculationInput
    ): UValueCalculationResult {
        val uValues = buildingShapeResult.thermalEnvelopeElements.stream().map { thermalEnvelopeElement ->
            log.debug(
                "[U-VALUE CALCULATION] Calculate U values for building ID: {} and thermal envelope element type: {}",
                buildingCalculationInput.fixedAttributes.buildingId,
                thermalEnvelopeElement.type
            )
            val uValueCalculationStrategy =
                uValueCalculationStrategies
                    .find { it.getThermalEnvelopeElementTypeForStrategy() == thermalEnvelopeElement.type }
                    ?: throw EPCCalculationException("[U-VALUE CALCULATION] Can't find strategy for ${thermalEnvelopeElement.type}")

            uValueCalculationStrategy.apply(buildingCalculationInput, buildingShapeResult)
        }.toList().toSet()
        return UValueCalculationResult(
            uValuesThermalEnvelopeElements = uValues
        )
    }
}



