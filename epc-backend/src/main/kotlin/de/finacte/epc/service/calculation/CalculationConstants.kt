package de.finacte.epc.service.calculation

object CalculationConstants {
    const val WATT_TO_KILOWATT_RATIO = 1000
    const val DAYS_PER_YEAR = 365
    const val HOURS_PER_DAY = 24
    const val CM_IN_METER = 100
    const val STANDARD_AVG_BASEMENT_HEIGHT = 2.2
}

enum class Season(val radiationRatio: Double, val days: Int) {
    SPRING(0.3, 92),
    SUMMER(0.45, 92),
    AUTUMN(0.18, 91),
    WINTER(0.07, 90)
}