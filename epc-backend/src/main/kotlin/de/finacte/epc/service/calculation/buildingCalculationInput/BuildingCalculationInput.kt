package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.*
import java.util.*

data class BuildingCalculationInput(
    val fixedAttributes: FixedAttributes,
    val heating: Heating,
    val roofMaterial: RoofMaterial,
    val roofInsulation: RoofInsulation?,
    val basementHeating: BasementHeating,
    val basementInsulation: BasementInsulation?,
    val ceilingWallInsulation: CeilingWallInsulation?,
    val entranceDoor: EntranceDoor,
    val groundFloorInsulation: GroundFloorInsulation?,
    val solarThermal: SolarThermal,
    val solarPlant: SolarPlant,
    val facadeWallInsulation: FacadeWallInsulation?,
    val windowsGlazing: WindowsGlazing,
    val windowsFrame: WindowsFrame,
    val ventHeatExchange: VentHeatExchange,
    val windowsShutters: WindowsShutters?,
    val windowsCoating: WindowsCoating,
) {
    data class FixedAttributes(
        val buildingId: UUID,
        val shape: BuildingShape,
        val position: BuildingPosition,
        val zipCode: String,
        val constructionYear: Int,
        val area: Double,
        val floors: Int,
        val floorHeight: Double,
        val tenants: Int,
        val roofFloor: BuildingRoofFloor,
        @Generated
        val facadeWallType: BuildingWallType,
        @Generated
        val facadeWallThickness: Int,
        val windowsToWallRatio: BuildingWindowsToWallRatio,
        val basementExist: Boolean,
        @Generated
        val basementFloorThickness: Int,
        @Generated
        val basementFloorType: BuildingWallType,
        @Generated
        val basementExternalWallsType: BuildingWallType,
        @Generated
        val ceilingWallType: BuildingWallType,
        @Generated
        val ceilingWallThickness: Int,
        @Generated
        val groundFloorThickness: Int,
        @Generated
        val groundFloorType: BuildingWallType,
        val acInstalled: Boolean,
    )

    data class Heating(
        val heatingEnergySource: SystemEfficiencyType,
        val hotWaterEnergySource: SystemEfficiencyType,
        val heatingInstallationYear: Int,
        val heatingInstallationModernized: Boolean,
    )

    data class RoofMaterial(
        @Generated
        val roofMaterial: BuildingRoofMaterial
    )

    data class RoofInsulation(
        val roofInsulationRenewed: Boolean,
        val roofInsulationYear: Int,
        @Generated
        val roofInsulationThickness: Int,
        @Generated
        val roofInsulationType: BuildingInsulationType,
    )

    data class BasementHeating(
        val basementHeated: Boolean
    )

    data class BasementInsulation(
        @Generated
        val basementFloorInsulationType: BuildingInsulationType,
        @Generated
        val basementFloorInsulationThickness: Int,
        @Generated
        val basementExternalWallsInsulationType: BuildingInsulationType,
        @Generated
        val basementExternalWallsInsulationThickness: Int,
        val basementInsulationYear: Int,
    )

    data class CeilingWallInsulation(
        @Generated
        val ceilingWallInsulationType: BuildingInsulationType,
        @Generated
        val ceilingWallInsulationThickness: Int
    )

    data class EntranceDoor(
        @Generated
        val entranceDoorMaterial: BuildingDoorMaterial
    )

    data class GroundFloorInsulation(
        @Generated
        val groundFloorInsulationType: BuildingInsulationType,
        @Generated
        val groundFloorInsulationThickness: Int,
        val groundFloorInsulationYear: Int,
    )

    data class SolarThermal(
        val solarThermalExist: Boolean
    )

    data class SolarPlant(
        val solarPlantExist: Boolean,
        val solarPlantNominalPower: Double?,
        val solarPlantInstallationYear: Int
    )

    data class FacadeWallInsulation(
        val facadeWallInsulationYear: Int,
        val facadeWallInsulationRenewed: Boolean,
        @Generated
        val facadeWallInsulationType: BuildingInsulationType,
        @Generated
        val facadeWallInsulationThickness: Int,
    )

    data class WindowsGlazing(
        val windowsGlazing: BuildingWindowsGlazing,
        val windowsRenewed: Boolean,
        val windowsInstallationYear: Int,
    )

    data class WindowsCoating(
        @Generated
        val windowsCoatingType: BuildingWindowCoatingType,
    )

    data class WindowsFrame(
        @Generated
        val windowsFrameMaterial: BuildingWindowFrameMaterial,
    )

    data class WindowsShutters(
        val windowsShutters: BuildingWindowsShutters,
    )

    data class VentHeatExchange(
        val ventHeatExchangeInstalled: Boolean,
    )
}