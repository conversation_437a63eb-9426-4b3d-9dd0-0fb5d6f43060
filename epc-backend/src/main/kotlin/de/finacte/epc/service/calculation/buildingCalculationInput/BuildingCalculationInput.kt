package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.*
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.GPTGenerated
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.StaticallyGenerated
import java.util.*

data class BuildingCalculationInput(
    val fixedAttributes: FixedAttributes,
    val heating: Heating,
    val roofMaterial: RoofMaterial,
    val roofInsulation: RoofInsulation?,
    val basementHeating: BasementHeating,
    val basementInsulation: BasementInsulation?,
    val ceilingWallInsulation: CeilingWallInsulation?,
    val entranceDoor: EntranceDoor,
    val groundFloorInsulation: GroundFloorInsulation?,
    val solarThermal: SolarThermal,
    val solarPanels: SolarPanels,
    val facadeWallInsulation: FacadeWallInsulation?,
    val windowsGlazing: WindowsGlazing,
    val windowsFrame: WindowsFrame,
    val ventHeatExchange: VentHeatExchange,
    val windowsShutters: WindowsShutters?,
    val windowsCoating: WindowsCoating,
    val surfaceHeating: SurfaceHeating?,
    val heatingRadiators: HeatingRadiators,
    val intelligentHeatingControls: IntelligentHeatingControls,
    val lightingAutomation: LightingAutomation,
    val solarPanelsBattery: SolarPanelsBattery?,
    val solarPanelsImmersionHeater: SolarPanelsImmersionHeater,
    val groundFloor: GroundFloor,
    val basementFloor: BasementFloor,
) {
    data class FixedAttributes(
        val buildingId: UUID,
        val shape: BuildingShape,
        val position: BuildingPosition,
        val zipCode: String,
        val constructionYear: Int,
        val area: Double,
        val floors: Int,
        val floorHeight: Double,
        val tenants: Int,
        val roofFloor: BuildingRoofFloor,
        @GPTGenerated
        val facadeWallType: BuildingWallType,
        @GPTGenerated
        val facadeWallThickness: Int,
        val windowsToWallRatio: BuildingWindowsToWallRatio,
        val basementExist: Boolean,
        @GPTGenerated
        val basementExternalWallsType: BuildingWallType,
        @GPTGenerated
        val ceilingWallType: BuildingWallType,
        @GPTGenerated
        val ceilingWallThickness: Int,
        val acInstalled: Boolean,
    )

    data class Heating(
        val heatingEnergySource: SystemEfficiencyType,
        val hotWaterEnergySource: SystemEfficiencyType,
        val heatingInstallationYear: Int,
        val heatingInstallationModernized: Boolean,
    )

    data class SurfaceHeating(
        val deliverySystem: BuildingHeatingDeliverySystem
    )

    data class HeatingRadiators(
        val radiatorType: BuildingHeatingRadiatorType
    )

    data class IntelligentHeatingControls(
        val installed: Boolean
    )

    data class LightingAutomation(
        val installed: Boolean
    )

    data class RoofMaterial(
        @GPTGenerated
        val roofMaterial: BuildingRoofMaterial
    )

    data class RoofInsulation(
        val roofInsulationRenewed: Boolean,
        val roofInsulationYear: Int,
        @GPTGenerated
        val roofInsulationThickness: Int,
        @GPTGenerated
        val roofInsulationType: BuildingInsulationType,
    )

    data class BasementHeating(
        val basementHeated: Boolean
    )

    data class BasementInsulation(
        @GPTGenerated
        val basementFloorInsulationType: BuildingInsulationType,
        @GPTGenerated
        val basementFloorInsulationThickness: Int,
        @GPTGenerated
        val basementExternalWallsInsulationType: BuildingInsulationType,
        @GPTGenerated
        val basementExternalWallsInsulationThickness: Int,
        val basementInsulationYear: Int,
    )

    data class CeilingWallInsulation(
        @GPTGenerated
        val ceilingWallInsulationType: BuildingInsulationType,
        @GPTGenerated
        val ceilingWallInsulationThickness: Int
    )

    data class EntranceDoor(
        @GPTGenerated
        val entranceDoorMaterial: BuildingDoorMaterial
    )

    data class GroundFloorInsulation(
        @GPTGenerated
        val groundFloorInsulationType: BuildingInsulationType,
        @GPTGenerated
        val groundFloorInsulationThickness: Int,
        val groundFloorInsulationYear: Int,
    )

    data class SolarThermal(
        val solarThermalExist: Boolean
    )

    data class SolarPanels(
        val solarPanelsExist: Boolean,
        val solarPanelsNominalPower: Double?,
        val installationYear: Int,
    )

    data class SolarPanelsBattery(
        val batteryType: SolarPanelsBatteryType,
    )

    data class SolarPanelsImmersionHeater(
        val installed: Boolean,
    )

    data class FacadeWallInsulation(
        val facadeWallInsulationYear: Int,
        val facadeWallInsulationRenewed: Boolean,
        @GPTGenerated
        val facadeWallInsulationType: BuildingInsulationType,
        @GPTGenerated
        val facadeWallInsulationThickness: Int,
    )

    data class WindowsGlazing(
        val windowsGlazing: BuildingWindowsGlazing,
        val windowsRenewed: Boolean,
        val windowsInstallationYear: Int,
    )

    data class WindowsCoating(
        @GPTGenerated
        val windowsCoatingType: BuildingWindowCoatingType,
    )

    data class WindowsFrame(
        @GPTGenerated
        val windowsFrameMaterial: BuildingWindowFrameMaterial,
    )

    data class WindowsShutters(
        val windowsShutters: BuildingWindowsShutters,
    )

    data class VentHeatExchange(
        val ventHeatExchangeInstalled: Boolean,
    )

    data class GroundFloor (
        @StaticallyGenerated
        val groundFloorThickness: Int,
        @StaticallyGenerated
        val groundFloorType: BuildingScreedType,
        @StaticallyGenerated
        val groundFloorFinishType: BuildingFloorFinishType,
        @StaticallyGenerated
        val groundFloorFinishThickness: Int
    )

    data class BasementFloor (
        @StaticallyGenerated
        val basementFloorThickness: Int,
        @StaticallyGenerated
        val basementFloorType: BuildingScreedType,
        @StaticallyGenerated
        val basementFloorFinishType: BuildingFloorFinishType,
        @StaticallyGenerated
        val basementFloorFinishThickness: Int
    )
}