package de.finacte.epc.service.calculation.result.intermediate.finalEffectiveCoolingEnergy

import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult

interface FinalEffectiveCoolingEnergy {
    fun calculate(
        climateDataResult: ClimateDataResult,
        buildingCalculationInputFixedAttributes: BuildingCalculationInput.FixedAttributes,
        heatTransferCoefficientResult: Double,
        ventilationLossCoefficientResult: Double
    ): Double
}