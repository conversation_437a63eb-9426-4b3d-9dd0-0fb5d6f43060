package de.finacte.epc.service.calculation.result.intermediate.electricityDemand

import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class V1ElectricDemand(
    @Value("\${epc.calculation.electricity-consumption.single-tenant-household-avg-consumption-per-year-kwh}")
    private val singleTenantHouseholdAvgConsumptionPerYearKWH: Double,
    @Value("\${epc.calculation.electricity-consumption.tenant-avg-consumption-per-year-kwh}")
    private val tenantAvgConsumptionPerYearKWH: Double
) : ElectricityDemand {
    override suspend fun calculate(buildingCalculationInputFixedAttributes: BuildingCalculationInput.FixedAttributes): Double {
        return when (val numberOfTenants = buildingCalculationInputFixedAttributes.tenants) {
            0 -> 0.0
            1 -> singleTenantHouseholdAvgConsumptionPerYearKWH
            else -> {
                val base = singleTenantHouseholdAvgConsumptionPerYearKWH
                val tenantsNumberWithoutBase = numberOfTenants - 1
                val result = base + (tenantsNumberWithoutBase * tenantAvgConsumptionPerYearKWH)
                result
            }
        }
    }
}