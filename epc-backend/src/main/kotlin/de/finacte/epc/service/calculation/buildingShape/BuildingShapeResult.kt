package de.finacte.epc.service.calculation.buildingShape

import de.finacte.epc.entity.building.ThermalEnvelopeElementType


data class BuildingShapeResult(
    val buildingVolume: Double,
    val buildingArea: Double,
    val buildingHeatedArea: Double,
    val buildingThermalEnvelopeVolume: Double,
    val buildingRoofSurfaceArea: Double,
    val buildingBaseArea: Double,
    val buildingPerimeter: Double,
    val thermalEnvelopeElements: Set<ThermalEnvelopeElement>,
    val roofFloorVolume: Double
) {
    data class ThermalEnvelopeElement(
        val area: Double,
        val type: ThermalEnvelopeElementType
    )
}
