package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v2.basementInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class BasementInsulationAdvancedRenovationRecommendationStrategyV2 : RenovationRecommendationStrategy() {

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Bo<PERSON>an {
        if (buildingCalculationInput.basementInsulation == null) {
            return true
        }
        return buildingCalculationInput.basementInsulation.basementInsulationYear + 15 < Year.now().value
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = buildingCalculationInput.basementHeating.basementHeated

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.BASEMENT_INSULATION,
            RenovationMeasureValue.AEROGEL_1_CM_2025,
            VERSION_2
        )
}