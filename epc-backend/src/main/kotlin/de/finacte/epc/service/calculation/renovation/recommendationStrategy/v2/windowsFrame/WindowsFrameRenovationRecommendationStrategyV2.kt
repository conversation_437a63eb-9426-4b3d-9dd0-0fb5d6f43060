package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v2.windowsFrame

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class WindowsFrameRenovationRecommendationStrategyV2 : RenovationRecommendationStrategy() {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean = buildingCalculationInput.windowsGlazing.windowsInstallationYear + 10 < Year.now().value

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.WINDOWS_FRAME_MATERIAL_UPGRADE,
            RenovationMeasureValue.DEFAULT,
            VERSION_2
        )
}