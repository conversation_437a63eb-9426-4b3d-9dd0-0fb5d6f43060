package de.finacte.epc.service.calculation.result.intermediate.finalEffectiveCoolingEnergy

import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.CalculationConstants.HOURS_PER_DAY
import de.finacte.epc.service.calculation.CalculationConstants.WATT_TO_KILOWATT_RATIO
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import org.springframework.stereotype.Service

@Service
class V1FinalEffectiveCoolingEnergy(
    private val systemEfficiencyRepository: SystemEfficiencyRepository
) : FinalEffectiveCoolingEnergy {

    override fun calculate(
        climateDataResult: ClimateDataResult,
        buildingCalculationInputFixedAttributes: BuildingCalculationInput.FixedAttributes,
        heatTransferCoefficientResult: Double,
        ventilationLossCoefficientResult: Double
    ): Double {
        if(!buildingCalculationInputFixedAttributes.acInstalled){
            return 0.0
        }

        val coolingDegreeHours = climateDataResult.coolingDegreeDays * HOURS_PER_DAY

        val rawCoolingDemand =
            ((heatTransferCoefficientResult + ventilationLossCoefficientResult) *
                    coolingDegreeHours / WATT_TO_KILOWATT_RATIO)

        val coolingSourceEfficiency =
            findCoolingSourceEfficiency()
        return rawCoolingDemand / coolingSourceEfficiency
    }

    private fun findCoolingSourceEfficiency(): Double {
        return systemEfficiencyRepository.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
            SystemEfficiencyType.AC,
            2010 // TODO - we might introduce installation year for this system. 2010 for backward compatibility
        )?.efficiency
            ?: throw EPCCalculationException("Can't find energy efficiency for ${SystemEfficiencyType.ELECTRICITY}")
    }
}