package de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators

import de.finacte.epc.dto.building.generated.BuildingMissingAttributesGenerated
import de.finacte.epc.entity.building.BuildingEntity
import de.finacte.epc.service.gpt.GPTChat
import de.finacte.epc.service.gpt.GPTChatOpenAIImpl
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.ai.chat.messages.SystemMessage
import org.springframework.ai.chat.messages.UserMessage
import org.springframework.stereotype.Service
import java.time.Instant

@Service
class BuildingMissingAttributesInputGPTGeneratorImpl(
    val gptChat: GPTChat
) : BuildingMissingAttributesInputGPTGenerator {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun calculate(building: BuildingEntity): BuildingMissingAttributesGenerated {
        val startTime = Instant.now()
        log.info("Calculate missing building attributes input for building ID: {}", building.id)
        val userMessage = generateUserMessage(building)
        val systemMessage = generateSystemMessage(building)

        val gptResponse =
            gptChat.call(
                setOf(systemMessage, userMessage),
                BuildingMissingAttributesGenerated::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_4O_MINI
            )
        val totalDuration = Instant.now().toEpochMilli() - startTime.toEpochMilli()
        log.debug(
            "[BUILDING ATTRIBUTES] Building missing attributes generated, building ID: {} in {} ms," +
                    "UserMessage: {} \n" +
                    "SystemMessage: {}\n" +
                    "Generated values:\n{}",
            building.id, totalDuration, gptResponse, userMessage, systemMessage
        )
        log.debug(
            "\nBuilding missing building attributes  GPT query UserMessage: {} \nSystemMessage: {}",
            userMessage.text, systemMessage.text
        )
        return gptResponse as BuildingMissingAttributesGenerated
    }

    fun generateUserMessage(building: BuildingEntity): UserMessage {
        val roofInsulationYear = building.roof?.insulationYear ?: building.constructionYear
        val message = StringBuilder()
            .appendLine("The Building is located in Germany ")
            .appendLine("The Zipcode of the building is ${building.zipCode} ")
            .appendLine("Basic building information: ")
            .appendLine("- building was built in ${building.constructionYear} ")
            .appendLine("- building dwelling type: ${building.shape.gptText} ")
            .appendLine("Roof: ")
            .appendLine("- type: ${requireNotNull(building.roof?.floor?.slopeGptText)}")
            .appendLine("- roof insulation year $roofInsulationYear")
            .append(generateWindowsMessage(building))
            .append(generateFacadeMessage(building))

        return UserMessage(
            message.toString()
        )
    }

    fun generateSystemMessage(building: BuildingEntity): SystemMessage {
        val message = StringBuilder()
            .appendLine("User will provide information about the building, ")
            .appendLine("estimate additional information for this building.")
            .appendLine("Do not return insulation thickness more than 10 cm.")
            .append("Base insulation info on the building’s construction year or insulation date if given.")
            .append("Even if renovated, choose renovation options suitable for the original construction type,")
            .append("as some of the modern methods may not fit older buildings.")

        return SystemMessage(
            message.toString()
        )
    }

    private fun generateFacadeMessage(building: BuildingEntity): String {
        val message = StringBuilder()
        if (requireNotNull(building.facade?.insulated)) {
            message.appendLine("External walls:")
            val insulationYear = building.facade?.insulationYear ?: building.constructionYear
            message.appendLine("- wall insulation year $insulationYear").toString()
        }
        return message.toString()
    }

    private fun generateWindowsMessage(building: BuildingEntity): String {
        with(requireNotNull(building.windows)) {
            val message = StringBuilder()
                .appendLine("Windows: ")
                .appendLine("- window glazing type: ${glazing.gptText}")
            if (installationYear != null) {
                message.appendLine("- renewed in: ${installationYear}")
            } else {
                message.appendLine("- production year: ${building.constructionYear}")
            }

            if (shutters != null) {
                message.appendLine("- shutters: ${shutters.gptText}")
            }
            return message.toString()
        }
    }
}