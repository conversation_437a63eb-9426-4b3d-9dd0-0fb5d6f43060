package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v2.roofCoverage

import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

@Service
class RoofCoverageMultiTenantAdvancedAfter1949RenovationRecommendationStrategyV2 : RenovationRecommendationStrategy(),
    RoofCoverageRenovationRecommendationStrategyV2 {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        return super.isRecommendedStrategy(buildingCalculationInput, renovationTemplate)
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = buildingCalculationInput.fixedAttributes.constructionYear > 1949
            && buildingCalculationInput.fixedAttributes.tenants > 1
            && buildingCalculationInput.fixedAttributes.roofFloor != BuildingRoofFloor.FLAT_ROOF

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.ROOF_NEW_COVERAGE,
            RenovationMeasureValue.METAL_TILES,
            VERSION_2
        )
}