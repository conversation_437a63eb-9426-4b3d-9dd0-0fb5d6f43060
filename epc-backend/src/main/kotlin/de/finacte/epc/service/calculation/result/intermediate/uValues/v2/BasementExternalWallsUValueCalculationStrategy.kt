package de.finacte.epc.service.calculation.result.intermediate.uValues.v2

import de.finacte.epc.entity.ConstructionMaterialType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.repository.ConstructionMaterialPropertiesRepository
import de.finacte.epc.service.calculation.CalculationConstants.CM_IN_METER
import de.finacte.epc.service.calculation.CalculationConstants.STANDARD_AVG_BASEMENT_HEIGHT
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.v2.GroundFloorUValueCalculationStrategy.Companion.SOIL_THERMAL_CONDUCTIVITY
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service

@Service
class BasementExternalWallsUValueCalculationStrategy(
    constructionMaterialPropertiesRepository: ConstructionMaterialPropertiesRepository
) : UValueCalculationStrategy(constructionMaterialPropertiesRepository) {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun getThermalEnvelopeElementTypeForStrategy(): ThermalEnvelopeElementType =
        ThermalEnvelopeElementType.BASEMENT_EXTERNAL_WALLS

    override fun apply(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): UValueCalculationResult.UValuesThermalEnvelopeElement =
        with(buildingCalculationInput) {
            val wallMaterialThermalConductivity = findConstructionMaterialThermalConductivity(
                fixedAttributes.basementExternalWallsType.name,
                ConstructionMaterialType.WALL
            )

            val wallThickness = fixedAttributes.facadeWallThickness.toDouble() / CM_IN_METER
            val wallThermalResistance = wallThickness / wallMaterialThermalConductivity

            val (insulationThermalResistance, insulationMaterialThermalConductivity) =
                calculateInsulationThermalResistance(basementInsulation)

            val groundThermalResistance =
                calculateGroundThermalResistance()

            val thermalEnvelopeElementThermalResistance =
                wallThermalResistance + insulationThermalResistance + groundThermalResistance

            log.debug(
                "[U-VALUE CALCULATION] Main material thermal conductivity: {}," +
                        " Insulation material thermal conductivity: {}," +
                        "Main material thermal resistance: {}," +
                        "Insulation material thermal resistance: {}" +
                        "Main material type: {}",
                wallMaterialThermalConductivity,
                insulationMaterialThermalConductivity,
                wallThermalResistance,
                insulationThermalResistance,
                getThermalEnvelopeElementTypeForStrategy()
            )

            UValueCalculationResult.UValuesThermalEnvelopeElement(
                uValue = 1 / thermalEnvelopeElementThermalResistance,
                thermalEnvelopeElementType = getThermalEnvelopeElementTypeForStrategy()
            )
        }

    private fun calculateGroundThermalResistance(): Double {
        return STANDARD_AVG_BASEMENT_HEIGHT / SOIL_THERMAL_CONDUCTIVITY
    }

    private fun calculateInsulationThermalResistance(basementExternalWallsInsulation: BuildingCalculationInput.BasementInsulation?): Pair<Double, Double?> {
        if (basementExternalWallsInsulation == null) {
            return 0.0 to null
        }
        val insulationMaterialThermalConductivity = findConstructionMaterialThermalConductivity(
            basementExternalWallsInsulation.basementExternalWallsInsulationType.name,
            ConstructionMaterialType.INSULATION
        )
        val insulationThickness =
            basementExternalWallsInsulation.basementExternalWallsInsulationThickness.toDouble() / CM_IN_METER
        val insulationThermalResistance = insulationThickness / insulationMaterialThermalConductivity
        return insulationThermalResistance to insulationMaterialThermalConductivity
    }
}