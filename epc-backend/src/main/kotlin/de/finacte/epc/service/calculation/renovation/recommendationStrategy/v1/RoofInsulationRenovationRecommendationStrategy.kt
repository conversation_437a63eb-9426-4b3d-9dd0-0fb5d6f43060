package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1

import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class RoofInsulationRenovationRecommendationStrategy : RenovationRecommendationStrategy() {

    companion object {
        val applicableRoofFloors = setOf(
            BuildingRoofFloor.FLAT_ROOF,
            BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
            BuildingRoofFloor.PARTIALLY_LIVEABLE
        )
    }

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        return if (buildingCalculationInput.roofInsulation == null) {
            buildingCalculationInput.fixedAttributes.roofFloor in applicableRoofFloors
        } else {
            buildingCalculationInput.roofInsulation.roofInsulationYear + 5 < Year.now().value &&
                    buildingCalculationInput.fixedAttributes.roofFloor in applicableRoofFloors
        }
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.ROOF_INSULATION,
            RenovationMeasureValue.VACUUM_INSULATED_PANEL_30_CM_2025,
            VERSION_1
        )
}