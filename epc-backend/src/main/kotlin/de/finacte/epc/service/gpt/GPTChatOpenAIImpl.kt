package de.finacte.epc.service.gpt

import de.finacte.epc.exception.gpt.EPCGptException
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.ai.chat.messages.Message
import org.springframework.ai.chat.model.ChatResponse
import org.springframework.ai.chat.prompt.Prompt
import org.springframework.ai.openai.OpenAiChatModel
import org.springframework.ai.openai.OpenAiChatOptions
import org.springframework.ai.openai.api.ResponseFormat
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Service
class GPTChatOpenAIImpl(
    @Qualifier("openAi4oMiniChatModel") private val openAi4oMiniChatModel: OpenAiChatModel,
    @Qualifier("openAi41NanoChatModel") private val openAi41NanoChatModel: OpenAiChatModel
) : GPTChat {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun call(messages: Set<Message>, clazz: Class<*>, chatModelType: ChatModelType): Any {
        val jsonSchema = convertClassToJsonV4Schema(clazz)
        log.debug("JSON schema requested in GPT as result conversion: {}", jsonSchema)
        val builder = OpenAiChatOptions.builder()
            .responseFormat(ResponseFormat(ResponseFormat.Type.JSON_SCHEMA, jsonSchema))
            .build()
        val prompt = Prompt(
            messages.toList(),
            builder
        )
        val content = callGPT(prompt, chatModelType)
        return convertResponseToObject(clazz, content)
    }

    override fun call(messages: Set<Message>, chatModelType: ChatModelType): String {
        val builder = OpenAiChatOptions.builder()
            .build()
        val prompt = Prompt(
            messages.toList(),
            builder
        )
        val content = callGPT(prompt, chatModelType)
        return content
    }

    private fun callGPT(prompt: Prompt, chatModelType: ChatModelType): String {
        val chatModel = getChatModel(chatModelType)
        val content = runCatching {
            val response: ChatResponse = chatModel.call(prompt)
            log.debug("GPT raw response: \n{}", response.result.output.text)
            response.result.output.text
        }.getOrElse {
            log.error("Error occurred while calling GPT chat service.", it)
            throw EPCGptException(
                "Error occurred while calling GPT chat service." +
                        " Check previous error log for more information. Exception message: ${it.message}"
            )
        }
        return content
    }

    private fun getChatModel(chatModelType: ChatModelType) =
        when (chatModelType) {
            ChatModelType.GPT_4O_MINI -> openAi4oMiniChatModel
            ChatModelType.GPT_41_NANO -> openAi41NanoChatModel
        }

    enum class ChatModelType {
        GPT_4O_MINI,
        GPT_41_NANO
    }
}
