package de.finacte.epc.service.report

import com.itextpdf.io.image.ImageDataFactory
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.Document
import com.itextpdf.layout.element.Image
import com.itextpdf.layout.element.Paragraph
import com.itextpdf.layout.element.Table
import com.itextpdf.layout.properties.HorizontalAlignment
import com.itextpdf.layout.properties.TextAlignment
import de.finacte.epc.dto.calculation.CalculationResultDto
import de.finacte.epc.entity.*
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service
import org.springframework.util.ResourceUtils
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.util.UUID


@Service
class CalculationResultReportPDFGenerator(
) : PDFGenerator {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun generate(calculationId: UUID, inputType: InputType): ByteArray {
        val calculationResultDto = CalculationResultDto(
            id = UUID.randomUUID(),
            category = CalculationResultCategory.CURRENT,
            energyEfficiency = 123.0,
            energyEfficiencyUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
            energyEfficiencyScaleGrade = EPCScaleGrade.D,
            finalEnergyDemandPerArea = 100.0,
            finalEnergyDemandPerAreaUnit = EnergyEfficiencyUnit.KWH_M2_YEAR,
            finalEnergyDemandPerAreaScaleGrade = EPCScaleGrade.C,
            co2Emissions = 123.0,
            co2EmissionsUnit = Co2EmissionUnit.KG,
            solarEnergyProduction = 0.0,
            solarEnergyProductionUnit = EnergyOverTimeUnit.KWH_YEAR,
            finalElectricityDemand = 4000.0,
            finalHotWaterDemand = 2000.0,
            finalHeatingEnergyDemand = 4500.0,
            finalCoolingEnergyDemand = 0.0
        )
        val outputStream = ByteArrayOutputStream()
        PdfWriter(outputStream).use { writer ->
            PdfDocument(writer).use { pdfDocument ->
                Document(pdfDocument).use { document ->
                    addTitleText(document, "EPC - Report")
                    addCalculationResult(document, calculationResultDto)
                    addLogo(document)
                }
            }
        }
        return outputStream.toByteArray()
    }

    private fun addLogo(document: Document) {
        val logoFile: File = ResourceUtils.getFile("classpath:reports/Finacte.png")
        val logoInputStream: InputStream = FileInputStream(logoFile)
        try {
            val image = Image(ImageDataFactory.create(logoInputStream.readAllBytes()))

            image.setWidth(100f)
            image.setFixedPosition(10f, 780f)
            document.add(image)
        } catch (e: Exception) {
            log.error("Failed to add image to PDF", e)
        }
    }

    private fun addTitleText(document: Document, titleText: String) {
        val paragraph = Paragraph(titleText).setBold().setFontSize(20f).setMarginTop(20f)
        paragraph.setTextAlignment(TextAlignment.CENTER)
        document.add(paragraph)
    }

    private fun addCalculationResult(document: Document, calculationResult: CalculationResultDto) {
        val table = Table(2)
        table.addCell("CO2 Emissions")
        table.addCell(calculationResult.co2Emissions.toString())
        table.addCell("CO2 Emissions Unit")
        table.addCell(calculationResult.co2EmissionsUnit.unitText)
        table.addCell("Energy Efficiency")
        table.addCell(calculationResult.energyEfficiency.toString())
        table.addCell("Energy Efficiency Unit")
        table.addCell(calculationResult.energyEfficiencyUnit.unitText)
        table.setTextAlignment(TextAlignment.CENTER)
        table.setHorizontalAlignment(HorizontalAlignment.CENTER)
        document.add(table)
    }
}
