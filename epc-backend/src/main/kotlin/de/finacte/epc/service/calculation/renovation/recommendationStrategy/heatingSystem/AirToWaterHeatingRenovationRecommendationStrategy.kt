package de.finacte.epc.service.calculation.renovation.recommendationStrategy.heatingSystem

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class AirToWaterHeatingRenovationRecommendationStrategy : RenovationRecommendationStrategy() {

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean = buildingCalculationInput.heating.heatingInstallationYear + 10 < Year.now().value

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, RenovationMeasureVariant> =
        Triple(
            RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            RenovationMeasureValue.AIR_TO_WATER_HEAT_PUMP_2025,
            RenovationMeasureVariant.DEFAULT
        )
}
