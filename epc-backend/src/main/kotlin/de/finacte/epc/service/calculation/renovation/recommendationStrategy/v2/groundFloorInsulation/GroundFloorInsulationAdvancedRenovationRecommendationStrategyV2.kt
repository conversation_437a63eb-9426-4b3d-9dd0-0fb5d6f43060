package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v2.groundFloorInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class GroundFloorInsulationAdvancedRenovationRecommendationStrategyV2 : RenovationRecommendationStrategy() {

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        if (buildingCalculationInput.groundFloorInsulation == null) {
            return true
        }
        return buildingCalculationInput.groundFloorInsulation.groundFloorInsulationYear + 15 < Year.now().value
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = !buildingCalculationInput.basementHeating.basementHeated

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.GROUND_FLOOR_INSULATION,
            RenovationMeasureValue.AEROGEL_1_CM_2025,
            VERSION_2
        )
}