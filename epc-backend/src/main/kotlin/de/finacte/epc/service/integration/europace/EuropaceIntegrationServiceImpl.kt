package de.finacte.epc.service.integration.europace

import de.finacte.epc.config.integration.europace.TenantEuropaceAuthenticationDataResolver
import de.finacte.epc.dto.integration.europace.EuropaceCaseDto
import de.finacte.epc.dto.integration.europace.EuropaceCaseType
import de.finacte.epc.exception.integration.EPCEuropaceBuildingCaseNotFound
import de.finacte.epc.exception.integration.EPCEuropaceDataNotExistForOrganisationException
import de.finacte.epc.mapper.integration.europace.EuropaceCaseMapper
import de.finacte.epc.mapper.integration.europace.ImportKundenangabenRequestMapper
import de.finacte.epc.repository.CalculationResultRepository
import de.finacte.epc.repository.integration.europace.EuropaceCaseRepository
import de.finacte.epc.service.building.BuildingService
import de.finacte.epc.service.calculation.profitability.CalculationProfitabilityService
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service
import java.util.*

@Service
class EuropaceIntegrationServiceImpl(
    private val europaceAPIClient: EuropaceApiClient,
    private val calculationProfitabilityService: CalculationProfitabilityService,
    private val buildingService: BuildingService,
    private val calculationResultRepository: CalculationResultRepository,
    private val importKundenangabenRequestMapper: ImportKundenangabenRequestMapper,
    private val tenantEuropaceAuthenticationDataResolver: TenantEuropaceAuthenticationDataResolver,
    private val europaceCaseRepository: EuropaceCaseRepository,
    private val europaceCaseMapper: EuropaceCaseMapper
) : EuropaceIntegrationService {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun createKundenangaben(buildingId: UUID, caseType: EuropaceCaseType): EuropaceCaseDto {
        val buildingDto = buildingService.get(buildingId)
        val calculationProfitability = calculationProfitabilityService.getProfitabilityCalculation(buildingId)
        val calculationResults = calculationResultRepository.findByBuildingId(buildingId)

        val importKundenangabenRequest =
            importKundenangabenRequestMapper.toRequest(
                buildingDto,
                calculationProfitability,
                calculationResults,
                caseType
            )
        log.debug(
            "[EUROPACE] Created Europace request: {}",
            importKundenangabenRequest
        )

        val europaceAuthenticationData =
            tenantEuropaceAuthenticationDataResolver.resolveCurrentTenantEuropaceAuthenticationData()
        if (europaceAuthenticationData.clientId == null || europaceAuthenticationData.clientSecret == null)
            throw EPCEuropaceDataNotExistForOrganisationException("Europace authentication data missing for this tenant")

        log.warn(
            "Sending data to europace. Building ID: {}, europace client ID: {} ",
            buildingDto.id,
            europaceAuthenticationData.clientId
        )
        val response = europaceAPIClient.createKundenangaben(
            importKundenangabenRequest,
            europaceAuthenticationData.clientId!!,
            europaceAuthenticationData.clientSecret!!
        )
        log.warn(
            "Europace case created. Building ID: {}, europace client ID: {}, case number: {} ",
            buildingDto.id,
            europaceAuthenticationData.clientId,
            response.vorgangsnummer
        )
        val europaceCaseEntity = europaceCaseMapper.apiResponseToEntity(response, buildingId, caseType)
        return europaceCaseRepository.save(europaceCaseEntity)
            .let { entity -> europaceCaseMapper.toDto(entity) }
    }

    override fun getKundenangabenCase(buildingId: UUID): EuropaceCaseDto {
        val caseEntity = europaceCaseRepository.findFirstByBuildingIdOrderByCreationDateDesc(buildingId)
            ?: throw EPCEuropaceBuildingCaseNotFound("Europace case data missing for building: $buildingId")

        return europaceCaseMapper.toDto(caseEntity)
    }

    override fun hasKundenangabenCaseExist(buildingId: UUID): Boolean {
        europaceCaseRepository.findFirstByBuildingIdOrderByCreationDateDesc(buildingId)
            ?: return false
        return true
    }

}