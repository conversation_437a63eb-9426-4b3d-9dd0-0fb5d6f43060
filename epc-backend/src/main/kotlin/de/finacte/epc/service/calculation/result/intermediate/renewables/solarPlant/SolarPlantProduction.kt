package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarThermal.HotWaterDemandWithoutSolarThermalProductionResult

interface SolarPlantProduction {
    fun calculate(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult,
        climateDataResult: ClimateDataResult
    ): SolarPlantProductionResult

    fun deductFromHotWaterDemand(
        hotWaterDemandResult: HotWaterDemandWithoutSolarThermalProductionResult,
        solarPlantProductionResult: SolarPlantProductionResult,
        buildingCalculationInputHeating: BuildingCalculationInput.Heating
    ): Pair<HotWaterDemandWithoutSolarPlantProductionResult, SolarPlantProductionResult>

    fun deductFromElectricityDemand(
        electricityDemandResult: Double,
        solarPlantProductionResult: SolarPlantProductionResult,
    ): Pair<Double, SolarPlantProductionResult>

    fun deductFromCoolingEnergyDemand(
        coolingEnergyDemand: Double,
        solarPlantProductionResult: SolarPlantProductionResult,
        buildingCalculationInput: BuildingCalculationInput
    ): Pair<Double, SolarPlantProductionResult>

    fun deductFromHeatingEnergyDemand(
        heatingEnergyDemand: Double,
        solarPlantProductionResult: SolarPlantProductionResult,
        buildingCalculationInputHeating: BuildingCalculationInput.Heating
    ): Pair<Double, SolarPlantProductionResult>
}