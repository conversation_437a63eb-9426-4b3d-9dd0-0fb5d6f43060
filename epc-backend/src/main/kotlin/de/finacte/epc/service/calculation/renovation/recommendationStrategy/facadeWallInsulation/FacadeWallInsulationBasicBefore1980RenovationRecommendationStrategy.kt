package de.finacte.epc.service.calculation.renovation.recommendationStrategy.facadeWallInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

@Service
class FacadeWallInsulationBasicBefore1980RenovationRecommendationStrategy : RenovationRecommendationStrategy(),
    de.finacte.epc.service.calculation.renovation.recommendationStrategy.facadeWallInsulation.FacadeWallInsulationRenovationRecommendationStrategy {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        return super.isRecommendedStrategy(buildingCalculationInput, renovationTemplate)
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = buildingCalculationInput.fixedAttributes.constructionYear < 1980 &&
            buildingCalculationInput.fixedAttributes.constructionYear >= 1945

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, RenovationMeasureVariant> =
        Triple(
            RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            RenovationMeasureValue.MINERAL_WOOL_10_CM_2025,
            RenovationMeasureVariant.BASIC
        )
}
