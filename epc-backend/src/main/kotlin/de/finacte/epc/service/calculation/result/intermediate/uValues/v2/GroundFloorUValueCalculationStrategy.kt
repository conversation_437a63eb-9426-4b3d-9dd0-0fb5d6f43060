package de.finacte.epc.service.calculation.result.intermediate.uValues.v2

import de.finacte.epc.entity.ConstructionMaterialType
import de.finacte.epc.entity.building.BuildingInsulationType
import de.finacte.epc.entity.building.BuildingWallType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.repository.ConstructionMaterialPropertiesRepository
import de.finacte.epc.service.calculation.CalculationConstants.CM_IN_METER
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service
import kotlin.math.PI
import kotlin.math.ln

@Service
class GroundFloorUValueCalculationStrategy(
    constructionMaterialPropertiesRepository: ConstructionMaterialPropertiesRepository
) : UValueCalculationStrategy(constructionMaterialPropertiesRepository) {
    companion object {
        private val log by LoggerDelegate()
        const val INTERNAL_SURFACE_RESISTANCE = 0.17
        const val BASE_SLAB_THICKNESS = 15.0
        const val UNHEATED_BASEMENT_AIR_GAP_CORRECTION = 0.6
        const val SOIL_THERMAL_CONDUCTIVITY = 1.75
        const val GROUND_DEPTH = 0.5
    }

    override fun getThermalEnvelopeElementTypeForStrategy(): ThermalEnvelopeElementType =
        ThermalEnvelopeElementType.GROUND_FLOOR

    override fun apply(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): UValueCalculationResult.UValuesThermalEnvelopeElement {
        with(buildingCalculationInput) {
            val (floorBaseSlabMaterialThermalConductivity, floorBaseSlabThermalResistance) =
                calculateThermalConductivityAndResistance(
                    BuildingWallType.CONCRETE.name,
                    ConstructionMaterialType.WALL,
                    BASE_SLAB_THICKNESS
                )

            val (floorScreedMaterialThermalConductivity, floorScreedThermalResistance) =
                calculateThermalConductivityAndResistance(
                    groundFloor.groundFloorType.name,
                    ConstructionMaterialType.SCREED,
                    groundFloor.groundFloorThickness.toDouble()
                )

            val (floorFinishMaterialThermalConductivity, floorFinishThermalResistance) =
                calculateThermalConductivityAndResistance(
                    groundFloor.groundFloorFinishType.name,
                    ConstructionMaterialType.FLOOR_FINISH,
                    groundFloor.groundFloorFinishThickness.toDouble()
                )

            val (insulationThermalResistance, insulationMaterialThermalConductivity) =
                calculateInsulationThermalResistance(
                    groundFloorInsulation?.groundFloorInsulationType,
                    groundFloorInsulation?.groundFloorInsulationThickness
                )

            val groundThermalResistance =
                calculateGroundThermalResistance(buildingShapeResult)

            val thermalEnvelopeElementThermalResistance =
                floorBaseSlabThermalResistance + floorScreedThermalResistance + floorFinishThermalResistance +
                        insulationThermalResistance + groundThermalResistance + INTERNAL_SURFACE_RESISTANCE

            log.debug(
                "[U-VALUE CALCULATION] Floor base slab material thermal conductivity: {}," +
                        " Floor screed material thermal conductivity: {}," +
                        " Floor finish material thermal conductivity: {}," +
                        " Insulation material thermal conductivity: {}," +
                        " Floor base slab material thermal resistance: {}," +
                        " Floor screed material thermal resistance: {}," +
                        " Floor finish material thermal resistance: {}" +
                        " Insulation material thermal resistance: {}" +
                        " Main material type: {}",
                floorBaseSlabMaterialThermalConductivity,
                floorScreedMaterialThermalConductivity,
                floorFinishMaterialThermalConductivity,
                insulationMaterialThermalConductivity,
                floorBaseSlabThermalResistance,
                floorScreedThermalResistance,
                floorFinishThermalResistance,
                insulationThermalResistance,
                getThermalEnvelopeElementTypeForStrategy()
            )
            var uValue = 1 / thermalEnvelopeElementThermalResistance

            if (fixedAttributes.basementExist && !basementHeating.basementHeated) {
                uValue *= UNHEATED_BASEMENT_AIR_GAP_CORRECTION
            }

            return UValueCalculationResult.UValuesThermalEnvelopeElement(
                uValue = uValue,
                thermalEnvelopeElementType = getThermalEnvelopeElementTypeForStrategy()
            )
        }
    }

    protected fun calculateThermalConductivityAndResistance(
        materialName: String,
        materialType: ConstructionMaterialType,
        materialThickness: Double
    ): Pair<Double, Double> {
        val materialThermalConductivity = findConstructionMaterialThermalConductivity(
            materialName,
            materialType
        )

        val materialThicknessMeters = materialThickness / CM_IN_METER
        val materialThermalResistance = materialThicknessMeters / materialThermalConductivity
        return Pair(materialThermalConductivity, materialThermalResistance)
    }

    protected fun calculateInsulationThermalResistance(
        floorInsulationType: BuildingInsulationType?,
        floorInsulationThickness: Int?
    ): Pair<Double, Double?> {
        if (floorInsulationType == null || floorInsulationThickness == null) {
            return 0.0 to null
        }
        val insulationMaterialThermalConductivity = findConstructionMaterialThermalConductivity(
            floorInsulationType.name,
            ConstructionMaterialType.INSULATION
        )
        val insulationThickness =
            floorInsulationThickness.toDouble() / CM_IN_METER
        val insulationThermalResistance = insulationThickness / insulationMaterialThermalConductivity
        return insulationThermalResistance to insulationMaterialThermalConductivity
    }

    protected fun calculateGroundThermalResistance(buildingShapeResult: BuildingShapeResult): Double =
        (1 / (2 * PI * SOIL_THERMAL_CONDUCTIVITY)) * ln(1 + (2 * buildingShapeResult.buildingBaseArea) / (PI * buildingShapeResult.buildingPerimeter * GROUND_DEPTH))
}