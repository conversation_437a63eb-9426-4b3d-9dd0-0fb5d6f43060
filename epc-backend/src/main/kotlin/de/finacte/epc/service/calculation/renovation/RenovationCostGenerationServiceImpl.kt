package de.finacte.epc.service.calculation.renovation

import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.entity.renovation.RenovationCostEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.exception.calculation.EPCProfitabilityCalculationException
import de.finacte.epc.repository.renovation.RenovationCostRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.gpt.GPTChat
import de.finacte.epc.service.gpt.GPTChatOpenAIImpl
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.ai.chat.messages.SystemMessage
import org.springframework.ai.chat.messages.UserMessage
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant

@Service
class RenovationCostGenerationServiceImpl(
    private val gptChat: GPTChat,
    private val renovationCostRepository: RenovationCostRepository
) : RenovationCostGenerationService {
    companion object {
        private val log by LoggerDelegate()
    }

    override suspend fun getRenovationCost(
        renovations: Set<RenovationEntity>,
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): MutableIterable<RenovationCostEntity> {
        val startTime = Instant.now()
        log.info(
            "[RENOVATION COSTS GENERATION] Generate renovation costs for building: {}",
            buildingCalculationInput.fixedAttributes.buildingId
        )
        val userMessage = generateUserMessage(renovations, buildingCalculationInput, buildingShapeResult)
        val systemMessage = generateSystemMessage()

        val gptResponse =
            gptChat.call(
                setOf(userMessage, systemMessage),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_4_1
            )
        gptResponse as RenovationCostGenerationService.RenovationCostGenerationResult
        val totalDuration = Duration.between(startTime, Instant.now())

        log.debug(
            "[RENOVATION COSTS GENERATION]  Generated renovation costs for building: {} completed in {} ms," +
                    "UserMessage: {}, \n" +
                    "SystemMessage: {}\n," +
                    "Generated values: {}",
            buildingCalculationInput.fixedAttributes.buildingId,
            totalDuration.toMillis(),
            userMessage.text,
            systemMessage.text,
            gptResponse
        )
        return saveRenovationCosts(gptResponse, renovations)
    }

    fun generateUserMessage(
        renovations: Set<RenovationEntity>,
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): UserMessage {
        val facadeArea =
            buildingShapeResult.thermalEnvelopeElements
                .find { it.type == ThermalEnvelopeElementType.FACADE_WALLS }?.area
                ?: throw EPCProfitabilityCalculationException("Facade walls thermal envelope element type must exist in thermal envelope element collection")
        val windowsArea =
            buildingShapeResult.thermalEnvelopeElements.find { it.type == ThermalEnvelopeElementType.WINDOWS }?.area
                ?: throw EPCProfitabilityCalculationException("Windows thermal envelope element type must exist in thermal envelope element collection")


        return UserMessage(buildString {
            appendLine("Building description:")
            appendLine("- Country: Germany")
            appendLine("- Zip code: ${buildingCalculationInput.fixedAttributes.zipCode}")
            appendLine("- Building living area: ${buildingShapeResult.buildingArea} sqm")
            appendLine("- Facade surface: $facadeArea sqm")
            appendLine("- Windows surface: $windowsArea sqm")
            appendLine("- Roof surface: ${buildingShapeResult.buildingRoofSurfaceArea} sqm")
            appendLine()
            appendLine("Planned renovations:")
            renovations.forEach { renovation ->
                appendLine("- renovation_measure_category: ${renovation.renovationTemplate.renovationMeasureCategory}")
                appendLine("  renovation_measure_type: ${renovation.renovationTemplate.renovationMeasureType}")
                appendLine("  renovation_measure_value: ${renovation.renovationTemplate.renovationMeasureValue}")
                appendLine("  variant: ${renovation.renovationTemplate.renovationMeasureVariant}")
                appendLine("  description: ${renovation.renovationTemplate.renovationMeasureDescription}")
            }
        })
    }

    fun generateSystemMessage(): SystemMessage {
        return SystemMessage(buildString {
            appendLine("The user will provide a description of a building and a list of planned renovation measures. Your task is to estimate the renovation cost for each listed measure.")
            appendLine()
            appendLine("Instructions:")
            appendLine("- Estimate only the renovations provided by the user.")
            appendLine("- For all cost calculations, use ONLY the user’s provided building areas for living, facade, windows, and roof surfaces, to estimate the cost. Never attempt to calculate these areas.")
            appendLine("- For all cost calculations, use the location (based on German zip code) provided by user to find local cost data.")
            appendLine("- Consider costs of installation and materials.")
            appendLine("- Costs must be in EUR, and they must be gross costs (VAT according to Germany).")
            appendLine("- If you find material cost data that is more than a year old, take into account any cost increases or decreases that occurred during that time.")
            appendLine("- For each renovation measure:")
            appendLine("  - Repeat the cost estimation 5 times using different data sources:")
            appendLine("    1. German Construction Cost Index")
            appendLine("    2. Quotations from local construction companies (use zip code)")
            appendLine("    3. Market analysis from German Renewable Energy Associations")
            appendLine("    4. Standard pricing from German construction material suppliers")
            appendLine("    5. Any relevant German cost estimation dataset")
            appendLine()
            appendLine("- When comparing similar renovation measures:")
            appendLine("  - If two measures have the same category and type but different variants (e.g. BASIC and ADVANCED), enforce the following rule:")
            appendLine("    - The ADVANCED variant must **always** result in a **higher** most probable cost and upper range value than the BASIC variant.")
            appendLine("    - Adjust estimates as needed across the 5 sources to comply with this rule.")
            append("  - Always differentiate cost estimates based on technical complexity and included features.")
            append("Even if two renovation measures belong to the similar or same category (e.g., underfloor heating), you must treat them as separate if they differ in method, materials, or scope. ")
            appendLine("Consider the following when adjusting costs for similar measures:")
            appendLine("    - Installation method (e.g., wet heating system vs. dry systems).")
            appendLine("    - Included components (e.g., includes flooring or not, basic or advanced controls).")
            appendLine("    - Additional features (e.g., security features, advanced controls, etc.).")
            appendLine("    - Level of reconstruction required (e.g., minimal vs. structural changes).")
            appendLine("    - Material quality (e.g., standard vs. premium fittings).")
            appendLine("  - When calculating solar panel system size and battery size for this system, calculate the size using modules with 0.45 kWp per 2 sqm and covering 50% of the roof surface.")
            appendLine("  - For a given project, the total installed price of lithium-ion battery storage must never exceed the total installed price of the rooftop solar panel system sized based on the specification (0.45 kWp per 2 sqm, 50% roof coverage).")
            appendLine()
            appendLine("- For each renovation measure, provide:")
            appendLine("  - A cost range (minimum and maximum)")
            appendLine("  - A most probable cost value (e.g., median or weighted average of the estimates)")
        })
    }

    private fun saveRenovationCosts(
        gptResponse: RenovationCostGenerationService.RenovationCostGenerationResult,
        renovations: Set<RenovationEntity>
    ): MutableIterable<RenovationCostEntity> {
        val renovationCosts = mutableListOf<RenovationCostEntity>()
        renovations.forEach { renovation ->
            val renovationCost =
                gptResponse.renovationCostGenerationResultItem.find {
                    renovation.renovationTemplate.renovationMeasureCategory == it.renovationMeasureCategory &&
                            renovation.renovationTemplate.renovationMeasureType == it.renovationMeasureType &&
                            renovation.renovationTemplate.renovationMeasureValue == it.renovationMeasureValue
                }
                    ?: run {
                        log.warn("[GPT FAILURE] Can't find renovation cost returned by GPT for category: ${renovation.renovationTemplate.renovationMeasureCategory} and variant ${renovation.renovationTemplate.renovationMeasureValue}. GPT response: ${gptResponse}. Using default cost !")
                        return@forEach // can't generate costs - will be shown as Not available
                    }

            renovationCosts.add(
                RenovationCostEntity(
                    minCost = renovationCost.minCost,
                    maxCost = renovationCost.maxCost,
                    mostProbableValue = renovationCost.mostProbableValue,
                    id = null,
                    renovation = renovation
                )
            )
        }
        return renovationCostRepository.saveAll(renovationCosts)
    }
}