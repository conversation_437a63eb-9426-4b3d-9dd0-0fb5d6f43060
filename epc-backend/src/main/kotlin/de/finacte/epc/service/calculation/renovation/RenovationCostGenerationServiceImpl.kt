package de.finacte.epc.service.calculation.renovation

import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.entity.renovation.RenovationCostEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.exception.calculation.EPCProfitabilityCalculationException
import de.finacte.epc.repository.renovation.RenovationCostRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.gpt.GPTChat
import de.finacte.epc.service.gpt.GPTChatOpenAIImpl
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.ai.chat.messages.SystemMessage
import org.springframework.ai.chat.messages.UserMessage
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant

@Service
class RenovationCostGenerationServiceImpl(
    private val gptChat: GPTChat,
    private val renovationCostRepository: RenovationCostRepository
) : RenovationCostGenerationService {
    companion object {
        private val log by LoggerDelegate()
    }

    override suspend fun getRenovationCost(
        renovations: Set<RenovationEntity>,
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): MutableIterable<RenovationCostEntity> {
        val startTime = Instant.now()
        log.info(
            "[RENOVATION COSTS GENERATION] Generate renovation costs for building: {}",
            buildingCalculationInput.fixedAttributes.buildingId
        )
        val userMessage = generateUserMessage(renovations, buildingCalculationInput, buildingShapeResult)
        val systemMessage = generateSystemMessage()

        val gptResponse =
            gptChat.call(
                setOf(userMessage, systemMessage),
                RenovationCostGenerationService.RenovationCostGenerationResult::class.java,
                GPTChatOpenAIImpl.ChatModelType.GPT_41_NANO
            )
        gptResponse as RenovationCostGenerationService.RenovationCostGenerationResult
        val totalDuration = Duration.between(startTime, Instant.now())

        log.debug(
            "[RENOVATION COSTS GENERATION]  Generated renovation costs for building: {} completed in {} ms," +
                    "UserMessage: {}, \n" +
                    "SystemMessage: {}\n," +
                    "Generated values: {}",
            buildingCalculationInput.fixedAttributes.buildingId,
            totalDuration.toMillis(),
            userMessage.text,
            systemMessage.text,
            gptResponse
        )
        return saveRenovationCosts(gptResponse, renovations)
    }

    fun generateUserMessage(
        renovations: Set<RenovationEntity>,
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): UserMessage {
        val facadeArea =
            buildingShapeResult.thermalEnvelopeElements
                .find { it.type == ThermalEnvelopeElementType.FACADE_WALLS }?.area
                ?: throw EPCProfitabilityCalculationException("Facade walls thermal envelope element type must exist in thermal envelope element collection")
        val windowsArea =
            buildingShapeResult.thermalEnvelopeElements.find { it.type == ThermalEnvelopeElementType.WINDOWS }?.area
                ?: throw EPCProfitabilityCalculationException("Windows thermal envelope element type must exist in thermal envelope element collection")


        return UserMessage(buildString {
            appendLine("Building description:")
            appendLine("- Country: Germany")
            appendLine("- Zip code: ${buildingCalculationInput.fixedAttributes.zipCode}")
            appendLine("- Building area: ${buildingShapeResult.buildingArea} sqm")
            appendLine("- Facade surface: $facadeArea sqm")
            appendLine("- Windows surface: $windowsArea sqm")
            appendLine()
            appendLine("Planned renovations:")
            renovations.filter { it.renovationTemplate.renovationMeasureVersion == 2 ||
                    (it.recommended && it.renovationTemplate.renovationMeasureVersion == 1) } //TODO temporary until end of renovation V2
                .forEach { renovation ->
                appendLine("- renovation_measure_category: ${renovation.renovationTemplate.renovationMeasureCategory}")
                appendLine("  renovation_measure_id: ${renovation.renovationTemplate.renovationMeasureValue}")
                appendLine("  description: ${renovation.renovationTemplate.renovationMeasureDescription}")
            }
        })
    }

    fun generateSystemMessage(): SystemMessage {
        return SystemMessage(buildString {
            appendLine("The user will provide a description of a building and a list of planned renovation measures. Your task is to estimate the renovation cost for each listed measure.")
            appendLine()
            appendLine("Instructions:")
            appendLine("- Estimate only the renovations provided by the user.")
            appendLine("- Consider the building's shape, surface areas, and location (based on German zip code).")
            appendLine("- For each renovation measure:")
            appendLine("  - Repeat the cost estimation 5 times using different data sources:")
            appendLine("    1. German Construction Cost Index")
            appendLine("    2. Quotations from local construction companies (use zip code)")
            appendLine("    3. Market analysis from German Renewable Energy Associations")
            appendLine("    4. Standard pricing from German construction material suppliers")
            appendLine("    5. Any relevant German cost estimation dataset")
            appendLine("- Aggregate the results to produce:")
            appendLine("  - A cost range (minimum and maximum)")
            appendLine("  - A most probable cost value")
        })
    }

    private fun saveRenovationCosts(
        gptResponse: RenovationCostGenerationService.RenovationCostGenerationResult,
        renovations: Set<RenovationEntity>
    ): MutableIterable<RenovationCostEntity> {
        val uniqueResults = gptResponse.renovationCostGenerationResultItem
            .distinctBy { it.renovationMeasureCategory to it.renovationMeasureId }
        val renovationCosts = mutableListOf<RenovationCostEntity>()
        renovations.forEach { renovation ->
            if(!renovation.recommended) return@forEach // skip not recommended

            val renovationCost =
                uniqueResults.find { renovation.renovationTemplate.renovationMeasureCategory == it.renovationMeasureCategory &&
                        renovation.renovationTemplate.renovationMeasureValue == it.renovationMeasureId }
                    ?: run {
                        log.warn("[GPT FAILURE] Can't find renovation cost returned by GPT for category: ${renovation.renovationTemplate.renovationMeasureCategory} and variant ${renovation.renovationTemplate.renovationMeasureValue}. GPT response: ${gptResponse}. Using default cost !")
                        return@forEach // can't generate costs - will be shown as Not available
                    }

            renovationCosts.add(
                RenovationCostEntity(
                    minCost = renovationCost.minCost,
                    maxCost = renovationCost.maxCost,
                    mostProbableValue = renovationCost.mostProbableValue,
                    id = null,
                    renovation = renovation
                )
            )
        }
        return renovationCostRepository.saveAll(renovationCosts)
    }
}