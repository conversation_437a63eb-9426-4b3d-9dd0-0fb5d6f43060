package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v2.groundFloorInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class GroundFloorInsulationBasicRenovationRecommendationStrategyV2 : RenovationRecommendationStrategy() {

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean = false //we recommend Advanced only

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = !buildingCalculationInput.basementHeating.basementHeated

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.GROUND_FLOOR_INSULATION,
            RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            VERSION_2
        )
}