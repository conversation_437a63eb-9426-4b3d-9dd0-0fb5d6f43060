package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

@Service
class VentHeatExchangeRenovationRecommendationStrategy : RenovationRecommendationStrategy() {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean = !buildingCalculationInput.ventHeatExchange.ventHeatExchangeInstalled

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE,
            RenovationMeasureValue.HEAT_EXCHANGER,
            VERSION_1
        )
}