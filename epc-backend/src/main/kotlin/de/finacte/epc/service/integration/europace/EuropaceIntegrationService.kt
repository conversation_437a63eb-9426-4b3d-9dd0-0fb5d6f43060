package de.finacte.epc.service.integration.europace

import de.finacte.epc.dto.integration.europace.EuropaceCaseDto
import de.finacte.epc.dto.integration.europace.EuropaceCaseType
import java.util.UUID

interface EuropaceIntegrationService {
    fun createKundenangaben(buildingId: UUID, caseType: EuropaceCaseType): EuropaceCaseDto
    fun getKundenangabenCase(buildingId: UUID): EuropaceCaseDto
    fun hasKundenangabenCaseExist(buildingId: UUID): <PERSON><PERSON>an
}