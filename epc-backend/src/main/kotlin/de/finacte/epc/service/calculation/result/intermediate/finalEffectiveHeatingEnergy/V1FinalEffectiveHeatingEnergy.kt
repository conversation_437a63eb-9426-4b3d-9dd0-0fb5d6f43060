package de.finacte.epc.service.calculation.result.intermediate.finalEffectiveHeatingEnergy

import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.CalculationConstants.HOURS_PER_DAY
import de.finacte.epc.service.calculation.CalculationConstants.WATT_TO_KILOWATT_RATIO
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import org.springframework.stereotype.Service

@Service
class V1FinalEffectiveHeatingEnergy(
    private val systemEfficiencyRepository: SystemEfficiencyRepository
) : FinalEffectiveHeatingEnergy {

    override fun calculate(
        climateDataResult: ClimateDataResult,
        buildingCalculationInputHeating: BuildingCalculationInput.Heating,
        heatTransferCoefficientResult: Double,
        ventilationLossCoefficientResult: Double
    ): Double {
        val heatingDegreeHours = climateDataResult.heatingDegreeDays * HOURS_PER_DAY

        val rawHeatingDemand =
            ((heatTransferCoefficientResult + ventilationLossCoefficientResult) *
                    heatingDegreeHours / WATT_TO_KILOWATT_RATIO)

        val heatingSourceEfficiency =
            findHeatingSourceEfficiency(buildingCalculationInputHeating)
        return rawHeatingDemand / heatingSourceEfficiency
    }

    private fun findHeatingSourceEfficiency(heating: BuildingCalculationInput.Heating): Double {
        return systemEfficiencyRepository.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
            heating.heatingEnergySource, heating.heatingInstallationYear
        )?.efficiency ?: throw EPCCalculationException("Can't find energy efficiency for $heating")
    }

}