package de.finacte.epc.service.calculation.renovation.recommendationStrategy.roofCoverage

import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

@Service
class RoofCoveragePitchedRoofBasicBefore1950RenovationRecommendationStrategy : RenovationRecommendationStrategy(),
    de.finacte.epc.service.calculation.renovation.recommendationStrategy.roofCoverage.RoofCoverageRenovationRecommendationStrategy {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        return super.isRecommendedStrategy(buildingCalculationInput, renovationTemplate)
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = buildingCalculationInput.fixedAttributes.constructionYear < 1950
            && buildingCalculationInput.fixedAttributes.roofFloor != BuildingRoofFloor.FLAT_ROOF

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, RenovationMeasureVariant> =
        Triple(
            RenovationMeasureType.ROOF_NEW_COVERAGE,
            RenovationMeasureValue.CLAY_TILES,
            RenovationMeasureVariant.BASIC
        )
}
