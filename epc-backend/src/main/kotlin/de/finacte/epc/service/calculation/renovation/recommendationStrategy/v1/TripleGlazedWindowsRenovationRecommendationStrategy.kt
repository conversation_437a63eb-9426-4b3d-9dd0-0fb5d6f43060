package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1

import de.finacte.epc.entity.building.BuildingWindowsGlazing
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class TripleGlazedWindowsRenovationRecommendationStrategy : RenovationRecommendationStrategy() {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean =
        buildingCalculationInput.windowsGlazing.windowsInstallationYear + 5 < Year.now().value &&
                buildingCalculationInput.windowsGlazing.windowsGlazing != BuildingWindowsGlazing.TRIPLE

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            RenovationMeasureValue.PVC_TRIPLE_GLAZING_LOW_E_COATING_2025,
            VERSION_1
        )
}