package de.finacte.epc.service.calculation.buildingShape.v1

import de.finacte.epc.service.calculation.CalculationConstants.STANDARD_AVG_BASEMENT_HEIGHT


fun calculateResidentialFloorsVolume(
    singleFloorArea: Double,
    buildingFloorsWithoutRoofFloor: Int,
    buildingFloorHeight: Double,
): Double {
    val singleFloorVolume = calculateSingleFloorVolume(singleFloorArea, buildingFloorHeight)
    return buildingFloorsWithoutRoofFloor  * singleFloorVolume
}

fun calculateBasementVolume(
    singleFloorArea: Double,
    basementExist: Boolean
): Double {
    return when (basementExist) {
        true -> calculateSingleFloorVolume(singleFloorArea, STANDARD_AVG_BASEMENT_HEIGHT)
        false -> 0.0
    }
}

fun calculateBasementHeatedVolume(
    basementVolume: Double,
    basementHeated: Boolean
): Double {
    return when (basementHeated) {
        true -> basementVolume
        false -> 0.0
    }
}

private fun calculateSingleFloorVolume(singleFloorArea: Double, buildingFloorHeight: Double): Double {
    return singleFloorArea * buildingFloorHeight
}