package de.finacte.epc.service.admin

import de.finacte.epc.dto.building.generated.BuildingMissingAttributesGenerated
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import java.util.UUID

interface AdministrationCalculationResultsService {
    fun getBuildingMissingAttributesGenerated(buildingId: UUID): BuildingMissingAttributesGenerated?
    fun getClimateData(zipCode: String): ClimateDataResult?
}