package de.finacte.epc.service.calculation.renovation

import de.finacte.epc.dto.calculation.CalculationProfitabilityDto
import de.finacte.epc.dto.calculation.RenovationCostGenerationDto
import de.finacte.epc.dto.calculation.RenovationDto
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.renovation.RenovationCostEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.exception.calculation.EPCProfitabilityCalculationException
import de.finacte.epc.mapper.renovation.RenovationCostGenerationMapper
import de.finacte.epc.mapper.renovation.RenovationMapper
import de.finacte.epc.mapper.renovation.RenovationTemplateMapper
import de.finacte.epc.repository.CalculationResultRepository
import de.finacte.epc.repository.renovation.RenovationRepository
import de.finacte.epc.repository.renovation.RenovationTemplateRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInputService
import de.finacte.epc.service.calculation.buildingShape.BuildingShape
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.profitability.CalculationProfitabilityService
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import de.finacte.epc.service.calculation.renovation.renovationStrategy.RenovationStrategy
import de.finacte.epc.utils.LoggerDelegate
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.util.*

@Service
class RenovationServiceImpl(
    private val renovationTemplateRepository: RenovationTemplateRepository,
    private val renovationTemplateMapper: RenovationTemplateMapper,
    private val renovationMapper: RenovationMapper,
    private val renovationRepository: RenovationRepository,
    private val renovationRecommendationStrategies: Set<RenovationRecommendationStrategy>,
    private val renovationStrategies: Set<RenovationStrategy>,
    private val renovationCostGenerationService: RenovationCostGenerationService,
    private val buildingShape: BuildingShape,
    private val buildingCalculationInputService: BuildingCalculationInputService,
    private val calculationProfitabilityService: CalculationProfitabilityService,
    private val calculationResultRepository: CalculationResultRepository,
    private var renovationCostGenerationMapper: RenovationCostGenerationMapper
) : RenovationService {

    companion object {
        private val log by LoggerDelegate()

        // Define 6 groups with shuffled renovation measure types (it's giving better results)
        private val GROUP_1: Set<RenovationMeasureType> = setOf(
            RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION,
            RenovationMeasureType.ROOF_NEW_COVERAGE,
            RenovationMeasureType.BASEMENT_MOISTURE_PROTECTION,
            RenovationMeasureType.HEATING_PRIMARY_SOURCE,
            RenovationMeasureType.BARRIER_FREE_BATHROOM,
            RenovationMeasureType.HIGH_PERFORMANCE
        )

        private val GROUP_2: Set<RenovationMeasureType> = setOf(
            RenovationMeasureType.WINDOWS_GLAZING_TYPE,
            RenovationMeasureType.ROOF_INSULATION,
            RenovationMeasureType.GROUND_FLOOR_INSULATION,
            RenovationMeasureType.CEILING_HEATING,
            RenovationMeasureType.WIDENING_DOORS
        )

        private val GROUP_3: Set<RenovationMeasureType> = setOf(
            RenovationMeasureType.FACADE_EXTERIOR_CLADDING,
            RenovationMeasureType.CEILING_INSULATION,
            RenovationMeasureType.DOORS_REPLACEMENT,
            RenovationMeasureType.WALL_HEATING,
            RenovationMeasureType.INTELLIGENT_HEATING_CONTROLS,
            RenovationMeasureType.EMERGENCY_SYSTEMS
        )

        private val GROUP_4: Set<RenovationMeasureType> = setOf(
            RenovationMeasureType.WINDOWS_SHUTTERS,
            RenovationMeasureType.ROOF_RE_ENFORCEMENT,
            RenovationMeasureType.GROUND_FLOOR_MOISTURE_PROTECTION,
            RenovationMeasureType.BASIC_EFFICIENCY,
            RenovationMeasureType.IMMERSION_HEATER,
            RenovationMeasureType.GRID_CONNECTION_UPGRADE
        )

        private val GROUP_5: Set<RenovationMeasureType> = setOf(
            RenovationMeasureType.WINDOWS_SECURITY_FEATURES,
            RenovationMeasureType.BASEMENT_INSULATION,
            RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE,
            RenovationMeasureType.STAIRLIFT
        )

        private val GROUP_6: Set<RenovationMeasureType> = setOf(
            RenovationMeasureType.DOORS_SECURITY_FEATURES,
            RenovationMeasureType.UNDERFLOOR_HEATING,
            RenovationMeasureType.LIGHTING_AUTOMATION,
            RenovationMeasureType.WALLBOX_INSTALLATION,
            RenovationMeasureType.SOLAR_PANELS,
            RenovationMeasureType.BATTERY_STORAGE
        )

        val TYPE_GROUPS: Map<String, Set<RenovationMeasureType>> = mapOf(
            "GROUP_1" to GROUP_1,
            "GROUP_2" to GROUP_2,
            "GROUP_3" to GROUP_3,
            "GROUP_4" to GROUP_4,
            "GROUP_5" to GROUP_5,
            "GROUP_6" to GROUP_6
        )
    }

    override fun getRenovationTemplates(): Set<RenovationTemplateDto> {
        return renovationTemplateRepository.findAll().map { renovationTemplateMapper.toDto(it) }.toSet()
    }

    override fun generateRenovations(buildingId: UUID): List<RenovationDto> {
        val buildingCalculationInput =
            buildingCalculationInputService.getBuildingCalculationInput(buildingId)

        val renovationTemplates = renovationTemplateRepository.findAll().toSet()

        val renovations = renovationTemplates.map { renovationTemplate ->
            var applicable = false
            var recommended = false

            run strategies@{
                renovationRecommendationStrategies.forEach { renovationGenerateStrategy ->
                    val result = renovationGenerateStrategy.test(buildingCalculationInput, renovationTemplate)
                    applicable = result.first
                    recommended = result.second

                    if (applicable) return@strategies
                }
            }

            if (applicable)
                RenovationEntity(
                    buildingId = buildingCalculationInput.fixedAttributes.buildingId,
                    renovationTemplate = renovationTemplate,
                    recommended = recommended,
                    selected = false,
                    id = null,
                    renovationCost = null
                ) else null
        }.filterNotNull()
        log.debug(
            "[RENOVATION GENERATED] Renovations for building ID: {} , generated:\n{}",
            buildingCalculationInput.fixedAttributes.buildingId, renovations
        )

        renovationRepository.saveAll(renovations)

        return getRenovations(buildingId)
    }

    override fun selectRenovations(
        buildingId: UUID,
        selectedRenovations: Set<UUID>,
        buildingCalculationInputBeforeRenovation: BuildingCalculationInput
    ): Pair<List<RenovationDto>, BuildingCalculationInput> {
        val renovationEntities = renovationRepository.findAllByBuildingId(buildingId)
        val renovationEntitiesUpdated = renovationEntities.mapNotNull {
            if (it.selected != selectedRenovations.contains(it.id)) {
                it.selected = selectedRenovations.contains(it.id)
                it
            } else null
        }
        val renovationEntitiesUpdatedPersisted = renovationRepository.saveAll(renovationEntitiesUpdated)
        val allSelectedRenovationTemplates =
            renovationEntities.filter { it.selected }
                .map { renovationTemplateMapper.toDto(it.renovationTemplate) }.toSet()

        val buildingCalculationInputAfterRenovation =
            generateBuildingCalculationInput(
                buildingCalculationInputBeforeRenovation,
                allSelectedRenovationTemplates
            )

        return renovationEntitiesUpdatedPersisted.map {
            renovationMapper.toDto(it)
        } to buildingCalculationInputAfterRenovation
    }

    override fun getRenovations(buildingId: UUID): List<RenovationDto> =
        renovationRepository.findAllByBuildingIdEqualsOrderByCreationDate(buildingId).map {
            renovationMapper.toDto(it)
        }

    override fun generateBuildingCalculationInput(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplates: Set<RenovationTemplateDto>
    ): BuildingCalculationInput {
        var buildingCalculationInputAfterRenovation = buildingCalculationInput
        renovationTemplates.forEach { renovationTemplate ->
            val renovationTemplateKey = renovationTemplate.renovationMeasureType

            val strategy =
                renovationStrategies
                    .find { it.getRenovatedElementKeyForStrategy() == renovationTemplateKey }
                    ?: throw EPCProfitabilityCalculationException("Can't find strategy for ${renovationTemplate.renovationMeasureCategory} during renovation apply")

            buildingCalculationInputAfterRenovation =
                strategy.apply(buildingCalculationInputAfterRenovation, renovationTemplate)
        }

        log.debug(
            "[BUILDING CALCULATION INPUT - RENOVATED] Calculation input generated with renovations, building ID: {} ," +
                    " values:\n {}",
            buildingCalculationInput.fixedAttributes.buildingId, buildingCalculationInputAfterRenovation
        )
        return buildingCalculationInputAfterRenovation
    }

    override suspend fun generateRenovationCosts(
        buildingCalculationInput: BuildingCalculationInput,
        renovations: Set<RenovationEntity>,
        buildingShapeResult: BuildingShapeResult
    ): Set<RenovationCostEntity> {
        val result = renovationCostGenerationService.getRenovationCost(
            renovations,
            buildingCalculationInput,
            buildingShapeResult
        ).toSet()
        return result
    }

    override suspend fun generateRenovationCostsParallel(
        buildingCalculationInput: BuildingCalculationInput,
        renovations: Set<RenovationEntity>,
        buildingShapeResult: BuildingShapeResult
    ): Set<RenovationCostEntity> = coroutineScope {
        val startTime = Instant.now()
        log.info(
            "[RENOVATION COSTS GENERATION] Starting parallel renovation costs generation for building ID: {}",
            buildingCalculationInput.fixedAttributes.buildingId
        )

        val typeJobs = TYPE_GROUPS.map { (groupName, types) ->
            async {
                val groupStartTime = Instant.now()
                val filteredRenovations = renovations.filter { renovation ->
                    renovation.renovationTemplate.renovationMeasureType in types
                }.toSet()

                val result = renovationCostGenerationService.getRenovationCost(
                    filteredRenovations,
                    buildingCalculationInput,
                    buildingShapeResult
                ).toSet()

                val groupDuration = Duration.between(groupStartTime, Instant.now())
                log.debug(
                    "[RENOVATION COSTS GENERATION] Group {} processing completed in {} ms with {} results",
                    groupName, groupDuration.toMillis(), result.size
                )
                result
            }
        }

        val results = typeJobs.flatMap { job -> job.await() }.toSet()
        val totalDuration = Duration.between(startTime, Instant.now())

        log.debug(
            "[RENOVATION COSTS GENERATION] Completed parallel processing of all type groups for building ID: {} in {} ms, total results: {}",
            buildingCalculationInput.fixedAttributes.buildingId, totalDuration.toMillis(), results.size
        )
        results
    }

    override fun getProfitability(buildingId: UUID): CalculationProfitabilityDto {
        return calculationProfitabilityService.getProfitabilityCalculation(buildingId)
    }


    override fun generateCosts(buildingId: UUID): List<RenovationCostGenerationDto> {
        val buildingCalculationInput = buildingCalculationInputService.getBuildingCalculationInput(buildingId)
        val renovations = renovationRepository.findAllByBuildingId(buildingId).toSet()
        val buildingShapeCalculationResult = buildingShape.calculate(buildingCalculationInput)

        val renovationCosts = runBlocking {
            generateRenovationCostsParallel(
                buildingCalculationInput,
                renovations,
                buildingShapeCalculationResult
            )
        }

        return renovationCosts.map { renovationCostGenerationMapper.toDto(it) }
    }

    override fun getRenovationCosts(buildingId: UUID): List<RenovationCostGenerationDto> {
        val renovationCosts = renovationRepository.findAllByBuildingId(buildingId)
        return renovationCosts.map { renovationCostGenerationMapper.toDto(it) }
    }

    override fun calculateProfitability(
        buildingCalculationInputBeforeRenovation: BuildingCalculationInput,
        buildingCalculationInputAfterRenovation: BuildingCalculationInput
    ): CalculationProfitabilityDto {
        val calculationResults =
            calculationResultRepository.findByBuildingId(buildingCalculationInputAfterRenovation.fixedAttributes.buildingId)
        val renovations = getRenovations(buildingCalculationInputAfterRenovation.fixedAttributes.buildingId)
        return calculationProfitabilityService.calculateProfitability(
            buildingCalculationInputBeforeRenovation,
            buildingCalculationInputAfterRenovation,
            calculationResults,
            renovations
        )
    }

}