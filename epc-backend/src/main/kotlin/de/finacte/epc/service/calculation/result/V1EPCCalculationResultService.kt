package de.finacte.epc.service.calculation.result

import de.finacte.epc.repository.CarbonEmissionFactorRepository
import de.finacte.epc.repository.PrimaryEnergyFactorRepository
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeImpl
import de.finacte.epc.service.calculation.result.intermediate.climateData.V1ClimateData
import de.finacte.epc.service.calculation.result.intermediate.electricityDemand.V1ElectricDemand
import de.finacte.epc.service.calculation.result.intermediate.finalEffectiveCoolingEnergy.V1FinalEffectiveCoolingEnergy
import de.finacte.epc.service.calculation.result.intermediate.finalEffectiveHeatingEnergy.V1FinalEffectiveHeatingEnergy
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.appliances.V1AppliancesHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.light.V1LightHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.occupants.V1OccupantsHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.solar.V1SolarHeatEnergyGain
import de.finacte.epc.service.calculation.result.intermediate.heatTransferCoefficient.V1HeatTransferCoefficient
import de.finacte.epc.service.calculation.result.intermediate.hotWaterDemand.V1HotWaterDemand
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant.V1SolarPanelsProduction
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarThermal.V1SolarThermalProduction
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculation
import de.finacte.epc.service.calculation.result.intermediate.ventilationLossCoefficient.V1VentilationLossCoefficient
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

@Service
@ConditionalOnProperty(prefix = "epc", name = ["calculation.calculation-service"], havingValue = "V1")
class V1EPCCalculationResultService(
    buildingShape: BuildingShapeImpl,
    climateData: V1ClimateData,
    electricityDemand: V1ElectricDemand,
    finalEffectiveCoolingEnergy: V1FinalEffectiveCoolingEnergy,
    finalEffectiveHeatingEnergy: V1FinalEffectiveHeatingEnergy,
    appliancesHeatEnergyGain: V1AppliancesHeatEnergyGain,
    lightHeatEnergyGain: V1LightHeatEnergyGain,
    occupantsHeatEnergyGain: V1OccupantsHeatEnergyGain,
    solarHeatEnergyGain: V1SolarHeatEnergyGain,
    heatTransferCoefficient: V1HeatTransferCoefficient,
    hotWaterDemand: V1HotWaterDemand,
    ventilationLossCoefficient: V1VentilationLossCoefficient,
    primaryEnergyFactorRepository: PrimaryEnergyFactorRepository,
    uValueCalculation: UValueCalculation,
    solarThermalProduction: V1SolarThermalProduction,
    solarPlantProduction: V1SolarPanelsProduction,
    scaleGradePicker: V1ScaleGradePicker,
    carbonEmissionFactorRepository: CarbonEmissionFactorRepository
) : AbstractEPCCalculationResultService(
    appliancesHeatEnergyGain = appliancesHeatEnergyGain,
    climateData = climateData,
    lightHeatEnergyGain = lightHeatEnergyGain,
    solarHeatEnergyGain = solarHeatEnergyGain,
    electricityDemand = electricityDemand,
    hotWaterDemand = hotWaterDemand,
    heatTransferCoefficient = heatTransferCoefficient,
    occupantsHeatEnergyGain = occupantsHeatEnergyGain,
    ventilationLossCoefficient = ventilationLossCoefficient,
    finalEffectiveCoolingEnergy = finalEffectiveCoolingEnergy,
    finalEffectiveHeatingEnergy = finalEffectiveHeatingEnergy,
    primaryEnergyFactorRepository = primaryEnergyFactorRepository,
    uValueCalculation = uValueCalculation,
    solarThermalProduction = solarThermalProduction,
    solarPanelsProduction = solarPlantProduction,
    scaleGradePicker = scaleGradePicker,
    carbonEmissionFactorRepository = carbonEmissionFactorRepository
)