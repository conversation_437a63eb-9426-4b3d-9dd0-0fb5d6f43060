package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

/*
* This strategy is currently used to recommend Basement Ceiling insulation and this difference in naming can be at
* some point refactored but it will involve looking into calculation methods
* */
@Service
class GroundFloorInsulationRenovationRecommendationStrategy : RenovationRecommendationStrategy() {
    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean = !buildingCalculationInput.basementHeating.basementHeated
            && buildingCalculationInput.fixedAttributes.basementExist

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.GROUND_FLOOR_INSULATION,
            RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            VERSION_1
        )
}