package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.dto.building.BuildingMissingAttributesGenerated
import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.BuildingElectricalEquipment
import de.finacte.epc.entity.building.BuildingEntity
import org.springframework.stereotype.Service
import java.util.Objects.isNull

@Service
class BuildingCalculationInputFactoryImpl : BuildingCalculationInputFactory {

    override fun createBuildingCalculationInput(
        building: BuildingEntity,
        buildingMissingAttributesGenerated: BuildingMissingAttributesGenerated
    ): BuildingCalculationInput {
        val hasBasement = building.basement != null

        return BuildingCalculationInput(
            fixedAttributes = BuildingCalculationInput.FixedAttributes(
                buildingId = requireNotNull(building.id),
                shape = building.shape,
                position = building.position,
                zipCode = building.zipCode,
                constructionYear = building.constructionYear,
                area = building.area,
                floors = building.floors,
                floorHeight = building.floorHeight.value,
                tenants = building.tenants,
                roofFloor = requireNotNull(building.roof?.floor),
                facadeWallType = buildingMissingAttributesGenerated.facadeWallType,
                facadeWallThickness = buildingMissingAttributesGenerated.facadeWallThickness,
                windowsToWallRatio = requireNotNull(building.windows?.windowsToWallRatio),
                basementFloorThickness = buildingMissingAttributesGenerated.basementFloorThickness,
                basementFloorType = buildingMissingAttributesGenerated.basementFloorType,
                basementExternalWallsType = buildingMissingAttributesGenerated.basementExternalWallsType,
                ceilingWallThickness = buildingMissingAttributesGenerated.ceilingWallThickness,
                ceilingWallType = buildingMissingAttributesGenerated.ceilingWallType,
                groundFloorType = buildingMissingAttributesGenerated.groundFloorType,
                groundFloorThickness = buildingMissingAttributesGenerated.groundFloorThickness,
                basementExist = (building.basement != null),
                acInstalled = hasAcInstalled(building),
            ),

            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType
                    .fromBuildingHeating(requireNotNull(building.heating?.primaryHeating)),
                hotWaterEnergySource = SystemEfficiencyType
                    .fromBuildingHeating(requireNotNull(building.heating?.waterHeating)),
                heatingInstallationYear = building.heating?.primaryHeatingInstallationYear ?: building.constructionYear,
                heatingInstallationModernized = building.heating?.primaryHeatingInstallationYear != null &&
                        building.heating?.primaryHeatingInstallationYear != building.constructionYear,
            ),
            roofMaterial = BuildingCalculationInput.RoofMaterial(
                roofMaterial = buildingMissingAttributesGenerated.roofMaterial,
            ),
            roofInsulation = if(requireNotNull(building.roof?.ceilingOrRoofInsulated)) BuildingCalculationInput.RoofInsulation(
                roofInsulationRenewed = isNull(building.roof?.insulationYear),
                roofInsulationYear = building.roof?.insulationYear ?: building.constructionYear,
                roofInsulationThickness = buildingMissingAttributesGenerated.roofInsulationThickness,
                roofInsulationType = buildingMissingAttributesGenerated.roofInsulationType
            ) else null,
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = building.basement?.heated ?: false
            ),
            basementInsulation = if(hasBasement && building.basement!!.insulated) BuildingCalculationInput.BasementInsulation(
                basementFloorInsulationType = buildingMissingAttributesGenerated.basementFloorInsulationType,
                basementFloorInsulationThickness = buildingMissingAttributesGenerated.basementFloorInsulationThickness,
                basementExternalWallsInsulationType = buildingMissingAttributesGenerated.basementExternalWallsInsulationType,
                basementExternalWallsInsulationThickness = buildingMissingAttributesGenerated.basementExternalWallsInsulationThickness,
                basementInsulationYear = building.basement?.insulationYear ?: building.constructionYear,
            ) else null,
            ceilingWallInsulation = if(requireNotNull(building.roof?.ceilingOrRoofInsulated)) BuildingCalculationInput.CeilingWallInsulation(
                ceilingWallInsulationType = buildingMissingAttributesGenerated.ceilingWallInsulationType,
                ceilingWallInsulationThickness = buildingMissingAttributesGenerated.ceilingWallInsulationThickness,
            ) else null,
            entranceDoor = BuildingCalculationInput.EntranceDoor(
                entranceDoorMaterial = buildingMissingAttributesGenerated.entranceDoorMaterial
            ),
            groundFloorInsulation = if(requireNotNull(building.groundFloorInsulated)) BuildingCalculationInput.GroundFloorInsulation(
                groundFloorInsulationType = buildingMissingAttributesGenerated.groundFloorInsulationType,
                groundFloorInsulationThickness = buildingMissingAttributesGenerated.groundFloorInsulationThickness,
                groundFloorInsulationYear = building.constructionYear
            ) else null,
            solarThermal = BuildingCalculationInput.SolarThermal(
                solarThermalExist = (building.heating?.hasSolarThermalPlant) ?: false,
            ),
            solarPlant = BuildingCalculationInput.SolarPlant(
                solarPlantExist = (building.roof?.hasSolarPlant) ?: false,
                solarPlantNominalPower = (building.roof?.solarPlantPower),
                solarPlantInstallationYear = building.roof?.solarPlantInstallationYear ?: building.constructionYear,
            ),
            facadeWallInsulation = if(requireNotNull(building.facade?.insulated)) BuildingCalculationInput.FacadeWallInsulation(
                facadeWallInsulationYear = building.facade?.insulationYear ?: building.constructionYear,
                facadeWallInsulationRenewed = isNull(building.facade?.insulationYear),
                facadeWallInsulationType = buildingMissingAttributesGenerated.facadeWallInsulationType,
                facadeWallInsulationThickness = buildingMissingAttributesGenerated.facadeWallInsulationThickness,
            ) else null,
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = requireNotNull(building.windows?.glazing),
                windowsRenewed = isNull(building.windows?.installationYear),
                windowsInstallationYear = building.windows?.installationYear ?: building.constructionYear,
            ),
            windowsFrame = BuildingCalculationInput.WindowsFrame(
                windowsFrameMaterial = buildingMissingAttributesGenerated.windowsFrameMaterial
            ),
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = hasVentHeatExchangeInstalled(building),
            ),
            windowsShutters = if(building.windows?.shutters != null) BuildingCalculationInput.WindowsShutters(
                windowsShutters = requireNotNull(building.windows?.shutters)
            ) else null,
            windowsCoating = BuildingCalculationInput.WindowsCoating(
                windowsCoatingType = buildingMissingAttributesGenerated.windowsCoatingType,
            ),
        )
    }

    private fun hasVentHeatExchangeInstalled(building: BuildingEntity): Boolean {
        return building.electricalEquipment.any { it.type == BuildingElectricalEquipment.VENTILATION_HEAT_EXCHANGE }
    }

    private fun hasAcInstalled(building: BuildingEntity): Boolean {
        return building.electricalEquipment.any { it.type == BuildingElectricalEquipment.AC }
    }
}