package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.*
import de.finacte.epc.mapper.BuildingMissingAttributesGeneratedMapper
import de.finacte.epc.repository.BuildingMissingAttributesGeneratedRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.BuildingMissingAttributesInputGPTGenerator
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.BuildingMissingAttributesInputStaticGenerator
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service
import java.util.Objects.isNull

@Service
class BuildingCalculationInputFactoryImpl(
    private val buildingMissingAttributesGeneratedMapper: BuildingMissingAttributesGeneratedMapper,
    private val buildingMissingAttributesGeneratedRepository: BuildingMissingAttributesGeneratedRepository,
    private val buildingMissingAttributesInputGPTGenerator: BuildingMissingAttributesInputGPTGenerator,
    private val buildingMissingAttributesInputStaticGenerator: BuildingMissingAttributesInputStaticGenerator
) : BuildingCalculationInputFactory {

    companion object {
        private val log by LoggerDelegate()
    }

    override fun createBuildingCalculationInput(building: BuildingEntity): BuildingCalculationInput {
        val buildingMissingAttributesGenerated = findInDbOrGenerate(building)

        val hasBasement = building.basement != null

        return BuildingCalculationInput(
            fixedAttributes = BuildingCalculationInput.FixedAttributes(
                buildingId = requireNotNull(building.id),
                shape = building.shape,
                position = building.position,
                zipCode = building.zipCode,
                constructionYear = building.constructionYear,
                area = building.area,
                floors = building.floors,
                floorHeight = building.floorHeight.value,
                tenants = building.tenants,
                roofFloor = requireNotNull(building.roof?.floor),
                facadeWallType = buildingMissingAttributesGenerated.facadeWallType,
                facadeWallThickness = buildingMissingAttributesGenerated.facadeWallThickness,
                windowsToWallRatio = requireNotNull(building.windows?.windowsToWallRatio),
                basementExist = (building.basement != null),
                basementExternalWallsType = buildingMissingAttributesGenerated.basementExternalWallsType,
                ceilingWallType = buildingMissingAttributesGenerated.ceilingWallType,
                ceilingWallThickness = buildingMissingAttributesGenerated.ceilingWallThickness,
                acInstalled = hasAcInstalled(building),
            ),
            heating = BuildingCalculationInput.Heating(
                heatingEnergySource = SystemEfficiencyType
                    .fromBuildingHeating(requireNotNull(building.heating?.primaryHeating)),
                hotWaterEnergySource = SystemEfficiencyType
                    .fromBuildingHeating(requireNotNull(building.heating?.waterHeating)),
                heatingInstallationYear = building.heating?.primaryHeatingInstallationYear ?: building.constructionYear,
                heatingInstallationModernized = building.heating?.primaryHeatingInstallationYear != null &&
                        building.heating?.primaryHeatingInstallationYear != building.constructionYear,
            ),
            roofMaterial = BuildingCalculationInput.RoofMaterial(
                roofMaterial = buildingMissingAttributesGenerated.roofMaterial,
            ),
            roofInsulation = if (requireNotNull(building.roof?.ceilingOrRoofInsulated)) BuildingCalculationInput.RoofInsulation(
                roofInsulationRenewed = isNull(building.roof?.insulationYear),
                roofInsulationYear = building.roof?.insulationYear ?: building.constructionYear,
                roofInsulationThickness = buildingMissingAttributesGenerated.roofInsulationThickness,
                roofInsulationType = buildingMissingAttributesGenerated.roofInsulationType
            ) else null,
            basementHeating = BuildingCalculationInput.BasementHeating(
                basementHeated = building.basement?.heated ?: false
            ),
            basementInsulation = if (hasBasement && building.basement!!.insulated) BuildingCalculationInput.BasementInsulation(
                basementFloorInsulationType = buildingMissingAttributesGenerated.basementFloorInsulationType,
                basementFloorInsulationThickness = buildingMissingAttributesGenerated.basementFloorInsulationThickness,
                basementExternalWallsInsulationType = buildingMissingAttributesGenerated.basementExternalWallsInsulationType,
                basementExternalWallsInsulationThickness = buildingMissingAttributesGenerated.basementExternalWallsInsulationThickness,
                basementInsulationYear = building.basement?.insulationYear ?: building.constructionYear,
            ) else null,
            ceilingWallInsulation = if (requireNotNull(building.roof?.ceilingOrRoofInsulated)) BuildingCalculationInput.CeilingWallInsulation(
                ceilingWallInsulationType = buildingMissingAttributesGenerated.ceilingWallInsulationType,
                ceilingWallInsulationThickness = buildingMissingAttributesGenerated.ceilingWallInsulationThickness,
            ) else null,
            entranceDoor = BuildingCalculationInput.EntranceDoor(
                entranceDoorMaterial = buildingMissingAttributesGenerated.entranceDoorMaterial
            ),
            groundFloorInsulation = if (requireNotNull(building.groundFloorInsulated)) BuildingCalculationInput.GroundFloorInsulation(
                groundFloorInsulationType = buildingMissingAttributesGenerated.groundFloorInsulationType,
                groundFloorInsulationThickness = buildingMissingAttributesGenerated.groundFloorInsulationThickness,
                groundFloorInsulationYear = building.constructionYear
            ) else null,
            solarThermal = BuildingCalculationInput.SolarThermal(
                solarThermalExist = (building.heating?.hasSolarThermalPlant) ?: false,
            ),
            solarPanels = BuildingCalculationInput.SolarPanels(
                solarPanelsExist = (building.roof?.hasSolarPlant) ?: false,
                solarPanelsNominalPower = (building.roof?.solarPlantPower),
                installationYear = building.roof?.solarPlantInstallationYear ?: building.constructionYear,
            ),
            facadeWallInsulation = if (requireNotNull(building.facade?.insulated)) BuildingCalculationInput.FacadeWallInsulation(
                facadeWallInsulationYear = building.facade?.insulationYear ?: building.constructionYear,
                facadeWallInsulationRenewed = isNull(building.facade?.insulationYear),
                facadeWallInsulationType = buildingMissingAttributesGenerated.facadeWallInsulationType,
                facadeWallInsulationThickness = buildingMissingAttributesGenerated.facadeWallInsulationThickness,
            ) else null,
            windowsGlazing = BuildingCalculationInput.WindowsGlazing(
                windowsGlazing = requireNotNull(building.windows?.glazing),
                windowsRenewed = isNull(building.windows?.installationYear),
                windowsInstallationYear = building.windows?.installationYear ?: building.constructionYear,
            ),
            windowsFrame = BuildingCalculationInput.WindowsFrame(
                windowsFrameMaterial = buildingMissingAttributesGenerated.windowsFrameMaterial
            ),
            ventHeatExchange = BuildingCalculationInput.VentHeatExchange(
                ventHeatExchangeInstalled = hasVentHeatExchangeInstalled(building),
            ),
            windowsShutters = if (building.windows?.shutters != null) BuildingCalculationInput.WindowsShutters(
                windowsShutters = requireNotNull(building.windows?.shutters)
            ) else null,
            windowsCoating = BuildingCalculationInput.WindowsCoating(
                windowsCoatingType = buildingMissingAttributesGenerated.windowsCoatingType,
            ),
            surfaceHeating = if (requireNotNull(building.heating?.hasFloorHeating)) BuildingCalculationInput.SurfaceHeating(
                deliverySystem = BuildingHeatingDeliverySystem.UNDERFLOOR_WET
            ) else null,
            heatingRadiators = BuildingCalculationInput.HeatingRadiators(
                radiatorType = BuildingHeatingRadiatorType.STANDARD
            ),
            intelligentHeatingControls = BuildingCalculationInput.IntelligentHeatingControls(
                installed = false
            ),
            lightingAutomation = BuildingCalculationInput.LightingAutomation(
                installed = false
            ),
            solarPanelsBattery = null,
            solarPanelsImmersionHeater = BuildingCalculationInput.SolarPanelsImmersionHeater(
                installed = false
            ),
            groundFloor = buildingMissingAttributesInputStaticGenerator.calculateGroundFloor(building),
            basementFloor = buildingMissingAttributesInputStaticGenerator.calculateBasementFloor(building),
        )
    }

    private fun findInDbOrGenerate(building: BuildingEntity): BuildingMissingAttributesGeneratedEntity {
        when (val value = buildingMissingAttributesGeneratedRepository.findByBuildingId(requireNotNull(building.id))) {
            null -> {
                val buildingMissingAttributesGenerated = buildingMissingAttributesInputGPTGenerator.calculate(building)

                val buildingMissingAttributesGeneratedEntity =
                    buildingMissingAttributesGeneratedMapper.toEntity(
                        buildingMissingAttributesGenerated,
                        requireNotNull(building.id)
                    )
                return buildingMissingAttributesGeneratedRepository.save(buildingMissingAttributesGeneratedEntity)
            }

            else -> {
                log.debug(
                    "[BUILDING ATTRIBUTES] Building missing attributes found in DB, building ID: {} ,values:\n{}",
                    building.id, value
                )
                return value
            }
        }
    }

    private fun hasVentHeatExchangeInstalled(building: BuildingEntity): Boolean {
        return building.electricalEquipment.any { it.type == BuildingElectricalEquipment.VENTILATION_HEAT_EXCHANGE }
    }

    private fun hasAcInstalled(building: BuildingEntity): Boolean {
        return building.electricalEquipment.any { it.type == BuildingElectricalEquipment.AC }
    }
}