package de.finacte.epc.service.calculation.buildingShape.v1

import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeImpl.Companion.AVG_DOOR_AREA_IN_GERMANY

fun calculateDoorThermalEnvelopArea(): BuildingShapeResult.ThermalEnvelopeElement {
    return BuildingShapeResult.ThermalEnvelopeElement(
        area = AVG_DOOR_AREA_IN_GERMANY,
        type = ThermalEnvelopeElementType.DOOR
    )
}