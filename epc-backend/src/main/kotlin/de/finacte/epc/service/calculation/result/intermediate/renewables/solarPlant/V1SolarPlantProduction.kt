package de.finacte.epc.service.calculation.result.intermediate.renewables.solarPlant

import de.finacte.epc.entity.SystemEfficiencyType
import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.repository.SystemEfficiencyRepository
import de.finacte.epc.service.calculation.CalculationConstants.DAYS_PER_YEAR
import de.finacte.epc.service.calculation.CalculationConstants.WATT_TO_KILOWATT_RATIO
import de.finacte.epc.service.calculation.Season
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import de.finacte.epc.service.calculation.result.intermediate.renewables.solarThermal.HotWaterDemandWithoutSolarThermalProductionResult
import org.springframework.stereotype.Service

@Service
class V1SolarPlantProduction(
    private val systemEfficiencyRepository: SystemEfficiencyRepository
) : SolarPlantProduction {

    companion object {
        const val FLAT_ROOF_COVERAGE_RATIO =
            0.6 // Coverage should be around 0.4 but flat roof systems might be more efficient (better sunlight exposure)
        const val PITCHED_ROOF_COVERAGE_RATIO = 0.7
        const val AVG_SOLAR_PANEL_SIZE_SQR_METERS = 2.0
        const val AVG_SOLAR_ENERGY_CONSUMPTION_BY_ELECTRICITY = 0.6 // Electricity is used also in the time of the day where there is no production
    }

    override fun calculate(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult,
        climateDataResult: ClimateDataResult
    ): SolarPlantProductionResult {
        if (!buildingCalculationInput.solarPlant.solarPlantExist) {
            return SolarPlantProductionResult(
                seasonalProducedEnergy = getEmptySeasonalSolarPlantProduction(),
                annualProducedEnergy = 0.0
            )
        }

        val solarPlantNominalPowerKWP =
            buildingCalculationInput.solarPlant.solarPlantNominalPower ?: calculateNominalPower(
                buildingCalculationInput.fixedAttributes.roofFloor,
                buildingShapeResult.buildingRoofSurfaceArea,
                buildingCalculationInput.solarPlant.solarPlantInstallationYear
            )
        val solarPlantEfficiency = findSolarPlantEfficiency(
            buildingCalculationInput.solarPlant.solarPlantInstallationYear
        )

        val solarPlantProductionResult = Season.entries.associateWith {
            val seasonalRadiation = climateDataResult.averageAnnualSolarRadiation * it.radiationRatio

            solarPlantNominalPowerKWP * solarPlantEfficiency * seasonalRadiation
        }

        val annualProduction = solarPlantProductionResult.values.sum()

        return SolarPlantProductionResult(
            seasonalProducedEnergy = solarPlantProductionResult,
            annualProducedEnergy = annualProduction
        )
    }

    override fun deductFromCoolingEnergyDemand(
        coolingEnergyDemand: Double,
        solarPlantProductionResult: SolarPlantProductionResult,
        buildingCalculationInput: BuildingCalculationInput
    ): Pair<Double, SolarPlantProductionResult> {
        if (!buildingCalculationInput.fixedAttributes.acInstalled) {
            return Pair(coolingEnergyDemand, solarPlantProductionResult)
        }
        val summerProducedEnergy =
            requireNotNull(solarPlantProductionResult.seasonalProducedEnergy[Season.SUMMER])

        val adjustedEnergyDemand = maxOf(0.0, coolingEnergyDemand - summerProducedEnergy)
        val adjustedSolarProduction = maxOf(0.0, summerProducedEnergy - coolingEnergyDemand)

        val adjustedSeasonalSolarProduction = solarPlantProductionResult
            .seasonalProducedEnergy + (Season.SUMMER to adjustedSolarProduction)

        val adjustedAnnualProduction = adjustedSeasonalSolarProduction.values.sum()
        return adjustedEnergyDemand to SolarPlantProductionResult(
            seasonalProducedEnergy = adjustedSeasonalSolarProduction,
            annualProducedEnergy = adjustedAnnualProduction
        )
    }

    override fun deductFromHeatingEnergyDemand(
        heatingEnergyDemand: Double,
        solarPlantProductionResult: SolarPlantProductionResult,
        buildingCalculationInputHeating: BuildingCalculationInput.Heating
    ): Pair<Double, SolarPlantProductionResult> {
        if (buildingCalculationInputHeating.heatingEnergySource !in
            SystemEfficiencyType.getElectricHeatingEnergySources()
        ) {
            return Pair(heatingEnergyDemand, solarPlantProductionResult)
        }
        val summerProducedEnergy =
            requireNotNull(solarPlantProductionResult.seasonalProducedEnergy[Season.SUMMER])
        val adjustedSeasonalSolarProduction = mutableMapOf(Season.SUMMER to summerProducedEnergy)

        var adjustedHeatingDemand = heatingEnergyDemand

        listOf(Season.AUTUMN, Season.WINTER, Season.SPRING).forEach { season ->
            val seasonSolarProduction =
                requireNotNull(solarPlantProductionResult.seasonalProducedEnergy[season])

            val adjustedSeasonSolarProduction = maxOf(0.0, seasonSolarProduction - adjustedHeatingDemand)
            adjustedHeatingDemand = maxOf(0.0, adjustedHeatingDemand - seasonSolarProduction)

            adjustedSeasonalSolarProduction[season] = adjustedSeasonSolarProduction
        }
        val adjustedAnnualSolarProduction = adjustedSeasonalSolarProduction.values.sum()

        return adjustedHeatingDemand to SolarPlantProductionResult(
            seasonalProducedEnergy = adjustedSeasonalSolarProduction.toMap(),
            annualProducedEnergy = adjustedAnnualSolarProduction
        )
    }

    override fun deductFromHotWaterDemand(
        hotWaterDemandResult: HotWaterDemandWithoutSolarThermalProductionResult,
        solarPlantProductionResult: SolarPlantProductionResult,
        buildingCalculationInputHeating: BuildingCalculationInput.Heating
    ): Pair<HotWaterDemandWithoutSolarPlantProductionResult, SolarPlantProductionResult> {
        if (buildingCalculationInputHeating.hotWaterEnergySource != SystemEfficiencyType.ELECTRICITY) {
            return HotWaterDemandWithoutSolarPlantProductionResult(
                annualWaterDemandKWH = hotWaterDemandResult.annualWaterDemandKWH
            ) to solarPlantProductionResult
        }

        val energyDemandPerDay = hotWaterDemandResult.dailyWaterDemandKWH
        val (adjustedAnnualEnergyDemand, adjustedSolarProduction) = deductFromEnergyDemand(
            solarPlantProductionResult,
            energyDemandPerDay
        )
        val adjustedAnnualSolarProduction = adjustedSolarProduction.values.sum()

        return Pair(
            HotWaterDemandWithoutSolarPlantProductionResult(
                annualWaterDemandKWH = adjustedAnnualEnergyDemand
            ),
            SolarPlantProductionResult(
                seasonalProducedEnergy = adjustedSolarProduction,
                annualProducedEnergy = adjustedAnnualSolarProduction
            )
        )
    }

    override fun deductFromElectricityDemand(
        electricityDemandResult: Double,
        solarPlantProductionResult: SolarPlantProductionResult
    ): Pair<Double, SolarPlantProductionResult> {
        val energyDemandPerDay = electricityDemandResult / DAYS_PER_YEAR
        val energyDemandUsedWithSolarEnergy = energyDemandPerDay * AVG_SOLAR_ENERGY_CONSUMPTION_BY_ELECTRICITY
        val productionVsConsumptionDiff = energyDemandPerDay - energyDemandUsedWithSolarEnergy

        val (adjustedAnnualEnergyDemand, adjustedSolarProduction) = deductFromEnergyDemand(
            solarPlantProductionResult,
            energyDemandUsedWithSolarEnergy
        )
        val adjustedAnnualSolarProduction = adjustedSolarProduction.values.sum()

        return Pair(
            adjustedAnnualEnergyDemand + (productionVsConsumptionDiff * DAYS_PER_YEAR),
            SolarPlantProductionResult(
                seasonalProducedEnergy = adjustedSolarProduction,
                annualProducedEnergy = adjustedAnnualSolarProduction
            )
        )
    }

    private fun deductFromEnergyDemand(
        solarPlantProductionResult: SolarPlantProductionResult,
        energyDemandPerDay: Double
    ): Pair<Double, Map<Season, Double>> {
        val energyDemandAndSolarProduction = solarPlantProductionResult
            .seasonalProducedEnergy
            .entries.associate { (season, solarProduction) ->
                val seasonalEnergyDemand = season.days * energyDemandPerDay
                val adjustedEnergyDemand = maxOf(0.0, seasonalEnergyDemand - solarProduction)
                val adjustedSolarProduction = maxOf(0.0, solarProduction - seasonalEnergyDemand)

                season to Pair(adjustedEnergyDemand, adjustedSolarProduction)
            }

        val adjustedAnnualEnergyDemand = energyDemandAndSolarProduction.values.sumOf {
            it.first
        }

        val adjustedSolarProduction = energyDemandAndSolarProduction.map {
            it.key to it.value.second
        }.toMap()
        return Pair(adjustedAnnualEnergyDemand, adjustedSolarProduction)
    }

    private fun getEmptySeasonalSolarPlantProduction(): Map<Season, Double> =
        Season.entries.associateWith {
            0.0
        }

    private fun calculateNominalPower(
        buildingRoofFloor: BuildingRoofFloor,
        buildingRoofArea: Double,
        solarPlantInstallationYear: Int
    ): Double {
        val avgSolarPanelPower = calculateSolarPanelPower(solarPlantInstallationYear)
        val nominalPowerWats = when (buildingRoofFloor) {
            BuildingRoofFloor.FLAT_ROOF -> (buildingRoofArea * FLAT_ROOF_COVERAGE_RATIO) /
                    AVG_SOLAR_PANEL_SIZE_SQR_METERS * avgSolarPanelPower

            else -> (buildingRoofArea * PITCHED_ROOF_COVERAGE_RATIO) /
                    AVG_SOLAR_PANEL_SIZE_SQR_METERS * avgSolarPanelPower
        }

        return nominalPowerWats / WATT_TO_KILOWATT_RATIO
    }

    private fun calculateSolarPanelPower(solarPlantInstallationYear: Int): Double =
        findSolarPanelEfficiency(solarPlantInstallationYear) * 1000 * AVG_SOLAR_PANEL_SIZE_SQR_METERS

    private fun findSolarPanelEfficiency(solarPlantInstallationYear: Int): Double {
        return systemEfficiencyRepository.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
            SystemEfficiencyType.SOLAR_PANEL,
            solarPlantInstallationYear)
            ?.efficiency ?:
            throw EPCCalculationException("Can't find efficiency for ${SystemEfficiencyType.SOLAR_PANEL} for $solarPlantInstallationYear year")
    }

    private fun findSolarPlantEfficiency(solarPlantInstallationYear: Int): Double {
        return systemEfficiencyRepository.findBySystemEfficiencyTypeAndInstallationYearBetweenValidFromAndValidTo(
            SystemEfficiencyType.SOLAR_PLANT,
            solarPlantInstallationYear)
            ?.efficiency ?:
            throw EPCCalculationException("Can't find efficiency for ${SystemEfficiencyType.SOLAR_PLANT} for $solarPlantInstallationYear year")
    }
}