package de.finacte.epc.service.calculation.buildingShape.v1

import de.finacte.epc.entity.building.BuildingWindowsToWallRatio
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeImpl.Companion.AVG_DOOR_AREA_IN_GERMANY

fun calculateFacadeWallsThermalEnvelopArea(
    externalWallAreaWithWindows: Double,
    buildingWindowsToWallRatio: BuildingWindowsToWallRatio
): BuildingShapeResult.ThermalEnvelopeElement {
    return BuildingShapeResult.ThermalEnvelopeElement(
        area = (externalWallAreaWithWindows - AVG_DOOR_AREA_IN_GERMANY) * (1 - buildingWindowsToWallRatio.ratio),
        type = ThermalEnvelopeElementType.FACADE_WALLS
    )
}