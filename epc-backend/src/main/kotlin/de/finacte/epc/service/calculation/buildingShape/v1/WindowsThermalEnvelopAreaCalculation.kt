package de.finacte.epc.service.calculation.buildingShape.v1

import de.finacte.epc.entity.building.BuildingWindowsToWallRatio
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeImpl.Companion.AVG_DOOR_AREA_IN_GERMANY

fun calculateWindowsThermalEnvelopArea(
    externalWallAreaWithWindowsAndDoor: Double,
    buildingWindowsToWallRatio: BuildingWindowsToWallRatio
): BuildingShapeResult.ThermalEnvelopeElement {
    return BuildingShapeResult.ThermalEnvelopeElement(
        area = (externalWallAreaWithWindowsAndDoor - AVG_DOOR_AREA_IN_GERMANY) * buildingWindowsToWallRatio.ratio,
        type = ThermalEnvelopeElementType.WINDOWS
    )
}