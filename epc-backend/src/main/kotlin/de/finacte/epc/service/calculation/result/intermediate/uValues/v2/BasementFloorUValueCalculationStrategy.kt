package de.finacte.epc.service.calculation.result.intermediate.uValues.v2

import de.finacte.epc.entity.ConstructionMaterialType
import de.finacte.epc.entity.building.BuildingWallType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.repository.ConstructionMaterialPropertiesRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service

@Service
class BasementFloorUValueCalculationStrategy(
    constructionMaterialPropertiesRepository: ConstructionMaterialPropertiesRepository
) : GroundFloorUValueCalculationStrategy(constructionMaterialPropertiesRepository) {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun getThermalEnvelopeElementTypeForStrategy(): ThermalEnvelopeElementType =
        ThermalEnvelopeElementType.BASEMENT_FLOOR

    override fun apply(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): UValueCalculationResult.UValuesThermalEnvelopeElement {
        with(buildingCalculationInput) {
            val (floorBaseSlabMaterialThermalConductivity, floorBaseSlabThermalResistance) =
                calculateThermalConductivityAndResistance(
                    BuildingWallType.CONCRETE.name,
                    ConstructionMaterialType.WALL,
                    BASE_SLAB_THICKNESS
                )

            val (floorScreedMaterialThermalConductivity, floorScreedThermalResistance) =
                calculateThermalConductivityAndResistance(
                    basementFloor.basementFloorType.name,
                    ConstructionMaterialType.SCREED,
                    basementFloor.basementFloorThickness.toDouble()
                )

            val (floorFinishMaterialThermalConductivity, floorFinishThermalResistance) =
                calculateThermalConductivityAndResistance(
                    basementFloor.basementFloorFinishType.name,
                    ConstructionMaterialType.FLOOR_FINISH,
                    basementFloor.basementFloorFinishThickness.toDouble()
                )

            val (insulationThermalResistance, insulationMaterialThermalConductivity) =
                calculateInsulationThermalResistance(
                    basementInsulation?.basementFloorInsulationType,
                    basementInsulation?.basementFloorInsulationThickness
                )

            val groundThermalResistance =
                calculateGroundThermalResistance(buildingShapeResult)

            val thermalEnvelopeElementThermalResistance =
                floorBaseSlabThermalResistance + floorScreedThermalResistance + floorFinishThermalResistance +
                        insulationThermalResistance + groundThermalResistance + INTERNAL_SURFACE_RESISTANCE

            log.debug(
                "[U-VALUE CALCULATION] Floor base slab material thermal conductivity: {}," +
                        " Floor screed material thermal conductivity: {}," +
                        " Floor finish material thermal conductivity: {}," +
                        " Insulation material thermal conductivity: {}," +
                        " Floor base slab material thermal resistance: {}," +
                        " Floor screed material thermal resistance: {}," +
                        " Floor finish material thermal resistance: {}" +
                        " Insulation material thermal resistance: {}" +
                        " Main material type: {}",
                floorBaseSlabMaterialThermalConductivity,
                floorScreedMaterialThermalConductivity,
                floorFinishMaterialThermalConductivity,
                insulationMaterialThermalConductivity,
                floorBaseSlabThermalResistance,
                floorScreedThermalResistance,
                floorFinishThermalResistance,
                insulationThermalResistance,
                getThermalEnvelopeElementTypeForStrategy()
            )
            return UValueCalculationResult.UValuesThermalEnvelopeElement(
                uValue = 1 / thermalEnvelopeElementThermalResistance,
                thermalEnvelopeElementType = getThermalEnvelopeElementTypeForStrategy()
            )
        }
    }
}