package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.entity.building.BuildingEntity
import de.finacte.epc.entity.building.BuildingMissingAttributesGeneratedEntity
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.mapper.BuildingMissingAttributesGeneratedMapper
import de.finacte.epc.repository.BuildingMissingAttributesGeneratedRepository
import de.finacte.epc.repository.BuildingRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators.BuildingMissingAttributesInputGPTGenerator
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class BuildingCalculationInputServiceImpl(
    private val buildingBuildingCalculationInputFactory: BuildingCalculationInputFactory,
    private val buildingRepository: BuildingRepository,
) : BuildingCalculationInputService {

    companion object {
        private val log by LoggerDelegate()
    }

    @Transactional
    override fun getBuildingCalculationInput(buildingId: UUID): BuildingCalculationInput {
        val building = buildingRepository.findById(buildingId).orElseThrow {
            EPCCalculationException("Building with ID $buildingId not found")
        }
        log.debug(
            "[BUILDING ATTRIBUTES] Building original attributes, building ID: {} , values:\n{}",
            building.id, building
        )

        val buildingCalculationInput =
            buildingBuildingCalculationInputFactory.createBuildingCalculationInput(building)
        return buildingCalculationInput
    }
}
