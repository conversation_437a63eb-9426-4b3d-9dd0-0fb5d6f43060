package de.finacte.epc.service.calculation.buildingCalculationInput

import de.finacte.epc.dto.building.BuildingMissingAttributesGenerated
import de.finacte.epc.entity.building.BuildingEntity
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.mapper.BuildingMissingAttributesGeneratedMapper
import de.finacte.epc.repository.BuildingMissingAttributesGeneratedRepository
import de.finacte.epc.repository.BuildingRepository
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class BuildingCalculationInputServiceImpl(
    private val buildingMissingAttributesGeneratedMapper: BuildingMissingAttributesGeneratedMapper,
    private val buildingMissingAttributesGeneratedRepository: BuildingMissingAttributesGeneratedRepository,
    private val buildingBuildingCalculationInputFactory: BuildingCalculationInputFactory,
    private val buildingMissingAttributesInputGenerator: BuildingMissingAttributesInputGenerator,
    private val buildingRepository: BuildingRepository,
) : BuildingCalculationInputService {

    companion object {
        private val log by LoggerDelegate()
    }

    @Transactional
    override fun getBuildingCalculationInput(buildingId: UUID): BuildingCalculationInput {
        val building = buildingRepository.findById(buildingId).orElseThrow {
            EPCCalculationException("Building with ID $buildingId not found")
        }
        log.debug(
            "[BUILDING ATTRIBUTES] Building original attributes, building ID: {} , values:\n{}",
            building.id, building
        )

        val buildingMissingAttributesGenerated = findInDbOrGenerate(building)
        val buildingCalculationInput =
            buildingBuildingCalculationInputFactory.createBuildingCalculationInput(
                building,
                buildingMissingAttributesGenerated
            )
        return buildingCalculationInput
    }

    private fun findInDbOrGenerate(building: BuildingEntity): BuildingMissingAttributesGenerated {
        when (val value = buildingMissingAttributesGeneratedRepository.findByBuildingId(requireNotNull(building.id))) {
            null -> {
                val buildingMissingAttributesGenerated = buildingMissingAttributesInputGenerator.calculate(building)

                val buildingMissingAttributesGeneratedEntity =
                    buildingMissingAttributesGeneratedMapper.toEntity(
                        buildingMissingAttributesGenerated,
                        requireNotNull(building.id)
                    )
                buildingMissingAttributesGeneratedRepository.save(buildingMissingAttributesGeneratedEntity)
                return buildingMissingAttributesGenerated
            }

            else -> {
                log.debug(
                    "[BUILDING ATTRIBUTES] Building missing attributes found in DB, building ID: {} ,values:\n{}",
                    building.id, value
                )
                return buildingMissingAttributesGeneratedMapper.toDto(value)
            }
        }
    }
}