package de.finacte.epc.service.calculation.renovation.recommendationStrategy.ceilingInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class CeilingInsulationAdvancedRenovationRecommendationStrategy : RenovationRecommendationStrategy() {

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        if (buildingCalculationInput.roofInsulation == null) {
            return true
        }
        return buildingCalculationInput.roofInsulation.roofInsulationYear + 15 < Year.now().value

    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, RenovationMeasureVariant> =
        Triple(
            RenovationMeasureType.CEILING_INSULATION,
            RenovationMeasureValue.VACUUM_INSULATED_PANEL_5_CM_2025,
            RenovationMeasureVariant.ADVANCED
        )

}
