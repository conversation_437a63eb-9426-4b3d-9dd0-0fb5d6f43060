package de.finacte.epc.service.calculation.renovation

import de.finacte.epc.dto.calculation.CalculationProfitabilityDto
import de.finacte.epc.dto.calculation.RenovationCostGenerationDto
import de.finacte.epc.dto.calculation.RenovationDto
import de.finacte.epc.dto.renovation.RenovationTemplateDto
import de.finacte.epc.entity.renovation.RenovationCostEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import java.util.*

interface RenovationService {
    fun getRenovations(buildingId: UUID): List<RenovationDto>
    fun getRenovationTemplates(): Set<RenovationTemplateDto>
    fun generateRenovations(buildingId: UUID): List<RenovationDto>
    fun selectRenovations(
        buildingId: UUID,
        selectedRenovations: Set<UUID>,
        buildingCalculationInputBeforeRenovation: BuildingCalculationInput
    ): Pair<List<RenovationDto>, BuildingCalculationInput>

    fun generateBuildingCalculationInput(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplates: Set<RenovationTemplateDto>
    ): BuildingCalculationInput

    suspend fun generateRenovationCosts(
        buildingCalculationInput: BuildingCalculationInput,
        renovations: Set<RenovationEntity>,
        buildingShapeResult: BuildingShapeResult
    ): Set<RenovationCostEntity>

    suspend fun generateRenovationCostsParallel(
        buildingCalculationInput: BuildingCalculationInput,
        renovations: Set<RenovationEntity>,
        buildingShapeResult: BuildingShapeResult
    ): Set<RenovationCostEntity>

    fun generateCosts(buildingId: UUID): List<RenovationCostGenerationDto>

    fun getRenovationsV2(buildingId: UUID): List<RenovationDto>
    fun getProfitability(buildingId: UUID): CalculationProfitabilityDto
    fun getRenovationCosts(buildingId: UUID): List<RenovationCostGenerationDto>
    fun calculateProfitability(
        buildingCalculationInputBeforeRenovation: BuildingCalculationInput,
        buildingCalculationInputAfterRenovation: BuildingCalculationInput
    ): CalculationProfitabilityDto
}