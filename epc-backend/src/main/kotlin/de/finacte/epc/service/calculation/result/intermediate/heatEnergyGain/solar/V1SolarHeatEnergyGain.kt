package de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.solar

import de.finacte.epc.entity.building.BuildingWindowCoatingType
import de.finacte.epc.entity.building.BuildingWindowsGlazing
import de.finacte.epc.entity.building.BuildingWindowsShutters
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult
import de.finacte.epc.service.calculation.result.intermediate.heatEnergyGain.HeatEnergyGainResult
import org.springframework.stereotype.Service

@Service
class V1SolarHeatEnergyGain : SolarHeatEnergyGain {
    companion object {
        const val MANUAL_SHUTTERS_G_VALUE_REDUCTION_FACTOR = 0.7
        const val ELECTRIC_SHUTTERS_G_VALUE_REDUCTION_FACTOR = 0.8
        const val AVG_SOLAR_RADIATION_DISTRIBUTION_FACTOR =
            0.525 //it depends on what orientation this window is facing, this is average for all orientations
        const val AVG_HEATING_SEASON_HORIZONTAL_IRRADIANCE = 1.0 //kWh/m2/day
        const val AVG_COOLING_SEASON_HORIZONTAL_IRRADIANCE = 5.0 //kWh/m2/day
        val WINDOW_G_VALUE = mapOf(
            BuildingWindowsGlazing.SINGLE to 0.87,
            BuildingWindowsGlazing.DOUBLE to 0.70,
            BuildingWindowsGlazing.TRIPLE to 0.55
        )
        val WINDOW_COATING_G_VALUE_REDUCTION = mapOf(
            BuildingWindowCoatingType.LOW_E_COATING to 0.20,
            BuildingWindowCoatingType.TINTED_COATING to 0.225,
            BuildingWindowCoatingType.REFLECTIVE_COATING to 0.50,
            BuildingWindowCoatingType.UV_COATING to 0.05,
            BuildingWindowCoatingType.ANTI_GLARE_COATING to 0.05,
            BuildingWindowCoatingType.HYDROPHILIC_COATING to 0.02,
            BuildingWindowCoatingType.THERMAL_INSULATING_COATING to 0.15,
            BuildingWindowCoatingType.SOLAR_CONTROL_COATING to 0.325,
            BuildingWindowCoatingType.SCRATCH_RESISTANT_COATING to 0.01,
            BuildingWindowCoatingType.ANTI_FOG_COATING to 0.01,
            BuildingWindowCoatingType.SELF_CLEANING_COATING to 0.01,
            BuildingWindowCoatingType.SECURITY_FILM to 0.15,
            BuildingWindowCoatingType.PRIVACY_FILM to 0.225,
            BuildingWindowCoatingType.NONE to 0.0
        )
    }

    override suspend fun calculate(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult,
        climateDataResult: ClimateDataResult
    ): HeatEnergyGainResult {
        val gValue =
            calculateGValue(buildingCalculationInput)
        val windowsArea = getWindowsArea(buildingShapeResult)
        return HeatEnergyGainResult(
            summerHeatEnergyGainKWH = calculateSummerHeatGainKWH(windowsArea, gValue, climateDataResult),
            winterHeatEnergyGainKWH = calculateWinterHeatGainKWH(windowsArea, gValue, climateDataResult)
        )
    }

    private fun calculateWinterHeatGainKWH(
        windowsArea: Double,
        gValue: Double,
        climateDataResult: ClimateDataResult
    ): Double {
        val winterRadiation = AVG_HEATING_SEASON_HORIZONTAL_IRRADIANCE *
                climateDataResult.amountOfDaysContributingToHeatingDegreeDays *
                AVG_SOLAR_RADIATION_DISTRIBUTION_FACTOR
        return windowsArea * gValue * winterRadiation
    }

    private fun calculateSummerHeatGainKWH(
        windowsArea: Double,
        gValue: Double,
        climateDataResult: ClimateDataResult
    ) :Double {
        val summerRadiation = AVG_COOLING_SEASON_HORIZONTAL_IRRADIANCE *
                climateDataResult.amountOfDaysContributingToCoolingDegreeDays *
                AVG_SOLAR_RADIATION_DISTRIBUTION_FACTOR
        return windowsArea * gValue * summerRadiation
    }



    private fun calculateGValue(buildingCalculationInput: BuildingCalculationInput): Double {
        with(buildingCalculationInput) {
            val glazingGValue =
                WINDOW_G_VALUE[windowsGlazing.windowsGlazing]
                    ?: throw EPCCalculationException(
                        "Can't calculate solar heat gains. You must provide G Value for" +
                                " new glazing type"
                    )
            val coatingGValueReduction =
                WINDOW_COATING_G_VALUE_REDUCTION[windowsCoating.windowsCoatingType]
                    ?: throw EPCCalculationException(
                        "Can't calculate solar heat gains. You must provide" +
                                " G Value reduction for new coating type"
                    )
            val gValue =
                (glazingGValue - coatingGValueReduction) * getShuttersReductionFactor(buildingCalculationInput)
            return gValue
        }
    }

    private fun getShuttersReductionFactor(buildingCalculationInput: BuildingCalculationInput): Double =
        when (buildingCalculationInput.windowsShutters?.windowsShutters) {
            BuildingWindowsShutters.MANUAL -> 1 - MANUAL_SHUTTERS_G_VALUE_REDUCTION_FACTOR
            BuildingWindowsShutters.ELECTRIC -> 1 - ELECTRIC_SHUTTERS_G_VALUE_REDUCTION_FACTOR
            null -> 1.0 // no reduction
        }

    private fun getWindowsArea(buildingShapeResult: BuildingShapeResult): Double {
        return buildingShapeResult.thermalEnvelopeElements
            .find { it.type == ThermalEnvelopeElementType.WINDOWS }
            ?.area
            ?: throw EPCCalculationException(
                "Can't calculate solar heat energy gain due the lack of WINDOWS" +
                        " in thermalEnvelopeElements"
            )
    }
}