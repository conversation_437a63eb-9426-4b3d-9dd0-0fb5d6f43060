package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.utils.LoggerDelegate

abstract class RenovationRecommendationStrategy {
    companion object {
        private val log by LoggerDelegate()
        const val VERSION_1 = 1
        const val VERSION_2 = 2
    }

    fun test(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Pair<<PERSON>ole<PERSON>, Boolean> {
        val renovationStrategyKey = getRenovatedElementKeyForStrategy()
        val renovationTemplateKey =
            Triple(
                renovationTemplate.renovationMeasureType,
                renovationTemplate.renovationMeasureValue,
                renovationTemplate.renovationMeasureVersion
            )

        val (applicable, recommended) = if (renovationStrategyKey == renovationTemplateKey) {
            isApplicable(buildingCalculationInput, renovationTemplate) to isRecommendedStrategy(buildingCalculationInput, renovationTemplate)
        } else Pair(false, false)

        log.debug(
            "[RENOVATION RECOMMENDATION - {}] -Template: {} ,Applicable: {}, Recommended: {}",
            getRenovatedElementKeyForStrategy(),
            renovationTemplate,
            applicable,
            recommended
        )
        return applicable to recommended
    }

    protected abstract fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean

    protected abstract fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean

    protected abstract fun getRenovatedElementKeyForStrategy():
            Triple<RenovationMeasureType, RenovationMeasureValue, Int>

}