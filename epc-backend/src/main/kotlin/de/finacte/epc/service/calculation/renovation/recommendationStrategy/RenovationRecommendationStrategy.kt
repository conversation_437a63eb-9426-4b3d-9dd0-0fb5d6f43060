package de.finacte.epc.service.calculation.renovation.recommendationStrategy

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.utils.LoggerDelegate

abstract class RenovationRecommendationStrategy {
    companion object {
        private val log by LoggerDelegate()
    }

    fun test(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Pair<<PERSON>ole<PERSON>, <PERSON>olean> {
        val renovationStrategyKey = getRenovatedElementKeyForStrategy()
        val renovationTemplateKey = Triple(
            renovationTemplate.renovationMeasureType,
            renovationTemplate.renovationMeasureValue,
            renovationTemplate.renovationMeasureVariant
        )

        val (applicable, recommended) = if (renovationStrategyKey == renovationTemplateKey) {
            isApplicable(
                buildingCalculationInput,
                renovationTemplate
            ) to isRecommendedStrategy(buildingCalculationInput, renovationTemplate)
        } else Pair(false, false)

        log.debug(
            "[RENOVATION RECOMMENDATION - {}] -Template: {} ,Applicable: {}, Recommended: {}",
            getRenovatedElementKeyForStrategy(),
            renovationTemplate,
            applicable,
            recommended
        )
        return applicable to recommended
    }

    protected abstract fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean

    protected abstract fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean

    protected abstract fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, RenovationMeasureVariant>

}