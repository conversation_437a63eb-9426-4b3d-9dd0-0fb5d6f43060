package de.finacte.epc.service.calculation.renovation

import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureCategory
import de.finacte.epc.entity.renovation.RenovationCostEntity
import de.finacte.epc.entity.renovation.RenovationEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult

interface RenovationCostGenerationService {
    suspend fun getRenovationCost(
        renovations: Set<RenovationEntity>,
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): MutableIterable<RenovationCostEntity>

    data class RenovationCostGenerationResult(
        val renovationCostGenerationResultItem: List<RenovationCostGenerationResultItem>
    )

    data class RenovationCostGenerationResultItem(
        val renovationMeasureCategory: RenovationMeasureCategory,
        val renovationMeasureId: RenovationMeasureValue,
        val minCost: Double,
        val maxCost: Double,
        val mostProbableValue: Double
    )
}