package de.finacte.epc.service.calculation.renovation.recommendationStrategy.v1

import de.finacte.epc.entity.building.BuildingRoofFloor
import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service

@Service
class CeilingInsulationRenovationRecommendationStrategy : RenovationRecommendationStrategy() {
    companion object {
        val applicableRoofFloors = setOf(
            BuildingRoofFloor.FULLY_LIVEABLE_REDUCED,
            BuildingRoofFloor.FULLY_LIVEABLE_ELEVATED
        )
    }

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        return if (buildingCalculationInput.ceilingWallInsulation == null) {
            buildingCalculationInput.fixedAttributes.roofFloor in applicableRoofFloors
        } else false
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = true

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, Int> =
        Triple(
            RenovationMeasureType.CEILING_INSULATION,
            RenovationMeasureValue.EXTRUDED_POLYSTYRENE_10_CM_2025,
            VERSION_1
        )
}