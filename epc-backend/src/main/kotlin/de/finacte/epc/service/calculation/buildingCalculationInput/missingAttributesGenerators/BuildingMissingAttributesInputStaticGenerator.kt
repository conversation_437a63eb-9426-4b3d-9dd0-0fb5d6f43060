package de.finacte.epc.service.calculation.buildingCalculationInput.missingAttributesGenerators

import de.finacte.epc.entity.building.BuildingEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput

interface BuildingMissingAttributesInputStaticGenerator {
    fun calculateGroundFloor(building: BuildingEntity): BuildingCalculationInput.GroundFloor
    fun calculateBasementFloor(building: BuildingEntity): BuildingCalculationInput.BasementFloor
}