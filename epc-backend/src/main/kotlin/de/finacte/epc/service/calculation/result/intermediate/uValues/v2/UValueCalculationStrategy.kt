package de.finacte.epc.service.calculation.result.intermediate.uValues.v2

import de.finacte.epc.entity.ConstructionMaterialPropertiesEntity
import de.finacte.epc.entity.ConstructionMaterialType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.repository.ConstructionMaterialPropertiesRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult.UValuesThermalEnvelopeElement

abstract class UValueCalculationStrategy(
    private val constructionMaterialPropertiesRepository: ConstructionMaterialPropertiesRepository
) {
    abstract fun getThermalEnvelopeElementTypeForStrategy(): ThermalEnvelopeElementType
    abstract fun apply(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): UValuesThermalEnvelopeElement

    fun findConstructionMaterialUValue(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): Double = findConstructionMaterialProperties(name, constructionMaterialType).uValue
        ?: throw EPCCalculationException("U Value could not be found for $name material with $constructionMaterialType type")

    fun findConstructionMaterialThermalResistance(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): Double = findConstructionMaterialProperties(name, constructionMaterialType).thermalResistance
        ?: throw EPCCalculationException("Thermal resistance could not be found for $name material with $constructionMaterialType type")

    fun findConstructionMaterialThermalResistance(
        name: String,
        constructionMaterialType: ConstructionMaterialType,
        thickness: Double
    ): Double  = constructionMaterialPropertiesRepository.findByNameAndTypeBetweenThickness(
        name,
        constructionMaterialType,
        thickness
    )?.thermalResistance
        ?: throw EPCCalculationException("Thermal resistance could not be found for $name material with $constructionMaterialType type and $thickness thickness")

    fun findConstructionMaterialThermalConductivity(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): Double = findConstructionMaterialProperties(name, constructionMaterialType).thermalConductivity
        ?: throw EPCCalculationException("Thermal conductivity could not be found for $name material with $constructionMaterialType type")


    private fun findConstructionMaterialProperties(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): ConstructionMaterialPropertiesEntity = constructionMaterialPropertiesRepository.findByNameAndType(
        name,
        constructionMaterialType
    )
        ?: throw EPCCalculationException("Can't find construction material properties for $name and $constructionMaterialType")

}