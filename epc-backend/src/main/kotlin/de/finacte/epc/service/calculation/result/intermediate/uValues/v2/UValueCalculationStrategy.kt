package de.finacte.epc.service.calculation.result.intermediate.uValues.v2

import de.finacte.epc.entity.ConstructionMaterialPropertiesEntity
import de.finacte.epc.entity.ConstructionMaterialType
import de.finacte.epc.entity.building.ThermalEnvelopeElementType
import de.finacte.epc.exception.calculation.EPCCalculationException
import de.finacte.epc.repository.ConstructionMaterialPropertiesRepository
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.buildingShape.BuildingShapeResult
import de.finacte.epc.service.calculation.result.intermediate.uValues.UValueCalculationResult.UValuesThermalEnvelopeElement
import org.slf4j.LoggerFactory

abstract class UValueCalculationStrategy(
    private val constructionMaterialPropertiesRepository: ConstructionMaterialPropertiesRepository
) {
    private val log = LoggerFactory.getLogger(UValueCalculationStrategy::class.java)
    abstract fun getThermalEnvelopeElementTypeForStrategy(): ThermalEnvelopeElementType
    abstract fun apply(
        buildingCalculationInput: BuildingCalculationInput,
        buildingShapeResult: BuildingShapeResult
    ): UValuesThermalEnvelopeElement

    fun findConstructionMaterialUValue(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): Double = findConstructionMaterialProperties(name, constructionMaterialType).uValue
        ?: throw EPCCalculationException("U Value could not be found for $name material with $constructionMaterialType type")

    fun findConstructionMaterialThermalResistance(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): Double = findConstructionMaterialProperties(name, constructionMaterialType).thermalResistance
        ?: throw EPCCalculationException("Thermal resistance could not be found for $name material with $constructionMaterialType type")

    fun findConstructionMaterialThermalResistance(
        name: String,
        constructionMaterialType: ConstructionMaterialType,
        thickness: Double
    ): Double {
        val limitedThickness = when {
            thickness < 0.5 -> {
                log.warn("Thickness value $thickness is less than minimum allowed value 0.5. Using 0.5 instead.")
                0.5
            }
            thickness > 4.0 -> {
                log.warn("Thickness value $thickness is greater than maximum allowed value 4.0. Using 4.0 instead.")
                4.0
            }
            else -> thickness
        }

        return constructionMaterialPropertiesRepository.findByNameAndTypeBetweenThickness(
            name,
            constructionMaterialType,
            limitedThickness
        )?.thermalResistance
            ?: throw EPCCalculationException("Thermal resistance could not be found for $name material with $constructionMaterialType type and $limitedThickness thickness")
    }

    fun findConstructionMaterialThermalConductivity(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): Double = findConstructionMaterialProperties(name, constructionMaterialType).thermalConductivity
        ?: throw EPCCalculationException("Thermal conductivity could not be found for $name material with $constructionMaterialType type")


    private fun findConstructionMaterialProperties(
        name: String,
        constructionMaterialType: ConstructionMaterialType
    ): ConstructionMaterialPropertiesEntity = constructionMaterialPropertiesRepository.findByNameAndType(
        name,
        constructionMaterialType
    )
        ?: throw EPCCalculationException("Can't find construction material properties for $name and $constructionMaterialType")

}