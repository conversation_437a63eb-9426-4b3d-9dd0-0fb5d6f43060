package de.finacte.epc.service.calculation.result.intermediate.finalEffectiveHeatingEnergy

import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.result.intermediate.climateData.ClimateDataResult

interface FinalEffectiveHeatingEnergy {
    fun calculate(
        climateDataResult: ClimateDataResult,
        buildingCalculationInputHeating: BuildingCalculationInput.Heating,
        heatTransferCoefficientResult: Double,
        ventilationLossCoefficientResult: Double
    ): Double
}