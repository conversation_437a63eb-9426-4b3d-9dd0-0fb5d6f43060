package de.finacte.epc.service.calculation.renovation.recommendationStrategy.groundFloorInsulation

import de.finacte.epc.entity.renovation.RenovationMeasureType
import de.finacte.epc.entity.renovation.RenovationMeasureValue
import de.finacte.epc.entity.renovation.RenovationMeasureVariant
import de.finacte.epc.entity.renovation.RenovationTemplateEntity
import de.finacte.epc.service.calculation.buildingCalculationInput.BuildingCalculationInput
import de.finacte.epc.service.calculation.renovation.recommendationStrategy.RenovationRecommendationStrategy
import org.springframework.stereotype.Service
import java.time.Year

@Service
class GroundFloorInsulationAdvancedRenovationRecommendationStrategy : RenovationRecommendationStrategy() {

    override fun isRecommendedStrategy(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity,
    ): Boolean {
        if (buildingCalculationInput.groundFloorInsulation == null) {
            return true
        }
        return buildingCalculationInput.groundFloorInsulation.groundFloorInsulationYear + 15 < Year.now().value
    }

    override fun isApplicable(
        buildingCalculationInput: BuildingCalculationInput,
        renovationTemplate: RenovationTemplateEntity
    ): Boolean = !buildingCalculationInput.basementHeating.basementHeated

    override fun getRenovatedElementKeyForStrategy(): Triple<RenovationMeasureType, RenovationMeasureValue, RenovationMeasureVariant> =
        Triple(
            RenovationMeasureType.GROUND_FLOOR_INSULATION,
            RenovationMeasureValue.AEROGEL_4_CM_2025,
            RenovationMeasureVariant.ADVANCED
        )
}
