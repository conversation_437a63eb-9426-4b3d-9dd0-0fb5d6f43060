{"core": {"score": {"A_PLUS": "A+", "A": "A", "B": "B", "C": "C", "D": "D", "E": "E", "F": "F", "G": "G", "H": "H"}, "boolean": {"false": "No", "true": "Yes"}, "validation": {"required": "This information is required", "range": "Value must be in range {{min}} - {{max}}"}, "units": {"euro": "EUR", "energyDemand": "kWh/m² p.a.", "emission": {"KG": "kg CO2 p.a.", "KG_M2": "kg CO2/m² p.a."}, "energyPerAnnum": "kWh p.a.", "energyPeak": "kWp"}}, "interceptors": {"errors": {"401": {"body": "Your user session has expired or you are trying to access restricted resources.", "title": "You can try reloading the page"}}}, "pdf": {"notAvailable": "N/A", "currentEnergyEfficiency": "Current energy efficiency", "newEnergyEfficiency": "New energy efficiency", "selectedMeasures": "Selected measures", "measure": "Measure", "noMeasuresSelected": "No measures selected", "moreRenovations": "more renovation measures", "grant": "Potential grant program", "price": "Renovation cost (EUR)", "profitability": "Profitability", "energyIndex": "Energy Index", "totalProfit": "Total Profit", "energyCostSaved": "Energy cost saved over 10 years", "propertyValueIncrease": "Property value increase", "renovationCost": "Cost", "energyEfficiency": "Energy efficiency", "finalEnergyDemandPerArea": "Final energy demand", "co2Emission": "CO2 emission", "solarEnergyProd": "Solar power production", "buildingOverview": "Building info", "building": {"common": {"sameAsConstructionYear": "Same as construction year", "notProvided": "Not provided", "na": "Not available"}, "info": {"title": "Building Information", "shape": "Building shape", "position": "Building position", "constructionYear": "Construction year", "area": "Area", "floors": "No. of floors", "floorHeight": "Floor height", "tenants": "No. of tenants"}, "basement": {"title": "Basement", "type": "Basement type", "basementInsulationYear": "Basement insulation year", "basementInsulated": "Basement insulated", "groundFloorInsulationYear": "Ground floor insulation year", "groundFloorInsulated": "Ground floor insulated"}, "heating": {"title": "Heating", "primaryHeating": "Heating type", "primaryHeatingInstallationYear": "Installation year", "waterHeating": "Water heating type", "floorHeating": "Floor heating", "solarHeating": "Solar thermal plant in place"}, "facade": {"title": "Exterior Walls", "insulated": "Insulated walls", "insulationYear": "Insulation year"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Roof floor type", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "Pitched roof, liveable floor with reduced height", "FULLY_LIVEABLE_ELEVATED": "Pitched roof, liveable floor with full height", "PARTIALLY_LIVEABLE": "Pitched roof", "COLD_STORAGE": "Pitched roof, no liveable roof floor"}, "ceilingOrRoofInsulated": "Is the ceiling or roof insulated?", "insulationYear": "Insulation year", "hasSolarPlant": "Installed solar plant", "solarPlantPower": "Solar plant power", "solarPlantInstallationYear": "Solar plant installation year", "eligibleForSolar": "Roof eligible for solar"}, "windows": {"title": "Windows", "windowsToWallRatio": "Windows to wall proportion", "windowsToWallRatioOptions": {"LOW": "Few window surfaces", "MEDIUM": "Average-sized window surfaces", "HIGH": "Many windows plus floor-to-ceiling windows, across the facade windows"}, "glazing": "Insulation", "installationYear": "Installation year", "shutters": "Shutters type"}, "electricalEquipment": {"title": "Electrical equipment", "type": "Equipment type", "acInstallationYear": "Air conditioning installation year"}}}, "integration": {"europace": {"BaufiSmart": "BaufiSmart", "caseId": "Case ID", "sent": "<PERSON><PERSON>", "notSent": "Not sent", "integrationTime": "at {{date}}", "cta": {"send": "Send", "update": "Update"}, "errors": {"noLicenseTitle": "Integration setup required", "noLicenseMsgPrefix": "You have no BaufiSmart integration yet. Contact", "noLicenseMsgPostfix": ", to integrate.", "close": "Close"}}}, "dataAccess": {"buildingShape": {"COMPACT": "Compact", "COMPLEX": "Complex (U, S)", "L_SHAPE": "L-shaped"}, "buildingPosition": {"STAND_ALONE": "Stand-alone", "TWIN_HOUSE": "Double house", "ROW_MIDDLE": "Row-middle", "ROW_END": "Row-end"}, "buildingFloorHeight": {"REGULAR": "Regular (~2,5m)", "HIGH": "High (~3,4m)"}, "buildingBasement": {"NO_BASEMENT": "No basement", "UNHEATED": "Unheated basement", "HEATED": "Fully heated basement"}, "buildingHeating": {"GAS_BOILER": "Gas boiler", "OIL_BOILER": "Oil boiler", "BIOMASS_BOILER": "Biomass boiler", "ELECTRIC": "Electric heating", "HEAT_PUMP": "Heat pump", "NIGHT_STORAGE": "Night storage", "DISTRICT": "District heating"}, "buildingRoofSlope": {"PITCHED": "Pitched roof", "FLAT": "Flat roof"}, "buildingRoofFloor": {"FLAT_ROOF": "Flat roof", "PARTIALLY_LIVEABLE": "Partially liveable", "FULLY_LIVEABLE_REDUCED": "Fully liveable roof", "FULLY_LIVEABLE_ELEVATED": "Fully liveable roof", "COLD_STORAGE": "Cold storage"}, "buildingWindowsToWallRatio": {"LOW": "Low", "MEDIUM": "Medium", "HIGH": "High"}, "buildingWindowsGlazing": {"SINGLE": "Single glazing", "DOUBLE": "Double glazing", "TRIPLE": "Triple glazing"}, "buildingWindowsShutters": {"MANUAL": "Manual", "ELECTRIC": "Electric", "NONE": "None"}, "buildingEquipment": {"VENTILATION_HEAT_EXCHANGE": "Central ventilation system with heat exchange", "AC": "Air conditioning"}, "renovationMeasureQuality": {"BASIC": "Cost efficient", "ADVANCED": "High quality"}}, "appShell": {"loggedInAs": "Logged in as", "selectLanguage": "Select language", "logout": "Logout", "pageError": "Something went wrong", "pageErrorDesc": "Page couldn’t be loaded", "pageErrorHint": "Please check that the page URL is correct and try reloading. If the issue persists, contact us at", "pageErrorCta": "Go back", "pageErrorToggle": "Show error details"}, "pages": {"calculationResult": {"categories": {"common": {"na": "Not available", "range": "{{min}} - {{max}} EUR", "estimatedCost": "Estimated cost", "possibleGrants": "Possible grants", "noMeasuresSelected": "No measures selected", "recommended": "Recommended"}, "buildingRenovation": {"title": "Building renovation", "facade": {"claddingLabel": "Apply exterior cladding to the building"}}}, "cta": {"exportPdf": "Export PDF", "editBuildingData": "Edit building data", "applyRenovations": "Apply renovations", "calculating": "Calculating ...", "sendToBaufinex": "Send to Baufinex"}, "results": {"profitability": "Profitability", "energyIndex": "Energy Index", "totalProfit": "Total Profit", "energyCostSaved": "Energy cost saved over 10 years", "propertyValueIncrease": "Property value increase", "renovationCost": "Cost", "energyEfficiency": "Energy efficiency", "finalEnergyDemandPerArea": "Final energy demand", "co2Emission": "CO2 emission", "solarEnergyProd": "Solar power production"}, "energyEfficiency": {"current": "Current {{value}}", "new": "New {{value}}"}, "measures": {"title": "Recommended measures", "table": {"measures": "Measures", "grant": "Potential Grant Programs", "cost": "Approx. cost", "expectedValue": "EV", "recommended": "Rec.", "na": "Not available"}, "summary": {"selected": "Selected measures", "possible": "Potential measures"}}}, "calculationList": {"title": "Calculations", "cta": {"newCalculation": "New Calculation"}, "searchPlaceholder": "Search", "table": {"date": "Date", "address": "Address", "efficiency": "Efficiency", "efficiencyUnit": "kWh/m²/year", "score": "Score"}}, "buildingForm": {"cta": {"continue": "Continue", "calculating": "Calculating ..."}, "formErrors": {"roofFloorReqFloors": "Current roof floor configuration requires min {{min}} floors", "areaTooSmallForConfiguration": "The area is too small for current floors, and roof type configuration. It should be at least {{min}}"}, "buildingInfo": {"title": "Building Information", "shapeQue": "What is the shape of the building?", "shapeDsc": "Consider the heated part only", "positionQue": "What is position of the building?", "streetQue": "Address and street number", "streetPlaceholder": "Street", "noPlaceholder": "No.", "zipCodeQue": "ZIP code and city", "zipPlaceholder": "ZIP", "cityPlaceholder": "City", "constructionYearQue": "Construction year", "constructionYearPlaceholder": "Enter year", "areaQue": "What is the area of the building?", "areaPlaceholder": "Enter area", "floorsQue": "How many floors are there?", "floorDsc": "Provide all the floors that have at least 2m height on the entire floor area, excluding basement", "floorHeightQue": "What is the height of the floor?", "tenantsQue": "How many tenants live in the building?"}, "basement": {"title": "Basement", "typeQue": "What type of basement does the building have?", "basementInsulatedQue": "Is the basement insulated?", "groundFloorInsulatedQue": "Is the building floor insulated?", "basementInsulationYearQue": "Basement insulation year", "groundFloorInsulationYearQue": "Ground floor insulation year", "insulationYearDsc": "If no value is provided, the building's construction year will be considered as the insulation year", "insulationYearPlaceholder": "Enter year"}, "heating": {"title": "Heating", "primaryHeatingQue": "What type of heating does the building have?", "primaryHeatingInstallationYearQue": "Heating installation year", "primaryHeatingInstallationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "primaryHeatingInstallationYearPlaceholder": "Enter year", "isWaterHeatingDifferent": "Is water heating different from building heating?", "waterHeatingQue": "Select water heating type", "floorHeatingQue": "Is there a floor heating?", "solarHeatingQue": "Is there a solar thermal plant installed?"}, "facade": {"title": "Exterior Walls", "insulatedQue": "Have the exterior walls been insulated?", "insulationYearQue": "Walls insulation year", "insulationYearDsc": "If no value is provided, the building's construction year will be considered as the insulation year", "insulationYearPlaceholder": "Enter year"}, "roof": {"title": "<PERSON><PERSON>", "floorQue": "What type of the roof floor does the building have?", "floorOptionsSublabels": {"FULLY_LIVEABLE_REDUCED": "floor with reduced height", "FULLY_LIVEABLE_ELEVATED": "floor with full height"}, "floorOptionsDescriptions": {"FULLY_LIVEABLE_REDUCED": "The knee wall height is 1-2m", "FULLY_LIVEABLE_ELEVATED": "The knee wall height is 2m +"}, "ceilingOrRoofInsulatedQue": "Is the ceiling or roof insulated?", "insulationYearQue": "Insulation year", "insulationYearDsc": "If no value is provided, the building's construction year will be considered as the insulation year", "insulationYearPlaceholder": "Enter year", "hasSolarPlantQue": "Is there already a solar plant installed", "solarPlantPlaceholder": "Enter power", "solarPlantPlaceholderDsc": "Provide the nominal power for the solar plant", "solarPlantInstallationYearQue": "Solar plant installation year", "solarPlantInstallationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "solarPlantInstallationYearPlaceholder": "Enter year", "eligibleForSolarQue": "Does the roof seem eligible for solar?"}, "windows": {"title": "Windows", "windowsToWallRatioQue": "What is the size of the window surface area in the building?", "windowsToWallRatioDsc": {"LOW": "Few window surfaces", "MEDIUM": "Average-sized window surfaces", "HIGH": "Many windows plus floor-to-ceiling windows, across the facade windows"}, "glazingQue": "What is the glazing type of the windows?", "installationYearQue": "Windows installation year", "installationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "installationYearPlaceholder": "Enter year", "shuttersQue": "What type of shutters do the windows have?"}, "electricalEquipment": {"title": "Electrical equipment", "typeQue": "What type of other electrical equipment does the building have?", "acInstallationYearQue": "Air conditioning installation year", "acInstallationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "acInstallationYearPlaceholder": "Enter year"}}, "calculationReview": {"tabs": {"RESULTS": "Results", "BUILDING_INFO": "Building Info"}, "buildingReview": {"common": {"sameAsConstructionYear": "Same as construction year", "notProvided": "Not provided", "na": "Not available"}, "buildingInfo": {"title": "Building Information", "shape": "Building shape", "position": "Building position", "constructionYear": "Construction year", "area": "Area", "floors": "No. of floors", "floorHeight": "Floor height", "tenants": "No. of tenants"}, "basement": {"title": "Basement", "type": "Basement type", "basementInsulationYear": "Basement insulation year", "basementInsulated": "Basement insulated", "groundFloorInsulated": "Ground floor insulated", "groundFloorInsulationYear": "Ground floor insulation year"}, "heating": {"title": "Heating", "primaryHeating": "Heating type", "primaryHeatingInstallationYear": "Installation year", "waterHeating": "Water heating type", "floorHeating": "Floor heating", "solarHeating": "Solar thermal plant in place"}, "facade": {"title": "Exterior Walls", "insulated": "Insulated walls", "insulationYear": "Insulation year"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Roof floor type", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "Pitched roof, liveable floor with reduced height", "FULLY_LIVEABLE_ELEVATED": "Pitched roof, liveable floor with full height", "PARTIALLY_LIVEABLE": "Pitched roof", "COLD_STORAGE": "Pitched roof, no liveable roof floor"}, "ceilingOrRoofInsulated": "Ceiling or roof insulated", "insulationYear": "Insulation year", "hasSolarPlant": "Installed solar plant", "solarPlantInstallationYear": "Solar plant installation year", "solarPlantPower": "Solar plant power", "eligibleForSolar": "Roof eligible for solar"}, "windows": {"title": "Windows", "windowsToWallRatio": "Windows to wall proportion", "windowsToWallRatioOptions": {"LOW": "Few window surfaces", "MEDIUM": "Average-sized window surfaces", "HIGH": "Many windows plus floor-to-ceiling windows, across the facade windows"}, "glazing": "Insulation", "installationYear": "Installation year", "shutters": "Shutters type"}, "electricalEquipment": {"title": "Electrical equipment", "type": "Equipment type", "acInstallationYear": "Air conditioning installation year"}}, "cta": {"recalculate": "Re-run calculation", "exportToPdf": "Export to PDF"}}}}