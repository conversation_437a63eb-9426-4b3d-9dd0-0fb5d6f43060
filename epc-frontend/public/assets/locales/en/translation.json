{"core": {"score": {"A_PLUS": "A+", "A": "A", "B": "B", "C": "C", "D": "D", "E": "E", "F": "F", "G": "G", "H": "H"}, "boolean": {"false": "No", "true": "Yes"}, "validation": {"required": "This information is required", "range": "Value must be in range {{min}} - {{max}}"}, "units": {"euro": "EUR", "energyDemand": "kWh/m² p.a.", "emission": {"KG": "kg CO2 p.a.", "KG_M2": "kg CO2/m² p.a."}, "energyPerAnnum": "kWh p.a.", "energyPeak": "kWp"}}, "interceptors": {"errors": {"401": {"body": "Your user session has expired or you are trying to access restricted resources.", "title": "You can try reloading the page"}, "0": {"title": "Connection error", "body": ""}}, "axiosInternalErrors": {"ECONNABORTED": {"common": {"title": "The request takes longer than expected", "body": "Please wait a minute and refresh the page"}, "cost": {"title": "The cost query takes longer than expected", "body": "Please wait a minute and refresh the page"}}}}, "pdf": {"notAvailable": "N/A", "currentPrimaryEnergyDemand": "Current energy efficiency", "alternativePrimaryEnergyDemand": "New energy efficiency", "selectedMeasures": "Selected measures", "measure": "Measure", "noMeasuresSelected": "No measures selected", "moreRenovations": "more renovation measures", "grant": "Potential grant program", "price": "Renovation cost (EUR)", "profitability": "Profitability", "energyIndex": "Energy Index", "totalProfit": "Total Profit", "energyCostSaved": "Energy cost saved over 10 years", "propertyValueIncrease": "Property value increase", "renovationCost": "Cost", "primaryEnergyDemand": "Primary energy demand", "finalEnergyDemandPerArea": "Final energy demand", "co2Emission": "CO2 emission", "solarEnergyProd": "Solar power production", "buildingOverview": "Building info", "building": {"common": {"sameAsConstructionYear": "Same as construction year", "notProvided": "Not provided", "na": "Not available"}, "info": {"title": "Building Information", "shape": "Building shape", "position": "Building position", "constructionYear": "Construction year", "area": "Area", "floors": "No. of floors", "floorHeight": "Floor height", "tenants": "No. of tenants"}, "basement": {"title": "Basement", "type": "Basement type", "basementInsulationYear": "Basement insulation year", "basementInsulated": "Basement insulated", "groundFloorInsulationYear": "Ground floor insulation year", "groundFloorInsulated": "Ground floor insulated"}, "heating": {"title": "Heating", "primaryHeating": "Heating type", "primaryHeatingInstallationYear": "Installation year", "waterHeating": "Water heating type", "floorHeating": "Floor heating", "solarHeating": "Solar thermal plant in place"}, "facade": {"title": "Exterior Walls", "insulated": "Insulated walls", "insulationYear": "Insulation year"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Roof floor type", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "Pitched roof, liveable floor with reduced height", "FULLY_LIVEABLE_ELEVATED": "Pitched roof, liveable floor with full height", "PARTIALLY_LIVEABLE": "Pitched roof", "COLD_STORAGE": "Pitched roof, no liveable roof floor"}, "ceilingOrRoofInsulated": "Is the ceiling or roof insulated?", "insulationYear": "Insulation year", "hasSolarPlant": "Installed solar plant", "solarPlantPower": "Solar plant power", "solarPlantInstallationYear": "Solar plant installation year", "eligibleForSolar": "Roof eligible for solar"}, "windows": {"title": "Windows", "windowsToWallRatio": "Windows to wall proportion", "windowsToWallRatioOptions": {"LOW": "Few window surfaces", "MEDIUM": "Average-sized window surfaces", "HIGH": "Many windows plus floor-to-ceiling windows, across the facade windows"}, "glazing": "Insulation", "installationYear": "Installation year", "shutters": "Shutters type"}, "electricalEquipment": {"title": "Electrical equipment", "type": "Equipment type", "acInstallationYear": "Air conditioning installation year"}}, "measures": {"FACADE_EXTERNAL_WALL_INSULATION": {"label": "External wall insulation"}, "FACADE_EXTERIOR_CLADDING": {"label": "Apply exterior cladding to the building"}, "ROOF_INSULATION": {"label": "Roof insulation"}, "ROOF_NEW_COVERAGE": {"label": "New roof coverage"}, "ROOF_RE_ENFORCEMENT": {"label": "Roof reinforcement"}, "CEILING_INSULATION": {"label": "Ceiling insulation"}, "WINDOWS_GLAZING_TYPE": {"label": "Windows glazing type"}, "WINDOWS_FRAME_MATERIAL_UPGRADE": {"label": "Upgrade windows frame material"}, "WINDOWS_SHUTTERS": {"label": "Windows shutters"}, "WINDOWS_SECURITY_FEATURES": {"label": "Install windows security features"}, "GROUND_FLOOR_INSULATION": {"label": "Ground floor insulation"}, "GROUND_FLOOR_MOISTURE_PROTECTION": {"label": "Apply ground floor moisture protection"}, "BASEMENT_INSULATION": {"label": "Basement insulation"}, "BASEMENT_MOISTURE_PROTECTION": {"label": "Apply basement moisture protection"}, "DOORS_REPLACEMENT": {"label": "Doors replacement"}, "DOORS_SECURITY_FEATURES": {"label": "Door security features"}, "HEATING_PRIMARY_SOURCE": {"label": "Primary heating source", "values": {"DISTRICT_HEATING_2025": "District heating", "BIOMASS_HEATING_2025": "Biomass heating", "AIR_TO_WATER_HEAT_PUMP_2025": "Air-to-water heat pump", "GROUND_TO_WATER_HEAT_PUMP_2025": "Ground-to-water heat pump"}}, "HEATING_VENTILATION_HEAT_EXCHANGE": {"label": "Ventilation with heat exchange"}, "UNDERFLOOR_HEATING": {"label": "Underfloor heating", "values": {"WET_SYSTEM_WITH_SCREED": "Wet system incl. floor screed and flooring", "DRY_SYSTEM_WITH_FLOORING": "Dry system incl. new flooring", "DRY_SYSTEM_WITHOUT_FLOORING": "Dry system excl. new flooring"}}, "WALL_HEATING": {"label": "Wall heating", "values": {"WET_SYSTEM_WITH_RECONSTRUCTION": "Wet system incl. wall & paint reconstruction", "DRY_SYSTEM_WITH_RECONSTRUCTION": "Dry system incl. wall & paint reconstruction"}}, "CEILING_HEATING": {"label": "Ceiling heating", "values": {"DRY_SYSTEM_WITH_RECONSTRUCTION": "Dry system incl. ceiling & paint reconstruction"}}, "BASIC_EFFICIENCY": {"label": "Basic efficiency"}, "HIGH_PERFORMANCE": {"label": "High performance"}, "SOLAR_PANELS": {"label": "Solar panels"}, "SOLAR_PLANT_PANELS": {"label": "Solar power plant panels"}, "SOLAR_THERMAL": {"label": "Solar thermal system"}, "BATTERY_STORAGE": {"label": "Battery storage", "values": {"LITHIUM_ION_BATTERY": "Lithium-ion battery"}}, "IMMERSION_HEATER": {"label": "Immersion heater", "values": {"WITH_BUFFER": "With new hot water tank", "WITHOUT_BUFFER": "Without new hot water tank"}}, "INTELLIGENT_HEATING_CONTROLS": {"label": "Intelligent heating controls"}, "LIGHTING_AUTOMATION": {"label": "Lighting automation"}, "SENSORS": {"label": "Sensors"}, "WALLBOX_INSTALLATION": {"label": "Wallbox installation"}, "GRID_CONNECTION_UPGRADE": {"label": "Grid connection upgrade"}, "BARRIER_FREE_BATHROOM": {"label": "Barrier-free bathroom"}, "STAIRLIFT": {"label": "Stairlift"}, "WIDENING_DOORS": {"label": "Widening doors"}, "EMERGENCY_SYSTEMS": {"label": "Emergency systems"}}, "categories": {"FACADE": "Facade", "ROOF": "<PERSON><PERSON>", "WINDOWS": "Windows", "BASEMENT": "Basement", "DOOR": "Door", "HEATING_SYSTEM": "Heating system", "RADIATOR_UPGRADE": "Radiators upgrade", "SURFACE_HEATING": "Surface heating", "SOLAR_PANELS": "Solar panels", "SMART_HOME_ENERGY_MANAGEMENT": "Smart home energy management", "ELECTRIC_VEHICLE_CHARGING": "Electric vehicle charging", "AGE_APPROPRIATE_LIVING": "Age-appropriate living"}}, "integration": {"europace": {"BaufiSmart": "BaufiSmart", "caseId": "Case ID", "sent": "<PERSON><PERSON>", "notSent": "Not sent", "integrationTime": "at {{date}}", "cta": {"send": "Send", "update": "Update"}, "errors": {"noLicenseTitle": "Integration setup required", "noLicenseMsgPrefix": "You have no BaufiSmart integration yet. Contact", "noLicenseMsgPostfix": ", to integrate.", "close": "Close"}, "caseType": {"placeholder": "--Select--", "purchase": "Purchase", "modernization": "Modernization", "prolongation": "Prolongation"}}}, "dataAccess": {"grants": {"KFW_261": {"name": "KfW 261", "headline": "Federal Funding for Efficient Buildings (BEG)", "description": "subsidized loan + grant up to 45%"}, "KFW_430": {"name": "KfW 430", "headline": "Grant for Individual Measures (BEG EM)", "description": "Grant for door replacement as a single measure"}, "KFW_270": {"name": "KfW 270", "headline": "Renewable Energy Standard", "description": "subsidized loan"}, "KFW_159": {"name": "KfW 159", "headline": "Age-appropriate renovation – loan", "description": "subsidized loan up to EUR 50.000"}}, "buildingShape": {"COMPACT": "Compact", "COMPLEX": "Complex (U, S)", "L_SHAPE": "L-shaped"}, "buildingPosition": {"STAND_ALONE": "Stand-alone", "TWIN_HOUSE": "Double house", "ROW_MIDDLE": "Row-middle", "ROW_END": "Row-end"}, "buildingFloorHeight": {"REGULAR": "Regular (~2,5m)", "HIGH": "High (~3,4m)"}, "buildingBasement": {"NO_BASEMENT": "No basement", "UNHEATED": "Unheated basement", "HEATED": "Fully heated basement"}, "buildingHeating": {"GAS_BOILER": "Gas boiler", "OIL_BOILER": "Oil boiler", "BIOMASS_BOILER": "Biomass boiler", "ELECTRIC": "Electric heating", "HEAT_PUMP": "Heat pump", "NIGHT_STORAGE": "Night storage", "DISTRICT": "District heating"}, "buildingRoofSlope": {"PITCHED": "Pitched roof", "FLAT": "Flat roof"}, "buildingRoofFloor": {"FLAT_ROOF": "Flat roof", "PARTIALLY_LIVEABLE": "Partially liveable", "FULLY_LIVEABLE_REDUCED": "Fully liveable roof", "FULLY_LIVEABLE_ELEVATED": "Fully liveable roof", "COLD_STORAGE": "Cold storage"}, "buildingWindowsToWallRatio": {"LOW": "Low", "MEDIUM": "Medium", "HIGH": "High"}, "buildingWindowsGlazing": {"SINGLE": "Single glazing", "DOUBLE": "Double glazing", "TRIPLE": "Triple glazing"}, "buildingWindowsShutters": {"MANUAL": "Manual", "ELECTRIC": "Electric", "NONE": "None"}, "buildingEquipment": {"VENTILATION_HEAT_EXCHANGE": "Central ventilation system with heat exchange", "AC": "Air conditioning"}, "renovationMeasureVariant": {"BASIC": "Cost efficient", "ADVANCED": "High quality", "DEFAULT": "<PERSON><PERSON><PERSON>"}}, "appShell": {"loggedInAs": "Logged in as", "selectLanguage": "Select language", "logout": "Logout", "pageError": "Something went wrong", "pageErrorDesc": "Page couldn't be loaded", "pageErrorHint": "Please check that the page URL is correct and try reloading. If the issue persists, contact us at", "pageErrorCta": "Go back", "pageErrorToggle": "Show error details"}, "pages": {"calculationResult": {"categories": {"summary": {"totalCost": "Current total renovation cost", "notApplied": "Apply renovations to see the new results", "applyToSeeTheChanges": "First apply new renovations to see the changes in the results ", "applied": "Energy score is up to date", "apply": "Apply renovations", "showSelectedMeasures": "Show all selected measures", "hideSelectedMeasures": "<PERSON><PERSON> selected measures", "noMeasuresSelected": "No measures were selected"}, "common": {"na": "Not available", "naRange": "Range not available", "range": "{{min}} - {{max}} EUR", "estimatedCost": "Estimated cost", "possibleGrants": "Possible grants", "noMeasuresSelected": "No measures selected", "recommended": "Recommended", "showGrants": "Show grants", "hideGrants": "Hide grants", "installationType": "Installation type", "selectedMeasures": "Selected measures", "reviewAndApplyRenovations": "Review and apply renovations", "forExample": "e.g. {{example}}"}, "texts": {"FACADE": {"label": "Facade", "measures": {"FACADE_EXTERNAL_WALL_INSULATION": {"label": "External wall insulation", "badge": "Wall insulation", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Modern Expanded Polystyrene", "desc": "e.g. modern expanded polystyrene"}, "MINERAL_WOOL_10_CM_2025": {"label": "Modern Mineral Wool", "desc": "e.g. modern mineral wool"}, "WOOD_FIBER_15_CM_2025": {"label": "Modern Wood Fiber", "desc": "e.g. modern wood fiber"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Modern Extruded Polystyrene", "desc": "e.g. modern extruded polystyrene"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "AEROGEL_4_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "AEROGEL_1_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Modern Fiberglass Insulation", "desc": "e.g. modern fiberglass insulation"}}}, "FACADE_EXTERIOR_CLADDING": {"label": "Apply exterior cladding to the building", "badge": "Cladding", "values": {"DEFAULT": {"label": "", "desc": "e.g. ventilated wooden or fiber cement cladding, brick veneer, slate cladding"}}}}}, "ROOF": {"label": "<PERSON><PERSON>", "measures": {"CEILING_INSULATION": {"label": "Ceiling insulation", "badge": "Insulation", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Modern Expanded Polystyrene", "desc": "e.g. modern expanded polystyrene"}, "MINERAL_WOOL_10_CM_2025": {"label": "Modern Mineral Wool", "desc": "e.g. modern mineral wool"}, "WOOD_FIBER_15_CM_2025": {"label": "Modern Wood Fiber", "desc": "e.g. modern wood fiber"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Modern Extruded Polystyrene", "desc": "e.g. modern extruded polystyrene"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "AEROGEL_4_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "AEROGEL_1_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Modern Fiberglass Insulation", "desc": "e.g. modern fiberglass insulation"}}}, "ROOF_INSULATION": {"label": "Roof insulation", "badge": "Insulation", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Modern Expanded Polystyrene", "desc": "e.g. modern expanded polystyrene"}, "MINERAL_WOOL_10_CM_2025": {"label": "Modern Mineral Wool", "desc": "e.g. modern mineral wool"}, "WOOD_FIBER_15_CM_2025": {"label": "Modern Wood Fiber", "desc": "e.g. modern wood fiber"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Modern Extruded Polystyrene", "desc": "e.g. modern extruded polystyrene"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "AEROGEL_4_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "AEROGEL_1_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Modern Fiberglass Insulation", "desc": "e.g. modern fiberglass insulation"}}}, "ROOF_NEW_COVERAGE": {"label": "New roof coverage", "badge": "Coverage", "values": {"CLAY_TILES": {"label": "<PERSON>", "desc": "e.g. clay tiles"}, "NATURAL_SLATE": {"label": "Natural Slate", "desc": "e.g. natural slate"}, "CONCRETE_TILES": {"label": "Concrete <PERSON>", "desc": "e.g. concrete tiles"}, "METAL_TILES": {"label": "Metal Tiles", "desc": "e.g. metal tiles"}, "BITUMEN_MEMBRANE": {"label": "Bitumen Membrane", "desc": "e.g. bitumen membrane"}, "HIGH_QUALITY_PLASTIC_FOIL": {"label": "High-Quality Plastic Foil", "desc": "e.g. high-quality plastic foil"}}}, "ROOF_RE_ENFORCEMENT": {"label": "Roof reinforcement", "badge": "Reinforcement", "values": {"DEFAULT": {"label": "", "desc": "e.g. modification/reinforcement of the roof structure"}}}}}, "WINDOWS": {"label": "Windows", "measures": {"WINDOWS_GLAZING_TYPE": {"label": "Window glazing type", "badge": "Glazing", "values": {"DOUBLE_GLAZING": {"label": "", "desc": "double glazing"}, "TRIPLE_GLAZING": {"label": "", "desc": "triple glazing"}}}, "WINDOWS_SHUTTERS": {"label": "Window shutters", "badge": "Shutters", "values": {"MANUAL_SHUTTERS": {"label": "high-quality manual shutters", "desc": "high-quality manual shutters"}, "ELECTRIC_SHUTTERS": {"label": "high-quality electric shutters", "desc": "high-quality electric shutters"}}}, "WINDOWS_SECURITY_FEATURES": {"label": "Install windows security features", "badge": "Security features", "values": {"DEFAULT": {"label": "", "desc": "e.g. mushroom head locking system or lockable window handles"}}}}}, "BASEMENT": {"label": "Basement", "measures": {"GROUND_FLOOR_INSULATION": {"label": "Ground floor insulation", "badge": "Insulation", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Modern Expanded Polystyrene", "desc": "e.g. modern expanded polystyrene"}, "MINERAL_WOOL_10_CM_2025": {"label": "Modern Mineral Wool", "desc": "e.g. modern mineral wool"}, "WOOD_FIBER_15_CM_2025": {"label": "Modern Wood Fiber", "desc": "e.g. modern wood fiber"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Modern Extruded Polystyrene", "desc": "e.g. modern extruded polystyrene"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "AEROGEL_4_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "AEROGEL_1_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Modern Fiberglass Insulation", "desc": "e.g. modern fiberglass insulation"}}}, "BASEMENT_INSULATION": {"label": "Basement insulation", "badge": "Insulation", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Modern Expanded Polystyrene", "desc": "e.g. modern expanded polystyrene"}, "MINERAL_WOOL_10_CM_2025": {"label": "Modern Mineral Wool", "desc": "e.g. modern mineral wool"}, "WOOD_FIBER_15_CM_2025": {"label": "Modern Wood Fiber", "desc": "e.g. modern wood fiber"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Modern Extruded Polystyrene", "desc": "e.g. modern extruded polystyrene"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Modern Vacuum Insulated Panel", "desc": "e.g. modern vacuum insulated panel"}, "AEROGEL_4_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "AEROGEL_1_CM_2025": {"label": "Modern Aerogel", "desc": "e.g. modern aerogel"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Modern Fiberglass Insulation", "desc": "e.g. modern fiberglass insulation"}}}, "BASEMENT_MOISTURE_PROTECTION": {"label": "Apply basement moisture protection", "badge": "Moisture protection", "values": {"DEFAULT": {"label": "", "desc": "e.g. thick bitumen coating or horizontal waterproofing barrier"}}}}}, "DOOR": {"label": "Door", "measures": {"DOORS_REPLACEMENT": {"label": "Doors replacement", "badge": "Doors replacement", "values": {"DEFAULT": {"label": "", "desc": "e.g. thermally insulated front door with multi-point locking system (at least CR2)"}}}, "DOORS_SECURITY_FEATURES": {"label": "Install doors security features", "badge": "Security features", "values": {"DEFAULT": {"label": "", "desc": "e.g. change of cylinder system and addition of crossbar or vertical lock"}}}}}, "HEATING_SYSTEM": {"label": "Heating system", "measures": {"HEATING_PRIMARY_SOURCE": {"label": "Primary heating source", "badge": "Heating", "values": {"DISTRICT_HEATING_2025": {"label": "District heating", "desc": "connection to local district heating network with transfer station", "badge": "District heating"}, "GROUND_TO_WATER_HEAT_PUMP_2025": {"label": "Ground-to-water heat pump", "desc": "geothermal borehole and heat pump with brine circuit", "badge": "Ground-to-water heat pump"}, "BIOMASS_HEATING_2025": {"label": "Biomass heating", "desc": "pellet boiler with automatic feed, solid fuel boiler", "badge": "Biomass heating"}, "AIR_TO_WATER_HEAT_PUMP_2025": {"label": "Air-to-water heat pump", "desc": "split unit with outdoor and indoor components / always with outdoor unit", "badge": "Air-to-water heat pump"}}}, "HEATING_VENTILATION_HEAT_EXCHANGE": {"label": "Ventilation with heat exchange", "badge": "Ventilation with heat exchange", "values": {"HEAT_EXCHANGER": {"label": "", "desc": "centralized ventilation system with heat exchanger"}}}}}, "SURFACE_HEATING": {"label": "Surface heating", "measures": {"UNDERFLOOR_HEATING": {"label": "Floor heating", "badge": "Floor heating", "values": {"WET_SYSTEM_WITH_SCREED": {"label": "Wet system incl. floor screed and flooring", "desc": "e.g. water-based pipe system in cement screed with new parquet or tiles", "badge": "Wet system incl. floor screed and flooring"}, "DRY_SYSTEM_WITH_FLOORING": {"label": "Dry system incl. new flooring", "desc": "e.g. dry construction elements with pipe channels and new prefinished parquet", "badge": "Dry system incl. new flooring"}, "DRY_SYSTEM_WITHOUT_FLOORING": {"label": "Dry systems excl. new flooring", "desc": "e.g. installation underneath existing flooring", "badge": "Dry system excl. new flooring"}}}, "WALL_HEATING": {"label": "Wall heating", "badge": "Wall heating", "values": {"WET_SYSTEM_WITH_RECONSTRUCTION": {"label": "Wet system incl. reconstruction", "desc": "e.g. embedded pipe register on structural interior wall with plaster finish", "badge": "Wet system incl. reconstruction"}, "DRY_SYSTEM_WITH_RECONSTRUCTION": {"label": "Dry system incl. reconstruction", "desc": "e.g. drywall panels with integrated pipe system and wall setup", "badge": "Dry system incl. reconstruction"}}}, "CEILING_HEATING": {"label": "Ceiling heating", "badge": "Ceiling heating", "values": {"DRY_SYSTEM_WITH_RECONSTRUCTION": {"label": "Dry system incl. reconstruction", "desc": "e.g. heating panels in suspended ceiling with adjusted ceiling construction", "badge": "Dry system incl. reconstruction"}}}}}, "RADIATOR_UPGRADE": {"label": "Radiators upgrade", "measures": {"BASIC_EFFICIENCY": {"label": "Basic efficiency", "badge": "Basic efficiency", "values": {"DEFAULT": {"label": "", "desc": "e.g. replacement with modern flat radiators"}}}, "HIGH_PERFORMANCE": {"label": "High performance", "badge": "High performance", "values": {"DEFAULT": {"label": "", "desc": "e.g. replacement with high-efficiency radiators with a large fin surface"}}}}}, "SOLAR_PANELS": {"label": "Solar panels", "measures": {"BATTERY_STORAGE": {"label": "Battery storage", "badge": "Battery storage", "values": {"LITHIUM_ION_BATTERY": {"label": "Battery storage", "desc": "e.g. lithium-ion battery storage"}}}, "IMMERSION_HEATER": {"label": "Immersion heater", "badge": "Immersion heater", "values": {"WITH_BUFFER": {"badge": "With new hot water tank", "label": "With new hot water tank", "desc": "e.g. replacement of existing hot water tank with integrated electric heating rod"}, "WITHOUT_BUFFER": {"badge": "Without new hot water tank", "label": "Without new hot water tank", "desc": "e.g. electric heating rod for existing hot water storage tank"}}}, "SOLAR_PANELS": {"label": "Solar panels", "badge": "Solar panels", "values": {"DEFAULT": {"label": "Solar panels", "desc": "e.g. monocrystalline solar modules with inverter"}}}}}, "SMART_HOME_ENERGY_MANAGEMENT": {"label": "Smart home energy management", "measures": {"INTELLIGENT_HEATING_CONTROLS": {"label": "Intelligent heating controls", "badge": "Intelligent heating controls", "values": {"DEFAULT": {"label": "", "desc": "e.g. smart thermostats and central heating control incl. BUS system upgrade"}}}, "LIGHTING_AUTOMATION": {"label": "Lighting automation", "badge": "Lighting automation", "values": {"DEFAULT": {"label": "", "desc": "e.g. motion detectors or daylight-dependent lighting control"}}}}}, "ELECTRIC_VEHICLE_CHARGING": {"label": "Electric vehicle charging", "measures": {"WALLBOX_INSTALLATION": {"label": "Wallbox installation", "badge": "Wallbox installation", "values": {"DEFAULT": {"label": "", "desc": "e.g. 11 kW wallbox with Type 2 plug"}}}, "GRID_CONNECTION_UPGRADE": {"label": "Grid connection upgrade", "badge": "Grid connection upgrade", "values": {"DEFAULT": {"label": "", "desc": "e.g. increase to higher capacity (e.g. 63A) by grid operator"}}}}}, "AGE_APPROPRIATE_LIVING": {"label": "Age-appropriate living", "measures": {"BARRIER_FREE_BATHROOM": {"label": "Barrier-free bathroom", "badge": "Barrier-free bathroom", "values": {"DEFAULT": {"label": "", "desc": "e.g. walk-in shower with grab bars, toilet grab bars, accessible bathroom entrance"}}}, "STAIRLIFT": {"label": "Stairlift", "badge": "Stairlift", "values": {"DEFAULT": {"label": "", "desc": "e.g. platform or seat lift for straight staircases across all floors"}}}, "WIDENING_DOORS": {"label": "Widening doors", "badge": "Widening doors", "values": {"DEFAULT": {"label": "", "desc": "e.g. conversion from 70 cm to 90 cm door width incl. wall opening and new doors"}}}}}}, "measuresBadgesValues": {"ADVANCED": "High quality", "BASIC": "Cost efficient", "DEFAULT": "<PERSON><PERSON><PERSON>"}, "buildingRenovation": {"title": "Building renovation"}, "heatingAndVentilation": {"title": "Heating and ventilation"}, "energyAndComfort": {"title": "Energy and comfort"}}, "cta": {"exportPdf": "Export PDF", "editBuildingData": "Edit building data", "applyRenovations": "Apply renovations", "calculating": "Calculating ...", "sendToBaufinex": "Send to Baufinex"}, "results": {"profitability": "Profitability", "energyIndex": "Energy Index", "totalProfit": "Total Profit", "energyCostSaved": "Energy cost saved over 10 years", "propertyValueIncrease": "Property value increase", "renovationCost": "Cost", "primaryEnergyDemand": "Primary energy demand", "finalEnergyDemandPerArea": "Final energy demand", "co2Emission": "CO2 emission", "solarEnergyProd": "Solar power production"}, "primaryEnergyDemand": {"current": "Current {{value}}", "new": "New {{value}}"}, "measures": {"title": "Recommended measures", "table": {"measures": "Measures", "grant": "Potential Grant Programs", "cost": "Approx. cost", "expectedValue": "EV", "recommended": "Rec.", "na": "Not available"}, "summary": {"selected": "Selected measures", "possible": "Potential measures"}}}, "calculationList": {"title": "Calculations", "cta": {"newCalculation": "New Calculation"}, "searchPlaceholder": "Search", "table": {"date": "Date", "address": "Address", "efficiency": "Efficiency", "efficiencyUnit": "kWh/m²/year", "score": "Score"}}, "buildingForm": {"cta": {"continue": "Continue", "calculating": "Calculating ..."}, "formErrors": {"roofFloorReqFloors": "Current roof floor configuration requires min {{min}} floors", "areaTooSmallForConfiguration": "The area is too small for current floors, and roof type configuration. It should be at least {{min}}"}, "buildingInfo": {"title": "Building Information", "shapeQue": "What is the shape of the building?", "shapeDsc": "Consider the heated part only", "positionQue": "What is position of the building?", "streetQue": "Address and street number", "streetPlaceholder": "Street", "noPlaceholder": "No.", "zipCodeQue": "ZIP code and city", "zipPlaceholder": "ZIP", "cityPlaceholder": "City", "constructionYearQue": "Construction year", "constructionYearPlaceholder": "Enter year", "areaQue": "What is the area of the building?", "areaPlaceholder": "Enter area", "floorsQue": "How many floors are there?", "floorDsc": "Provide all the floors that have at least 2m height on the entire floor area, excluding basement", "floorHeightQue": "What is the height of the floor?", "tenantsQue": "How many tenants live in the building?"}, "basement": {"title": "Basement", "typeQue": "What type of basement does the building have?", "basementInsulatedQue": "Is the basement insulated?", "groundFloorInsulatedQue": "Is the building floor insulated?", "basementInsulationYearQue": "Basement insulation year", "groundFloorInsulationYearQue": "Ground floor insulation year", "insulationYearDsc": "If no value is provided, the building's construction year will be considered as the insulation year", "insulationYearPlaceholder": "Enter year"}, "heating": {"title": "Heating", "primaryHeatingQue": "What type of heating does the building have?", "primaryHeatingInstallationYearQue": "Heating installation year", "primaryHeatingInstallationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "primaryHeatingInstallationYearPlaceholder": "Enter year", "isWaterHeatingDifferent": "Is water heating different from building heating?", "waterHeatingQue": "Select water heating type", "floorHeatingQue": "Is there a floor heating?", "solarHeatingQue": "Is there a solar thermal plant installed?"}, "facade": {"title": "Exterior Walls", "insulatedQue": "Have the exterior walls been insulated?", "insulationYearQue": "Walls insulation year", "insulationYearDsc": "If no value is provided, the building's construction year will be considered as the insulation year", "insulationYearPlaceholder": "Enter year"}, "roof": {"title": "<PERSON><PERSON>", "floorQue": "What type of the roof floor does the building have?", "floorOptionsSublabels": {"FULLY_LIVEABLE_REDUCED": "floor with reduced height", "FULLY_LIVEABLE_ELEVATED": "floor with full height"}, "floorOptionsDescriptions": {"FULLY_LIVEABLE_REDUCED": "The knee wall height is 1-2m", "FULLY_LIVEABLE_ELEVATED": "The knee wall height is 2m +"}, "ceilingOrRoofInsulatedQue": "Is the ceiling or roof insulated?", "insulationYearQue": "Insulation year", "insulationYearDsc": "If no value is provided, the building's construction year will be considered as the insulation year", "insulationYearPlaceholder": "Enter year", "hasSolarPlantQue": "Is there already a solar plant installed", "solarPlantPlaceholder": "Enter power", "solarPlantPlaceholderDsc": "Provide the nominal power for the solar plant", "solarPlantInstallationYearQue": "Solar plant installation year", "solarPlantInstallationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "solarPlantInstallationYearPlaceholder": "Enter year", "eligibleForSolarQue": "Does the roof seem eligible for solar?"}, "windows": {"title": "Windows", "windowsToWallRatioQue": "What is the size of the window surface area in the building?", "windowsToWallRatioDsc": {"LOW": "Few window surfaces", "MEDIUM": "Average-sized window surfaces", "HIGH": "Many windows plus floor-to-ceiling windows, across the facade windows"}, "glazingQue": "What is the glazing type of the windows?", "installationYearQue": "Windows installation year", "installationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "installationYearPlaceholder": "Enter year", "shuttersQue": "What type of shutters do the windows have?"}, "electricalEquipment": {"title": "Electrical equipment", "typeQue": "What type of other electrical equipment does the building have?", "acInstallationYearQue": "Air conditioning installation year", "acInstallationYearDsc": "If no value is provided, the building's construction year will be considered as the installation year", "acInstallationYearPlaceholder": "Enter year"}}, "calculationReview": {"tabs": {"RESULTS": "Results", "BUILDING_INFO": "Building Info"}, "buildingReview": {"common": {"sameAsConstructionYear": "Same as construction year", "notProvided": "Not provided", "na": "Not available"}, "buildingInfo": {"title": "Building Information", "shape": "Building shape", "position": "Building position", "constructionYear": "Construction year", "area": "Area", "floors": "No. of floors", "floorHeight": "Floor height", "tenants": "No. of tenants"}, "basement": {"title": "Basement", "type": "Basement type", "basementInsulationYear": "Basement insulation year", "basementInsulated": "Basement insulated", "groundFloorInsulated": "Ground floor insulated", "groundFloorInsulationYear": "Ground floor insulation year"}, "heating": {"title": "Heating", "primaryHeating": "Heating type", "primaryHeatingInstallationYear": "Installation year", "waterHeating": "Water heating type", "floorHeating": "Floor heating", "solarHeating": "Solar thermal plant in place"}, "facade": {"title": "Exterior Walls", "insulated": "Insulated walls", "insulationYear": "Insulation year"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Roof floor type", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "Pitched roof, liveable floor with reduced height", "FULLY_LIVEABLE_ELEVATED": "Pitched roof, liveable floor with full height", "PARTIALLY_LIVEABLE": "Pitched roof", "COLD_STORAGE": "Pitched roof, no liveable roof floor"}, "ceilingOrRoofInsulated": "Ceiling or roof insulated", "insulationYear": "Insulation year", "hasSolarPlant": "Installed solar plant", "solarPlantInstallationYear": "Solar plant installation year", "solarPlantPower": "Solar plant power", "eligibleForSolar": "Roof eligible for solar"}, "windows": {"title": "Windows", "windowsToWallRatio": "Windows to wall proportion", "windowsToWallRatioOptions": {"LOW": "Few window surfaces", "MEDIUM": "Average-sized window surfaces", "HIGH": "Many windows plus floor-to-ceiling windows, across the facade windows"}, "glazing": "Insulation", "installationYear": "Installation year", "shutters": "Shutters type"}, "electricalEquipment": {"title": "Electrical equipment", "type": "Equipment type", "acInstallationYear": "Air conditioning installation year"}}, "cta": {"recalculate": "Re-run calculation", "exportToPdf": "Export to PDF"}}}}