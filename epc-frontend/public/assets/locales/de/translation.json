{"core": {"score": {"A_PLUS": "A+", "A": "A", "B": "B", "C": "C", "D": "D", "E": "E", "F": "F", "G": "G", "H": "H"}, "boolean": {"false": "<PERSON><PERSON>", "true": "<PERSON>a"}, "validation": {"required": "Diese Information ist erforderlich", "range": "Wert muss im Bereich {{min}} - {{max}} liegen"}, "units": {"euro": "EUR", "energyDemand": "kWh/m² p.a.", "emission": {"KG": "kg CO2 p.a.", "KG_M2": "kg CO2/m² p.a."}, "energyPerAnnum": "kWh p.a.", "energyPeak": "kWp"}}, "interceptors": {"errors": {"401": {"body": "<PERSON>e wurden ausgeloggt, bitte laden Sie die Seite neu.", "title": "Seite neu laden"}}}, "pdf": {"notAvailable": "k.A.", "currentEnergyEfficiency": "Aktueller Primärenergiebedarf", "newEnergyEfficiency": "Neuer Primärenergiebedarf", "selectedMeasures": "Ausgewählte Maßnahmen", "measure": "Maßnahmen", "noMeasuresSelected": "<PERSON><PERSON> ausgewählt", "moreRenovations": "weitere Modernisierungsmaßnahmen", "grant": "Potenzielle Förderprogramme", "price": "Geschätzte Kosten (EUR)", "profitability": "Rentabilität", "energyIndex": "Energieindex", "totalProfit": "Gesamt", "energyCostSaved": "Energieersparnis über 10 Jahre", "propertyValueIncrease": "Immobilienwertsteigerung", "renovationCost": "Modernisierungskosten", "energyEfficiency": "<PERSON>rim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalEnergyDemandPerArea": "Endenergiebedarf", "co2Emission": "CO2-Emission", "solarEnergyProd": "Solarstromproduktion", "buildingOverview": "Gebäudeinformationen", "building": {"common": {"sameAsConstructionYear": "<PERSON><PERSON>", "notProvided": "Nicht angegeben", "na": "Nicht verfügbar"}, "info": {"title": "Gebäudeinformationen", "shape": "Gebäudestruktur", "position": "Gebäudeposition", "constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "area": "Fläche", "floors": "Anzahl der Vollgeschosse", "floorHeight": "Stockwerkhöhe", "tenants": "Anzahl der Parteien"}, "basement": {"title": "<PERSON>", "type": "Kellertyp", "basementInsulationYear": "Jahr der Kellerbodendämmung", "basementInsulated": "<PERSON>", "groundFloorInsulationYear": "Jahr der Erdgeschossbodendämmung", "groundFloorInsulated": "Erdgeschossboden gedämmt"}, "heating": {"title": "Heizung", "primaryHeating": "Heizungsart", "primaryHeatingInstallationYear": "Installationsjahr", "waterHeating": "Art der Warmwasserbereitung", "floorHeating": "Fußbodenheizung", "solarHeating": "Solarthermieanlage vorhanden"}, "facade": {"title": "Außenwände", "insulated": "Gedämmte Wände", "insulationYear": "Dämmungsjahr"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Dachgeschosstyp", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit reduzierter Höhe", "FULLY_LIVEABLE_ELEVATED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit voller Höhe", "COLD_STORAGE": "<PERSON><PERSON><PERSON><PERSON>, unbewohntes Dachgeschoss", "PARTIALLY_LIVEABLE": "<PERSON><PERSON><PERSON><PERSON>"}, "insulationYear": "Dämmungsjahr", "ceilingOrRoofInsulated": "Ist die oberste Geschossdecke oder das Dach gedämmt?", "hasSolarPlant": "Installierte PV-Anlage", "solarPlantPower": "Leistung der PV-Anlage", "solarPlantInstallationYear": "Installationsjahr PV", "eligibleForSolar": "Dach für PV-<PERSON>lage geeignet"}, "windows": {"title": "<PERSON><PERSON>", "windowsToWallRatio": "Fensterflächenanteil", "windowsToWallRatioOptions": {"LOW": "Wenige Fensterflächen", "MEDIUM": "Durchschnittlich große Fensterflächen", "HIGH": "Viele Fenster oder bodentiefe Fenster, Fassade mit großen Fensterflächen"}, "glazing": "Verglasung", "installationYear": "Einbaujahr", "shutters": "Art der Rollläden"}, "electricalEquipment": {"title": "Elektrische Ausstattung", "type": "Ausstattungstyp", "acInstallationYear": "Installationsjahr der Klimaanlage"}}}, "integration": {"europace": {"BaufiSmart": "BaufiSmart", "caseId": "Vorgangsnr.", "sent": "Gesendet", "notSent": "<PERSON><PERSON><PERSON><PERSON>", "integrationTime": "{{date}}", "cta": {"send": "Senden", "update": "Update"}, "errors": {"noLicenseTitle": "<PERSON> benötigt", "noLicenseMsgPrefix": "Sie haben noch keine BaufiSmart-Integration. Kontaktieren Sie", "noLicenseMsgPostfix": ", um die Integration vorzunehmen.", "close": "Schließen"}}}, "dataAccess": {"buildingShape": {"COMPACT": "Kompakt", "COMPLEX": "Komplex (U, S)", "L_SHAPE": "L-förmig"}, "buildingPosition": {"STAND_ALONE": "<PERSON><PERSON><PERSON><PERSON>", "TWIN_HOUSE": "<PERSON><PERSON>haus", "ROW_MIDDLE": "Reihenmittelhaus", "ROW_END": "Re<PERSON><PERSON>ndhaus"}, "buildingFloorHeight": {"REGULAR": "<PERSON><PERSON><PERSON><PERSON> (<=2,5m)", "HIGH": "Hoch (>2,5m)"}, "buildingBasement": {"NO_BASEMENT": "<PERSON><PERSON>", "UNHEATED": "Unbehe<PERSON><PERSON> Keller", "HEATED": "Vollbeheizter Keller"}, "buildingHeating": {"GAS_BOILER": "Gasheizung", "OIL_BOILER": "Ölheizung", "BIOMASS_BOILER": "Biomasse/Holz", "ELECTRIC": "Elektrischer Boiler", "HEAT_PUMP": "Wärmepumpe", "NIGHT_STORAGE": "Nachtspeicherheizung", "DISTRICT": "Fernwärme"}, "buildingRoofSlope": {"PITCHED": "<PERSON><PERSON><PERSON><PERSON>", "FLAT": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "buildingRoofFloor": {"FLAT_ROOF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PARTIALLY_LIVEABLE": "<PERSON>il<PERSON><PERSON> bewohnt", "FULLY_LIVEABLE_REDUCED": "Vollständig bewohnt", "FULLY_LIVEABLE_ELEVATED": "Vollständig bewohnt", "COLD_STORAGE": "Dachgeschoss"}, "buildingWindowsToWallRatio": {"LOW": "<PERSON><PERSON>", "MEDIUM": "<PERSON><PERSON><PERSON>", "HIGH": "Viel"}, "buildingWindowsGlazing": {"SINGLE": "Einfachverglasung", "DOUBLE": "Doppelverglasung", "TRIPLE": "Dreifachverglasung"}, "buildingWindowsShutters": {"MANUAL": "<PERSON><PERSON>", "ELECTRIC": "Elektrisch", "NONE": "<PERSON><PERSON>"}, "buildingEquipment": {"VENTILATION_HEAT_EXCHANGE": "Zentrales Belüftungssystem mit Wärmerückgewinnung", "AC": "<PERSON><PERSON><PERSON>"}}, "appShell": {"loggedInAs": "<PERSON><PERSON><PERSON><PERSON> als", "selectLanguage": "Sprache auswählen", "logout": "Abmelden", "pageError": "Etwas ist schiefgelaufen", "pageErrorDesc": "Die Seite konnte nicht geladen werden", "pageErrorHint": "Bitte überprüfen Sie, ob die Seiten-URL korrekt ist, und versuchen Sie, die Seite neu zu laden. Falls das Problem weiterhin besteht, kontaktieren Sie uns unter", "pageErrorCta": "Zurück", "pageErrorToggle": "Fehlerdetails anzeigen"}, "pages": {"calculationResult": {"cta": {"exportPdf": "Als PDF exportieren", "editBuildingData": "Gebäudedaten anpassen", "applyRenovations": "<PERSON><PERSON><PERSON>", "calculating": "Be<PERSON><PERSON>nen ...", "sendToBaufinex": "An Baufinex senden"}, "results": {"profitability": "Rentabilität", "energyIndex": "Energieindex", "totalProfit": "Gesamt", "energyCostSaved": "Energieersparnis über 10 Jahre", "propertyValueIncrease": "Immobilienwertsteigerung", "renovationCost": "Modernisierungskosten", "energyEfficiency": "<PERSON>rim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalEnergyDemandPerArea": "Endenergiebedarf", "co2Emission": "CO2-Emission", "solarEnergyProd": "Solarstromproduktion"}, "energyEfficiency": {"current": "Aktuell {{value}}", "new": "Neu {{value}}"}, "measures": {"title": "<PERSON><PERSON><PERSON><PERSON>", "table": {"measures": "Maßnahmen", "grant": "Potenzielle Förderprogramme", "cost": "Geschätzte Kosten", "expectedValue": "EW", "recommended": "Empf.", "na": "Nicht verfügbar"}, "summary": {"selected": "Ausgewählte Maßnahmen", "possible": "Mögliche Maßnahmen"}}}, "calculationList": {"title": "Berechnungen", "cta": {"newCalculation": "Neue Berechnung"}, "searchPlaceholder": "<PERSON><PERSON>", "table": {"date": "Datum", "address": "<PERSON><PERSON><PERSON>", "efficiency": "Effizienz", "efficiencyUnit": "kWh/m²/Jahr", "score": "Score"}}, "buildingForm": {"cta": {"continue": "<PERSON><PERSON>", "calculating": "Be<PERSON><PERSON>nen ..."}, "formErrors": {"roofFloorReqFloors": "Die aktuelle Dachkonfiguration erfordert mindestens {{min}} Vollgeschosse", "areaTooSmallForConfiguration": "Die Fläche ist zu klein für die aktuelle Stockwerks- und Dachkonfiguration. Sie sollte mindestens {{min}} betragen."}, "buildingInfo": {"title": "Gebäudeinformationen", "shapeQue": "Welche Form hat das Gebäude?", "shapeDsc": "Betrachten Sie nur den beheizten Teil", "positionQue": "Welche Position hat das Gebäude?", "streetQue": "Adresse und Hausnummer", "streetPlaceholder": "Straße", "noPlaceholder": "Nr.", "zipCodeQue": "Postleitzahl und Stadt", "zipPlaceholder": "PLZ", "cityPlaceholder": "Ort", "constructionYearQue": "<PERSON><PERSON><PERSON><PERSON>", "constructionYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "areaQue": "Wie groß ist die Wohnfläche des Gebäudes?", "areaPlaceholder": "Fläche eingeben", "floorsQue": "Wie viele Vollgeschosse hat das Gebäude?", "floorDsc": "Geben Sie alle Stockwerke mit mindestens 2 m Raumhöhe an, <PERSON> ausgenommen", "floorHeightQue": "Wie hoch sind die Stockwerke (Vollgeschosse)?", "tenantsQue": "Wie viele Parteien leben im Gebäude?"}, "basement": {"title": "<PERSON>", "typeQue": "<PERSON><PERSON> Kellertyp hat das Gebäude?", "insulationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Dämmjahr", "insulationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "basementInsulatedQue": "Ist der Keller gedämmt?", "groundFloorInsulatedQue": "Ist der der Erdgeschossboden gedämmt?", "basementInsulationYearQue": "Jahr der Kellerbodendämmung", "groundFloorInsulationYearQue": "Jahr der Erdgeschossbodendämmung"}, "heating": {"title": "Heizung", "primaryHeatingQue": "Welche Heizungsart hat das Gebäude?", "primaryHeatingInstallationYearQue": "Installationsjahr der Heizung", "primaryHeatingInstallationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Installationsjahr", "primaryHeatingInstallationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "isWaterHeatingDifferent": "Unterscheidet sich die Warmwasserbereitung von der Gebäudeheizung?", "waterHeatingQue": "Wählen Sie die Art der Warmwasserbereitung", "floorHeatingQue": "Gibt es eine Fußbodenheizung?", "solarHeatingQue": "Ist eine Solarthermieanlage installiert?"}, "facade": {"title": "Außenwände", "insulatedQue": "Sind die Außenwände gedämmt?", "insulationYearQue": "Jahr der Wanddämmung", "insulationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Dämmjahr", "insulationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>"}, "roof": {"title": "<PERSON><PERSON>", "floorQue": "Welche Art von Dachboden hat das Gebäude?", "floorOptionsSublabels": {"FULLY_LIVEABLE_REDUCED": "Reduzierte Höhe", "FULLY_LIVEABLE_ELEVATED": "Hohes Dachgeschoss"}, "floorOptionsDescriptions": {"FULLY_LIVEABLE_REDUCED": "Die Kniestockhöhe beträgt 1-2 m", "FULLY_LIVEABLE_ELEVATED": "Die Kniestockhöhe beträgt ≥2 m"}, "ceilingOrRoofInsulatedQue": "Ist die oberste Geschossdecke oder das Dach gedämmt?", "insulationYearQue": "Jahr der Dämmung", "insulationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Dämmjahr", "insulationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "hasSolarPlantQue": "Ist bereits eine PV-Anlage installiert?", "solarPlantPlaceholder": "Leistung in kWp eingeben", "solarPlantPlaceholderDsc": "Geben Sie die Nennleistung der PV-Anlage an", "solarPlantInstallationYearQue": "Installationsjahr der PV-Anlage", "solarPlantInstallationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Installationsjahr", "solarPlantInstallationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "eligibleForSolarQue": "Ist das Dach für PV-Anlage geeignet?"}, "windows": {"title": "<PERSON><PERSON>", "windowsToWallRatioQue": "Wie groß ist die Fensterfläche im Gebäude?", "windowsToWallRatioDsc": {"LOW": "Wenige Fensterflächen", "MEDIUM": "Durchschnittlich große Fensterflächen", "HIGH": "Viele Fenster oder bodentiefe Fenster, Fassade mit großen Fensterflächen"}, "glazingQue": "Welche Verglasung haben die Fenster?", "installationYearQue": "Einbaujahr der Fenster", "installationYearDsc": "<PERSON><PERSON> kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Einbaujahr", "installationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "shuttersQue": "Welche Art von Rollläden haben die Fenster?"}, "electricalEquipment": {"title": "Elektrische Ausstattung", "typeQue": "Welche Art von elektrischer Ausstattung hat das Gebäude?", "acInstallationYearQue": "Installationsjahr der Klimaanlage", "acInstallationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Installationsjahr", "acInstallationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>"}}, "calculationReview": {"tabs": {"RESULTS": "Ergebnisse", "BUILDING_INFO": "Gebäudeinformationen"}, "buildingReview": {"common": {"sameAsConstructionYear": "<PERSON><PERSON>", "notProvided": "Nicht angegeben", "na": "Nicht verfügbar"}, "buildingInfo": {"title": "Gebäudeinformationen", "shape": "Gebäudestruktur", "position": "Gebäudeposition", "constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "area": "Fläche", "floors": "Anzahl der Vollgeschosse", "floorHeight": "Stockwerkhöhe", "tenants": "Anzahl der Parteien"}, "basement": {"title": "<PERSON>", "type": "Kellertyp", "groundFloorInsulated": "Erdgeschossboden gedämmt", "groundFloorInsulationYear": "Jahr der Erdgeschossbodendämmung", "basementInsulationYear": "Jahr der Kellerbodendämmung", "basementInsulated": "<PERSON>"}, "heating": {"title": "Heizung", "primaryHeating": "Heizungsart", "primaryHeatingInstallationYear": "Installationsjahr", "waterHeating": "Art der Warmwasserbereitung", "floorHeating": "Fußbodenheizung", "solarHeating": "Solarthermieanlage vorhanden"}, "facade": {"title": "Außenwände", "insulated": "Gedämmte Wände", "insulationYear": "Dämmungsjahr"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Dachgeschosstyp", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit reduzierter Höhe", "FULLY_LIVEABLE_ELEVATED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit voller Höhe", "COLD_STORAGE": "<PERSON><PERSON><PERSON><PERSON>, unbewohntes Dachgeschoss", "PARTIALLY_LIVEABLE": "<PERSON><PERSON><PERSON><PERSON>"}, "ceilingOrRoofInsulated": "Geschossdecke oder das Dach gedämmt", "insulationYear": "Dämmungsjahr", "hasSolarPlant": "Installierte PV-Anlage", "solarPlantInstallationYear": "Installationsjahr der PV-Anlage", "solarPlantPower": "Leistung der PV-Anlage", "eligibleForSolar": "Dach für PV-<PERSON>lage geeignet"}, "windows": {"title": "<PERSON><PERSON>", "windowsToWallRatio": "Fensterflächenanteil", "windowsToWallRatioOptions": {"LOW": "Wenige Fensterflächen", "MEDIUM": "Durchschnittlich große Fensterflächen", "HIGH": "Viele Fenster oder bodentiefe Fenster, Fassade mit großen Fensterflächen"}, "glazing": "Verglasung", "installationYear": "Einbaujahr", "shutters": "Art der Rollläden"}, "electricalEquipment": {"title": "Elektrische Ausstattung", "type": "Ausstattungstyp", "acInstallationYear": "Installationsjahr der Klimaanlage"}}, "cta": {"recalculate": "Berechnung erneut durch<PERSON>hren", "exportToPdf": "Als PDF exportieren"}}}}