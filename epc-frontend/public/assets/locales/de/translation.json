{"core": {"score": {"A_PLUS": "A+", "A": "A", "B": "B", "C": "C", "D": "D", "E": "E", "F": "F", "G": "G", "H": "H"}, "boolean": {"false": "<PERSON><PERSON>", "true": "<PERSON>a"}, "validation": {"required": "Diese Information ist erforderlich", "range": "Wert muss im Bereich {{min}} - {{max}} liegen"}, "units": {"euro": "EUR", "energyDemand": "kWh/m² p.a.", "emission": {"KG": "kg CO2 p.a.", "KG_M2": "kg CO2/m² p.a."}, "energyPerAnnum": "kWh p.a.", "energyPeak": "kWp"}}, "interceptors": {"errors": {"401": {"body": "<PERSON>e wurden ausgeloggt, bitte laden Sie die Seite neu.", "title": "Seite neu laden"}, "0": {"title": "Probleme mit der Verbindung", "body": ""}}, "axiosInternalErrors": {"ECONNABORTED": {"common": {"title": "Die Anfrage dauert länger als erwartet", "body": "Bitte warten Sie einen Moment und aktualisieren Sie die Seite."}, "cost": {"title": "Die Kostenabfrage dauert länger als erwartet", "body": "Bitte warten Sie einen Moment und aktualisieren Sie die Seite."}}}}, "pdf": {"notAvailable": "k.A.", "currentPrimaryEnergyDemand": "Aktueller Primärenergiebedarf", "alternativePrimaryEnergyDemand": "Neuer Primärenergiebedarf", "selectedMeasures": "Ausgewählte Maßnahmen", "measure": "Maßnahmen", "noMeasuresSelected": "<PERSON><PERSON> ausgewählt", "moreRenovations": "weitere Modernisierungsmaßnahmen", "grant": "Potenzielle Förderprogramme", "price": "Geschätzte Kosten (EUR)", "profitability": "Rentabilität", "energyIndex": "Energieindex", "totalProfit": "Gesamt", "energyCostSaved": "Energieersparnis über 10 Jahre", "propertyValueIncrease": "Immobilienwertsteigerung", "renovationCost": "Modernisierungskosten", "primaryEnergyDemand": "<PERSON>rim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalEnergyDemandPerArea": "Endenergiebedarf", "co2Emission": "CO2-Emission", "solarEnergyProd": "Solarstromproduktion", "buildingOverview": "Gebäudeinformationen", "building": {"common": {"sameAsConstructionYear": "<PERSON><PERSON>", "notProvided": "Nicht angegeben", "na": "Nicht verfügbar"}, "info": {"title": "Gebäudeinformationen", "shape": "Gebäudestruktur", "position": "Gebäudeposition", "constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "area": "Fläche", "floors": "Anzahl der Vollgeschosse", "floorHeight": "Stockwerkhöhe", "tenants": "Anzahl der Parteien"}, "basement": {"title": "<PERSON>", "type": "Kellertyp", "basementInsulationYear": "Jahr der Kellerbodendämmung", "basementInsulated": "<PERSON>", "groundFloorInsulationYear": "Jahr der Erdgeschossbodendämmung", "groundFloorInsulated": "Erdgeschossboden gedämmt"}, "heating": {"title": "Heizung", "primaryHeating": "Heizungsart", "primaryHeatingInstallationYear": "Installationsjahr", "waterHeating": "Art der Warmwasserbereitung", "floorHeating": "Fußbodenheizung", "solarHeating": "Solarthermieanlage vorhanden"}, "facade": {"title": "Außenwände", "insulated": "Gedämmte Wände", "insulationYear": "Dämmungsjahr"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Dachgeschosstyp", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit reduzierter Höhe", "FULLY_LIVEABLE_ELEVATED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit voller Höhe", "COLD_STORAGE": "<PERSON><PERSON><PERSON><PERSON>, unbewohntes Dachgeschoss", "PARTIALLY_LIVEABLE": "<PERSON><PERSON><PERSON><PERSON>"}, "insulationYear": "Dämmungsjahr", "ceilingOrRoofInsulated": "Ist die oberste Geschossdecke oder das Dach gedämmt?", "hasSolarPlant": "Installierte PV-Anlage", "solarPlantPower": "Leistung der PV-Anlage", "solarPlantInstallationYear": "Installationsjahr PV", "eligibleForSolar": "Dach für PV-<PERSON>lage geeignet"}, "windows": {"title": "<PERSON><PERSON>", "windowsToWallRatio": "Fensterflächenanteil", "windowsToWallRatioOptions": {"LOW": "Wenige Fensterflächen", "MEDIUM": "Durchschnittlich große Fensterflächen", "HIGH": "Viele Fenster oder bodentiefe Fenster, Fassade mit großen Fensterflächen"}, "glazing": "Verglasung", "installationYear": "Einbaujahr", "shutters": "Art der Rollläden"}, "electricalEquipment": {"title": "Elektrische Ausstattung", "type": "Ausstattungstyp", "acInstallationYear": "Installationsjahr der Klimaanlage"}}, "measures": {"FACADE_EXTERNAL_WALL_INSULATION": {"label": "Fassadendämmung"}, "FACADE_EXTERIOR_CLADDING": {"label": "Fassadenverkleidung anbringen"}, "ROOF_INSULATION": {"label": "Dachdämmung"}, "ROOF_NEW_COVERAGE": {"label": "Neue Dacheindeckung"}, "ROOF_RE_ENFORCEMENT": {"label": "Dachverstärkung"}, "CEILING_INSULATION": {"label": "Deckendämmung"}, "WINDOWS_GLAZING_TYPE": {"label": "Fensterverglasungstyp"}, "WINDOWS_FRAME_MATERIAL_UPGRADE": {"label": "Fensterrahmen verbessern"}, "WINDOWS_SHUTTERS": {"label": "Fensterrollläden"}, "WINDOWS_SECURITY_FEATURES": {"label": "Fenstersicherungen installieren"}, "GROUND_FLOOR_INSULATION": {"label": "Bodendämmung Erdgeschoss"}, "GROUND_FLOOR_MOISTURE_PROTECTION": {"label": "Feuchteschutz Erdgeschoss"}, "BASEMENT_INSULATION": {"label": "Kellerdämmung"}, "BASEMENT_MOISTURE_PROTECTION": {"label": "<PERSON>"}, "DOORS_REPLACEMENT": {"label": "<PERSON><PERSON><PERSON>"}, "DOORS_SECURITY_FEATURES": {"label": "Türsicherungen installieren"}, "HEATING_PRIMARY_SOURCE": {"label": "<PERSON><PERSON><PERSON><PERSON>", "values": {"DISTRICT_HEATING_2025": "Fernwärme", "BIOMASS_HEATING_2025": "Biomasse-Heizung", "AIR_TO_WATER_HEAT_PUMP_2025": "Luft-Wasser-Wärmepumpe", "GROUND_TO_WATER_HEAT_PUMP_2025": "Sole-Wasser-Wärmepumpe"}}, "UNDERFLOOR_HEATING": {"label": "Fußbodenheizung", "values": {"WET_SYSTEM_WITH_SCREED": "Nasssystem inkl. Estrich und neuem Bodenbelag", "DRY_SYSTEM_WITH_FLOORING": "Trockensystem inkl. neuem Bodenbelag", "DRY_SYSTEM_WITHOUT_FLOORING": "Trockensystem ohne neuen Bodenbelag"}}, "WALL_HEATING": {"label": "Wandh<PERSON><PERSON>ng", "values": {"WET_SYSTEM_WITH_RECONSTRUCTION": "Nasssystem inkl. Wandsanierung", "DRY_SYSTEM_WITH_RECONSTRUCTION": "Trockensystem inkl. Wandsanierung"}}, "CEILING_HEATING": {"label": "Deckenheizung", "values": {"DRY_SYSTEM_WITH_RECONSTRUCTION": "Trockensystem inkl. Deckensanierung"}}, "HEATING_VENTILATION_HEAT_EXCHANGE": {"label": "Lüftung mit Wärmerückgewinnung"}, "BASIC_EFFICIENCY": {"label": "Basiseffizienz"}, "HIGH_PERFORMANCE": {"label": "Höchstleistung"}, "SOLAR_PANELS": {"label": "PV-Anlage"}, "SOLAR_PLANT_PANELS": {"label": "Solaranlagenmodule"}, "SOLAR_THERMAL": {"label": "Solarthermische Anlage"}, "BATTERY_STORAGE": {"label": "Batteriespeicher", "values": {"LITHIUM_ION_BATTERY": "Lithium-Ionen-Bat<PERSON>ie"}}, "IMMERSION_HEATER": {"label": "Heizstab", "values": {"WITH_BUFFER": "<PERSON><PERSON> ne<PERSON>m Warmwasserspeicher", "WITHOUT_BUFFER": "<PERSON><PERSON> neuem Warmwasserspeicher"}}, "INTELLIGENT_HEATING_CONTROLS": {"label": "Intelligente Heizungssteuerung"}, "LIGHTING_AUTOMATION": {"label": "Automatisierte Beleuchtung"}, "SENSORS": {"label": "Sensoriksysteme"}, "WALLBOX_INSTALLATION": {"label": "Installation einer Wallbox"}, "GRID_CONNECTION_UPGRADE": {"label": "Erweiterung des Netzanschlusses"}, "BARRIER_FREE_BATHROOM": {"label": "Barrierefre<PERSON>"}, "STAIRLIFT": {"label": "<PERSON><PERSON><PERSON>nlift"}, "WIDENING_DOORS": {"label": "Türverbreiterung"}, "EMERGENCY_SYSTEMS": {"label": "Notrufsysteme"}}, "categories": {"FACADE": "Fassade", "ROOF": "<PERSON><PERSON>", "WINDOWS": "<PERSON><PERSON>", "BASEMENT": "<PERSON>", "DOOR": "<PERSON><PERSON><PERSON>", "HEATING_SYSTEM": "Heizsystem", "SURFACE_HEATING": "Flächenheizung", "RADIATOR_UPGRADE": "Heizkörper er<PERSON>uern", "SOLAR_PANELS": "PV-Anlage", "SMART_HOME_ENERGY_MANAGEMENT": "Smart-Home Energiemanagement", "ELECTRIC_VEHICLE_CHARGING": "E-Auto-Ladestation", "AGE_APPROPRIATE_LIVING": "Barrierefreies Wohnen"}}, "integration": {"europace": {"BaufiSmart": "BaufiSmart", "caseId": "Vorgangsnr.", "sent": "Gesendet", "notSent": "<PERSON><PERSON><PERSON><PERSON>", "integrationTime": "{{date}}", "cta": {"send": "Senden", "update": "Update"}, "errors": {"noLicenseTitle": "<PERSON> benötigt", "noLicenseMsgPrefix": "Sie haben noch keine BaufiSmart-Integration. Kontaktieren Sie", "noLicenseMsgPostfix": ", um die Integration vorzunehmen.", "close": "Schließen"}, "caseType": {"placeholder": "Bitte wählen", "purchase": "<PERSON><PERSON>", "modernization": "Modernisierung (Umbau / Anbau)", "prolongation": "Anschlussfinanzierung"}}}, "dataAccess": {"grants": {"KFW_261": {"name": "KfW 261", "headline": "Bundesförderung für effiziente Gebäude – Wohngebäude (BEG WG)", "description": "Zinsgünstiges Darlehen + Tilgungszuschuss bis zu 45 %", "link": "https://www.kfw.de/inlandsfoerderung/Privatpersonen/Bestehende-Immobilie/F%C3%B6rderprodukte/Bundesf%C3%B6rderung-f%C3%BCr-effiziente-Geb%C3%A4ude-Wohngeb%C3%A4ude-Kredit-(261-262)/"}, "KFW_430": {"name": "KfW 159", "headline": "Altersgerecht Umbauen – Kredit", "description": "Zinsgünstiger Kredit bis EUR 50.000", "link": "https://www.kfw.de/inlandsfoerderung/Privatpersonen/Bestehende-Immobilie/F%C3%B6rderprodukte/Altersgerecht-Umbauen-(159)/"}, "KFW_270": {"name": "KfW 270", "headline": "Erneuerbare Energien – Standard", "description": "Zinsgünstiges Darlehen für erneuerbare Energien", "link": "https://www.kfw.de/inlandsfoerderung/Unternehmen/Energie-Umwelt/F%C3%B6rderprodukte/Erneuerbare-Energien-Standard-(270)/"}, "KFW_159": {"name": "KfW 159", "headline": "Altersgerecht Umbauen – Kredit", "description": "Zinsgünstiger Kredit bis EUR 50.000", "link": "https://www.kfw.de/inlandsfoerderung/Privatpersonen/Bestehende-Immobilie/F%C3%B6rderprodukte/Altersgerecht-Umbauen-(159)/"}}, "buildingShape": {"COMPACT": "Kompakt", "COMPLEX": "Komplex (U, S)", "L_SHAPE": "L-förmig"}, "buildingPosition": {"STAND_ALONE": "<PERSON><PERSON><PERSON><PERSON>", "TWIN_HOUSE": "<PERSON><PERSON>haus", "ROW_MIDDLE": "Reihenmittelhaus", "ROW_END": "Re<PERSON><PERSON>ndhaus"}, "buildingFloorHeight": {"REGULAR": "<PERSON><PERSON><PERSON><PERSON> (<=2,5m)", "HIGH": "Hoch (>2,5m)"}, "buildingBasement": {"NO_BASEMENT": "<PERSON><PERSON>", "UNHEATED": "Unbehe<PERSON><PERSON> Keller", "HEATED": "Vollbeheizter Keller"}, "buildingHeating": {"GAS_BOILER": "Gasheizung", "OIL_BOILER": "Ölheizung", "BIOMASS_BOILER": "Biomasse/Holz", "ELECTRIC": "Elektrischer Boiler", "HEAT_PUMP": "Wärmepumpe", "NIGHT_STORAGE": "Nachtspeicherheizung", "DISTRICT": "Fernwärme"}, "buildingRoofSlope": {"PITCHED": "<PERSON><PERSON><PERSON><PERSON>", "FLAT": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "buildingRoofFloor": {"FLAT_ROOF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PARTIALLY_LIVEABLE": "<PERSON>il<PERSON><PERSON> bewohnt", "FULLY_LIVEABLE_REDUCED": "Vollständig bewohnt", "FULLY_LIVEABLE_ELEVATED": "Vollständig bewohnt", "COLD_STORAGE": "Dachgeschoss"}, "buildingWindowsToWallRatio": {"LOW": "<PERSON><PERSON>", "MEDIUM": "<PERSON><PERSON><PERSON>", "HIGH": "Viel"}, "buildingWindowsGlazing": {"SINGLE": "Einfachverglasung", "DOUBLE": "Doppelverglasung", "TRIPLE": "Dreifachverglasung"}, "buildingWindowsShutters": {"MANUAL": "<PERSON><PERSON>", "ELECTRIC": "Elektrisch", "NONE": "<PERSON><PERSON>"}, "buildingEquipment": {"VENTILATION_HEAT_EXCHANGE": "Zentrales Belüftungssystem mit Wärmerückgewinnung", "AC": "<PERSON><PERSON><PERSON>"}, "renovationMeasureVariant": {"ADVANCED": "Hohe Qualität", "BASIC": "Kostenoptimiert", "DEFAULT": "Standard"}}, "appShell": {"loggedInAs": "<PERSON><PERSON><PERSON><PERSON> als", "selectLanguage": "Sprache auswählen", "logout": "Abmelden", "pageError": "Etwas ist schiefgelaufen", "pageErrorDesc": "Die Seite konnte nicht geladen werden", "pageErrorHint": "Bitte überprüfen Sie, ob die Seiten-URL korrekt ist, und versuchen Sie, die Seite neu zu laden. Falls das Problem weiterhin besteht, kontaktieren Sie uns unter", "pageErrorCta": "Zurück", "pageErrorToggle": "Fehlerdetails anzeigen"}, "pages": {"calculationResult": {"categories": {"summary": {"totalCost": "Aktuelle Gesamtkosten Modernisierung", "notApplied": "Modernisierungen anwenden, um neue Ergebnisse zu sehen", "applyToSeeTheChanges": "Wenden Sie zunächst Modernisierungen an, um Änderungen der Ergebnisse zu sehen", "applied": "Energieeffizienz aktuell", "apply": "Modernisierungen anwenden", "showSelectedMeasures": "Alle ausgewählten Maßnahmen zeigen", "hideSelectedMeasures": "Ausgewählte Maßnahmen ausblenden", "noMeasuresSelected": "<PERSON><PERSON> ausgewählt"}, "common": {"na": "Nicht verfügbar", "naRange": "<PERSON><PERSON><PERSON> nicht verfügbar", "range": "{{min}} - {{max}} EUR", "estimatedCost": "Geschätzte Kosten", "possibleGrants": "Mögliche Zuschüsse", "noMeasuresSelected": "<PERSON><PERSON> ausgewählt", "recommended": "<PERSON><PERSON><PERSON><PERSON>", "showGrants": "Zuschüsse anzeigen", "hideGrants": "Zuschüsse ausblenden", "installationType": "Installationstyp", "selectedMeasures": "Ausgewählte Maßnahmen", "reviewAndApplyRenovations": "Modernisierungen anwenden", "forExample": "z.B<PERSON> {{example}}"}, "texts": {"FACADE": {"label": "Fassade", "measures": {"FACADE_EXTERNAL_WALL_INSULATION": {"label": "Fassadendämmung", "badge": "Dämmung", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Styropor-Dämmung", "desc": "z.B. Styropor-Dämmung"}, "MINERAL_WOOL_10_CM_2025": {"label": "Mineralwolle-Dämmung", "desc": "z.B. Mineralwolle-Dämmung"}, "WOOD_FIBER_15_CM_2025": {"label": "Holzfasern-Dämmung", "desc": "z.B. Holzfasern-Dämmung"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)", "desc": "z.B. Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Dünne Hochleistungsdämmplatten (Vakuum)", "desc": "z.B. Dünne Hochleistungsdämmplatten (Vakuum)"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Sehr dünne Hochleistungsdämmung (Vakuum)", "desc": "z.B. Sehr dünne Hochleistungsdämmung (Vakuum)"}, "AEROGEL_4_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "AEROGEL_1_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Glasfaser-Dämmung", "desc": "z.B. Glasfaser-Dämmung"}}}, "FACADE_EXTERIOR_CLADDING": {"label": "Fassadenverkleidung anbringen", "badge": "Verkleidung", "values": {"DEFAULT": {"label": "", "desc": "z.B. hinterlüftete Holz- oder Faserzementverkleidung, Klinker, Verschieferung"}}}}}, "ROOF": {"label": "<PERSON><PERSON>", "measures": {"CEILING_INSULATION": {"label": "Deckendämmung", "badge": "Dämmung", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Styropor-Dämmung", "desc": "z.B. Styropor-Dämmung"}, "MINERAL_WOOL_10_CM_2025": {"label": "Mineralwolle-Dämmung", "desc": "z.B. Mineralwolle-Dämmung"}, "WOOD_FIBER_15_CM_2025": {"label": "Holzfasern-Dämmung", "desc": "z.B. Holzfasern-Dämmung"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)", "desc": "z.B. Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Dünne Hochleistungsdämmplatten (Vakuum)", "desc": "z.B. Dünne Hochleistungsdämmplatten (Vakuum)"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Sehr dünne Hochleistungsdämmung (Vakuum)", "desc": "z.B. Sehr dünne Hochleistungsdämmung (Vakuum)"}, "AEROGEL_4_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "AEROGEL_1_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Glasfaser-Dämmung", "desc": "z.B. Glasfaser-Dämmung"}}}, "ROOF_INSULATION": {"label": "Dachdämmung", "badge": "Dämmung", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Styropor-Dämmung", "desc": "z.B. Styropor-Dämmung"}, "MINERAL_WOOL_10_CM_2025": {"label": "Mineralwolle-Dämmung", "desc": "z.B. Mineralwolle-Dämmung"}, "WOOD_FIBER_15_CM_2025": {"label": "Holzfasern-Dämmung", "desc": "z.B. Holzfasern-Dämmung"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)", "desc": "z.B. Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Dünne Hochleistungsdämmplatten (Vakuum)", "desc": "z.B. Dünne Hochleistungsdämmplatten (Vakuum)"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Sehr dünne Hochleistungsdämmung (Vakuum)", "desc": "z.B. Sehr dünne Hochleistungsdämmung (Vakuum)"}, "AEROGEL_4_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "AEROGEL_1_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Glasfaser-Dämmung", "desc": "z.B. Glasfaser-Dämmung"}}}, "ROOF_NEW_COVERAGE": {"label": "Neue Dacheindeckung", "badge": "Eindeckung", "values": {"CLAY_TILES": {"label": "Dachziegel aus Ton", "desc": "z.B. Dachziegel aus Ton"}, "NATURAL_SLATE": {"label": "Dachdeckung aus Naturschiefer", "desc": "z.B. Dachdeckung aus Naturschiefer"}, "CONCRETE_TILES": {"label": "Dachsteine aus Beton", "desc": "z.B. Dachsteine aus Beton"}, "METAL_TILES": {"label": "Dachplatten aus Metall", "desc": "z.B. Dachplatten aus Metall"}, "BITUMEN_MEMBRANE": {"label": "Bitumen-Dachbahn", "desc": "z.B. Bitumen-Dachbahn"}, "HIGH_QUALITY_PLASTIC_FOIL": {"label": "Hochwertige Dachfolie aus Kunststoff", "desc": "z.B. Hochwertige Dachfolie aus Kunststoff"}}}, "ROOF_RE_ENFORCEMENT": {"label": "Dachverstärkung", "badge": "Verstärkung", "values": {"DEFAULT": {"label": "", "desc": "z.B. Veränderung / Verstärkung des Dachstuhls"}}}}}, "WINDOWS": {"label": "<PERSON><PERSON>", "measures": {"WINDOWS_GLAZING_TYPE": {"label": "Fensterverglasungstyp", "badge": "Verglasung", "values": {"DOUBLE_GLAZING": {"label": "", "desc": "<PERSON><PERSON><PERSON> verglaste <PERSON>"}, "TRIPLE_GLAZING": {"label": "", "desc": "<PERSON>eifa<PERSON> verglaste <PERSON>"}}}, "WINDOWS_SHUTTERS": {"label": "Fensterrollläden", "badge": "Rollläden", "values": {"MANUAL_SHUTTERS": {"label": "<PERSON><PERSON>", "desc": "<PERSON><PERSON>"}, "ELECTRIC_SHUTTERS": {"label": "Elektrische Rollläden", "desc": "Elektrische Rollläden"}}}, "WINDOWS_SECURITY_FEATURES": {"label": "Fenstersicherungen installieren", "badge": "Sicherheit", "values": {"DEFAULT": {"label": "", "desc": "z.B. Pilzkopfverriegelung oder abschließbare Fenstergriffe"}}}}}, "BASEMENT": {"label": "<PERSON>", "measures": {"GROUND_FLOOR_INSULATION": {"label": "Bodendämmung Erdgeschoss", "badge": "Dämmung", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Styropor-Dämmung", "desc": "z.B. Styropor-Dämmung"}, "MINERAL_WOOL_10_CM_2025": {"label": "Mineralwolle-Dämmung", "desc": "z.B. Mineralwolle-Dämmung"}, "WOOD_FIBER_15_CM_2025": {"label": "Holzfasern-Dämmung", "desc": "z.B. Holzfasern-Dämmung"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)", "desc": "z.B. Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Dünne Hochleistungsdämmplatten (Vakuum)", "desc": "z.B. Dünne Hochleistungsdämmplatten (Vakuum)"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Sehr dünne Hochleistungsdämmung (Vakuum)", "desc": "z.B. Sehr dünne Hochleistungsdämmung (Vakuum)"}, "AEROGEL_4_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "AEROGEL_1_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Glasfaser-Dämmung", "desc": "z.B. Glasfaser-Dämmung"}}}, "BASEMENT_INSULATION": {"label": "Kellerdämmung", "badge": "Dämmung", "values": {"EXPANDED_POLYSTYRENE_30_CM_2025": {"label": "Styropor-Dämmung", "desc": "z.B. Styropor-Dämmung"}, "MINERAL_WOOL_10_CM_2025": {"label": "Mineralwolle-Dämmung", "desc": "z.B. Mineralwolle-Dämmung"}, "WOOD_FIBER_15_CM_2025": {"label": "Holzfasern-Dämmung", "desc": "z.B. Holzfasern-Dämmung"}, "EXTRUDED_POLYSTYRENE_10_CM_2025": {"label": "Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)", "desc": "z.B. Styrodur-Dämmung (ähnlich Styropor aber dichter & wasserabweisend)"}, "VACUUM_INSULATED_PANEL_30_CM_2025": {"label": "Dünne Hochleistungsdämmplatten (Vakuum)", "desc": "z.B. Dünne Hochleistungsdämmplatten (Vakuum)"}, "VACUUM_INSULATED_PANEL_5_CM_2025": {"label": "Sehr dünne Hochleistungsdämmung (Vakuum)", "desc": "z.B. Sehr dünne Hochleistungsdämmung (Vakuum)"}, "AEROGEL_4_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "AEROGEL_1_CM_2025": {"label": "Hochmoderne, extrem dünne Dämmung (Aerogel)", "desc": "z.<PERSON><PERSON>, extrem dünne Dämmung (Aerogel)"}, "FIBERGLASS_INSULATION_30_CM_2025": {"label": "Glasfaser-Dämmung", "desc": "z.B. Glasfaser-Dämmung"}}}, "BASEMENT_MOISTURE_PROTECTION": {"label": "<PERSON>", "badge": "Feuchteschutz", "values": {"DEFAULT": {"label": "", "desc": "z.B. Bitumendickbeschichtung oder Horizontalsperre"}}}}}, "DOOR": {"label": "<PERSON><PERSON><PERSON>", "measures": {"DOORS_REPLACEMENT": {"label": "<PERSON><PERSON><PERSON>", "badge": "<PERSON><PERSON><PERSON>", "values": {"DEFAULT": {"label": "", "desc": "z.B. wärmegedämmte Haustür mit Mehrfachverriegelung (mind. CR2)"}}}, "DOORS_SECURITY_FEATURES": {"label": "Türsicherungen installieren", "badge": "Sicherheit", "values": {"DEFAULT": {"label": "", "desc": "z.B. Wechsel der Zylinderanlage und Querriegel- oder Stangenschloss"}}}}}, "HEATING_SYSTEM": {"label": "Heizsystem", "measures": {"HEATING_PRIMARY_SOURCE": {"label": "<PERSON><PERSON><PERSON><PERSON>", "badge": "Heizung", "values": {"DISTRICT_HEATING_2025": {"label": "Fernwärme", "desc": "Anschluss an lokales Fernwärmenetz mit Übergabestation", "badge": "Fernwärme"}, "GROUND_TO_WATER_HEAT_PUMP_2025": {"label": "Sole-Wasser-Wärmepumpe", "desc": "Erdsondenbohrung und Wärmepumpe mit Solekreislauf", "badge": "Sole-Wasser-Wärmepumpe"}, "BIOMASS_HEATING_2025": {"label": "Biomasseheizung", "desc": "Pelletkessel mit automatischer Beschickung, Feststoff-Brennkessel", "badge": "Biomasseheizung"}, "AIR_TO_WATER_HEAT_PUMP_2025": {"label": "Luft-Wasser-Wärmepumpe", "desc": "Splitgerät mit Außen- und Inneneinheit", "badge": "Luft-Wasser-Wärmepumpe"}}}, "HEATING_VENTILATION_HEAT_EXCHANGE": {"label": "Lüftung mit Wärmerückgewinnung", "badge": "Lüftung mit Wärmerückgewinnung", "values": {"HEAT_EXCHANGER": {"label": "", "desc": "z.B. zentrale Lüftungsanlage mit Wärmetauscher"}}}}}, "SURFACE_HEATING": {"label": "Flächenheizung", "measures": {"UNDERFLOOR_HEATING": {"label": "Fußbodenheizung", "badge": "Fußbodenheizung", "values": {"WET_SYSTEM_WITH_SCREED": {"label": "Nasssystem inkl. Estrich und neuem Bodenbelag", "desc": "z.B. Wassergeführtes Rohrsystem im Zementestrich mit neuem Parkett oder Fliesen", "badge": "Nasssystem inkl. Estrich und neuem Bodenbelag"}, "DRY_SYSTEM_WITH_FLOORING": {"label": "Trockensystem inkl. neuem Bodenbelag", "desc": "z.B. Trockenbauelemente mit Rohrkanälen und neuem Fertigparkett", "badge": "Trockensystem inkl. neuem Bodenbelag"}, "DRY_SYSTEM_WITHOUT_FLOORING": {"label": "Trockensystem ohne neuen Bodenbelag", "desc": "z.B. Einbau unter vorhandenen Bodenbelag", "badge": "Trockensystem ohne neuen Bodenbelag"}}}, "WALL_HEATING": {"label": "Wandh<PERSON><PERSON>ng", "badge": "Wandh<PERSON><PERSON>ng", "values": {"WET_SYSTEM_WITH_RECONSTRUCTION": {"label": "Nasssystem inkl. Wandsanierung", "desc": "z.B. Verputztes Rohrregister auf tragender Innenwand mit Putz", "badge": "Nasssystem inkl. Wandsanierung"}, "DRY_SYSTEM_WITH_RECONSTRUCTION": {"label": "Trockensystem inkl. Wandsanierung", "desc": "z.B. Trockenbauplatten mit integriertem Rohrsystem und Wandaufbau", "badge": "Trockensystem inkl. Wandsanierung"}}}, "CEILING_HEATING": {"label": "Deckenheizung", "badge": "Deckenheizung", "values": {"DRY_SYSTEM_WITH_RECONSTRUCTION": {"label": "Trockensystem inkl. Deckensanierung", "desc": "z.B. Heizpaneele in abgehängter Decke mit Anpassung des Deckenaufbaus", "badge": "Trockensystem inkl. Deckensanierung"}}}}}, "RADIATOR_UPGRADE": {"label": "Heizkörper er<PERSON>uern", "measures": {"BASIC_EFFICIENCY": {"label": "Basiseffizienz", "badge": "Basiseffizienz", "values": {"DEFAULT": {"label": "", "desc": "z.B. Austausch durch moderne Flachheizkörper"}}}, "HIGH_PERFORMANCE": {"label": "Höchstleistung", "badge": "Höchstleistung", "values": {"DEFAULT": {"label": "", "desc": "z.B. Austausch durch hochwirkende Heizkörper mit großer Oberfläche"}}}}}, "SOLAR_PANELS": {"label": "PV-Anlage", "measures": {"BATTERY_STORAGE": {"label": "Batteriespeicher", "badge": "Batteriespeicher", "values": {"LITHIUM_ION_BATTERY": {"label": "Batteriespeicher", "desc": "z.B. Lithium-Ionen-Batteriespeicher"}}}, "IMMERSION_HEATER": {"label": "Heizstab", "badge": "Heizstab", "values": {"WITH_BUFFER": {"badge": "<PERSON><PERSON> ne<PERSON>m Warmwasserspeicher", "label": "<PERSON><PERSON> ne<PERSON>m Warmwasserspeicher", "desc": "z.B. Erneuerung des existierenden Warmwasserspeicher mit elektrischem Heizstab"}, "WITHOUT_BUFFER": {"badge": "<PERSON><PERSON> neuen Warmwasserspeicher", "label": "<PERSON><PERSON> neuen Warmwasserspeicher", "desc": "z.B. elektrischer Heizstab für existierenden Warmwasserspeicher"}}}, "SOLAR_PANELS": {"label": "PV-Anlage", "badge": "PV-Anlage", "values": {"DEFAULT": {"label": "PV-Anlage", "desc": "z.B. monokristalline Solarmodule mit Wechselrichter"}}}}}, "SMART_HOME_ENERGY_MANAGEMENT": {"label": "Smart-Home Energiemanagement", "measures": {"INTELLIGENT_HEATING_CONTROLS": {"label": "Intelligente Heizungssteuerung", "badge": "Intelligente Heizungssteuerung", "values": {"DEFAULT": {"label": "", "desc": "z.B. smarte Thermostate und zentrale Heizungssteuerung inkl. Erneuerung BUS-System"}}}, "LIGHTING_AUTOMATION": {"label": "Automatisierte Beleuchtung", "badge": "Automatisierte Beleuchtung", "values": {"DEFAULT": {"label": "", "desc": "z.B. Bewegungsmelder oder tageslichtabhängige Steuerung"}}}}}, "ELECTRIC_VEHICLE_CHARGING": {"label": "E-Auto-Ladestation", "measures": {"WALLBOX_INSTALLATION": {"label": "Installation einer Wallbox", "badge": "Installation einer Wallbox", "values": {"DEFAULT": {"label": "", "desc": "z.B. 11kW Wallbox mit Typ-2-<PERSON><PERSON><PERSON>"}}}, "GRID_CONNECTION_UPGRADE": {"label": "Erweiterung des Netzanschlusses", "badge": "Erweiterung des Netzanschlusses", "values": {"DEFAULT": {"label": "", "desc": "z.B. Anpassung auf höhere Leistung (z.B. 63A) durch Netzbetreiber"}}}}}, "AGE_APPROPRIATE_LIVING": {"label": "Barrierefreies Wohnen", "measures": {"BARRIER_FREE_BATHROOM": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": {"DEFAULT": {"label": "", "desc": "z.B. bodengleiche Dusche mit Haltegriffen, Haltegriffe für Toilette, barrierefreier Badezimmereingang"}}}, "STAIRLIFT": {"label": "<PERSON><PERSON><PERSON>nlift", "badge": "<PERSON><PERSON><PERSON>nlift", "values": {"DEFAULT": {"label": "", "desc": "z.B. Plattform- oder Sitzlift für gerade Treppen über alle Etagen"}}}, "WIDENING_DOORS": {"label": "Türverbreiterung", "badge": "Türverbreiterung", "values": {"DEFAULT": {"label": "", "desc": "z<PERSON><PERSON><PERSON> von 70cm- auf 90cm-Türbreite inkl. Aufstemmen und neuen Türen"}}}}}}, "measuresBadgesValues": {"ADVANCED": "Hohe Qualität", "BASIC": "Kostenoptimiert", "DEFAULT": "Standard"}, "buildingRenovation": {"title": "Modernisierung Gebäude"}, "heatingAndVentilation": {"title": "Heizung und Lüftung"}, "energyAndComfort": {"title": "Energie und Komfort"}}, "cta": {"exportPdf": "Als PDF exportieren", "editBuildingData": "Gebäudedaten anpassen", "applyRenovations": "<PERSON><PERSON><PERSON>", "calculating": "Be<PERSON><PERSON>nen ...", "sendToBaufinex": "An Baufinex senden"}, "results": {"profitability": "Rentabilität", "energyIndex": "Energieindex", "totalProfit": "Gesamt", "energyCostSaved": "Energieersparnis über 10 Jahre", "propertyValueIncrease": "Immobilienwertsteigerung", "renovationCost": "Modernisierungskosten", "primaryEnergyDemand": "<PERSON>rim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalEnergyDemandPerArea": "Endenergiebedarf", "co2Emission": "CO2-Emission", "solarEnergyProd": "Solarstromproduktion"}, "primaryEnergyDemand": {"current": "Aktuell {{value}}", "new": "Neu {{value}}"}, "measures": {"title": "<PERSON><PERSON><PERSON><PERSON>", "table": {"measures": "Maßnahmen", "grant": "Potenzielle Förderprogramme", "cost": "Geschätzte Kosten", "expectedValue": "EW", "recommended": "Empf.", "na": "Nicht verfügbar"}, "summary": {"selected": "Ausgewählte Maßnahmen", "possible": "Mögliche Maßnahmen"}}}, "calculationList": {"title": "Berechnungen", "cta": {"newCalculation": "Neue Berechnung"}, "searchPlaceholder": "<PERSON><PERSON>", "table": {"date": "Datum", "address": "<PERSON><PERSON><PERSON>", "efficiency": "Effizienz", "efficiencyUnit": "kWh/m²/Jahr", "score": "Score"}}, "buildingForm": {"cta": {"continue": "<PERSON><PERSON>", "calculating": "Be<PERSON><PERSON>nen ..."}, "formErrors": {"roofFloorReqFloors": "Die aktuelle Dachkonfiguration erfordert mindestens {{min}} Vollgeschosse", "areaTooSmallForConfiguration": "Die Fläche ist zu klein für die aktuelle Stockwerks- und Dachkonfiguration. Sie sollte mindestens {{min}} betragen."}, "buildingInfo": {"title": "Gebäudeinformationen", "shapeQue": "Welche Form hat das Gebäude?", "shapeDsc": "Betrachten Sie nur den beheizten Teil", "positionQue": "Welche Position hat das Gebäude?", "streetQue": "Adresse und Hausnummer", "streetPlaceholder": "Straße", "noPlaceholder": "Nr.", "zipCodeQue": "Postleitzahl und Stadt", "zipPlaceholder": "PLZ", "cityPlaceholder": "Ort", "constructionYearQue": "<PERSON><PERSON><PERSON><PERSON>", "constructionYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "areaQue": "Wie groß ist die Wohnfläche des Gebäudes?", "areaPlaceholder": "Fläche eingeben", "floorsQue": "Wie viele Vollgeschosse hat das Gebäude?", "floorDsc": "Geben Sie alle Stockwerke mit mindestens 2 m Raumhöhe an, <PERSON> ausgenommen", "floorHeightQue": "Wie hoch sind die Stockwerke (Vollgeschosse)?", "tenantsQue": "Wie viele Parteien leben im Gebäude?"}, "basement": {"title": "<PERSON>", "typeQue": "<PERSON><PERSON> Kellertyp hat das Gebäude?", "insulationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Dämmjahr", "insulationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "basementInsulatedQue": "Ist der Keller gedämmt?", "groundFloorInsulatedQue": "Ist der der Erdgeschossboden gedämmt?", "basementInsulationYearQue": "Jahr der Kellerbodendämmung", "groundFloorInsulationYearQue": "Jahr der Erdgeschossbodendämmung"}, "heating": {"title": "Heizung", "primaryHeatingQue": "Welche Heizungsart hat das Gebäude?", "primaryHeatingInstallationYearQue": "Installationsjahr der Heizung", "primaryHeatingInstallationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Installationsjahr", "primaryHeatingInstallationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "isWaterHeatingDifferent": "Unterscheidet sich die Warmwasserbereitung von der Gebäudeheizung?", "waterHeatingQue": "Wählen Sie die Art der Warmwasserbereitung", "floorHeatingQue": "Gibt es eine Fußbodenheizung?", "solarHeatingQue": "Ist eine Solarthermieanlage installiert?"}, "facade": {"title": "Außenwände", "insulatedQue": "Sind die Außenwände gedämmt?", "insulationYearQue": "Jahr der Wanddämmung", "insulationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Dämmjahr", "insulationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>"}, "roof": {"title": "<PERSON><PERSON>", "floorQue": "Welche Art von Dachboden hat das Gebäude?", "floorOptionsSublabels": {"FULLY_LIVEABLE_REDUCED": "Reduzierte Höhe", "FULLY_LIVEABLE_ELEVATED": "Hohes Dachgeschoss"}, "floorOptionsDescriptions": {"FULLY_LIVEABLE_REDUCED": "Die Kniestockhöhe beträgt 1-2 m", "FULLY_LIVEABLE_ELEVATED": "Die Kniestockhöhe beträgt ≥2 m"}, "ceilingOrRoofInsulatedQue": "Ist die oberste Geschossdecke oder das Dach gedämmt?", "insulationYearQue": "Jahr der Dämmung", "insulationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Dämmjahr", "insulationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "hasSolarPlantQue": "Ist bereits eine PV-Anlage installiert?", "solarPlantPlaceholder": "Leistung in kWp eingeben", "solarPlantPlaceholderDsc": "Geben Sie die Nennleistung der PV-Anlage an", "solarPlantInstallationYearQue": "Installationsjahr der PV-Anlage", "solarPlantInstallationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Installationsjahr", "solarPlantInstallationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "eligibleForSolarQue": "Ist das Dach für PV-Anlage geeignet?"}, "windows": {"title": "<PERSON><PERSON>", "windowsToWallRatioQue": "Wie groß ist die Fensterfläche im Gebäude?", "windowsToWallRatioDsc": {"LOW": "Wenige Fensterflächen", "MEDIUM": "Durchschnittlich große Fensterflächen", "HIGH": "Viele Fenster oder bodentiefe Fenster, Fassade mit großen Fensterflächen"}, "glazingQue": "Welche Verglasung haben die Fenster?", "installationYearQue": "Einbaujahr der Fenster", "installationYearDsc": "<PERSON><PERSON> kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Einbaujahr", "installationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>", "shuttersQue": "Welche Art von Rollläden haben die Fenster?"}, "electricalEquipment": {"title": "Elektrische Ausstattung", "typeQue": "Welche Art von elektrischer Ausstattung hat das Gebäude?", "acInstallationYearQue": "Installationsjahr der Klimaanlage", "acInstallationYearDsc": "<PERSON>n kein Wert angegeben wird, gilt das Baujahr des Gebäudes als Installationsjahr", "acInstallationYearPlaceholder": "<PERSON><PERSON><PERSON> <PERSON>"}}, "calculationReview": {"tabs": {"RESULTS": "Ergebnisse", "BUILDING_INFO": "Gebäudeinformationen"}, "buildingReview": {"common": {"sameAsConstructionYear": "<PERSON><PERSON>", "notProvided": "Nicht angegeben", "na": "Nicht verfügbar"}, "buildingInfo": {"title": "Gebäudeinformationen", "shape": "Gebäudestruktur", "position": "Gebäudeposition", "constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "area": "Fläche", "floors": "Anzahl der Vollgeschosse", "floorHeight": "Stockwerkhöhe", "tenants": "Anzahl der Parteien"}, "basement": {"title": "<PERSON>", "type": "Kellertyp", "groundFloorInsulated": "Erdgeschossboden gedämmt", "groundFloorInsulationYear": "Jahr der Erdgeschossbodendämmung", "basementInsulationYear": "Jahr der Kellerbodendämmung", "basementInsulated": "<PERSON>"}, "heating": {"title": "Heizung", "primaryHeating": "Heizungsart", "primaryHeatingInstallationYear": "Installationsjahr", "waterHeating": "Art der Warmwasserbereitung", "floorHeating": "Fußbodenheizung", "solarHeating": "Solarthermieanlage vorhanden"}, "facade": {"title": "Außenwände", "insulated": "Gedämmte Wände", "insulationYear": "Dämmungsjahr"}, "roof": {"title": "<PERSON><PERSON>", "floor": "Dachgeschosstyp", "floorOptions": {"FULLY_LIVEABLE_REDUCED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit reduzierter Höhe", "FULLY_LIVEABLE_ELEVATED": "<PERSON><PERSON><PERSON><PERSON>, bewohntes Dachgeschoss mit voller Höhe", "COLD_STORAGE": "<PERSON><PERSON><PERSON><PERSON>, unbewohntes Dachgeschoss", "PARTIALLY_LIVEABLE": "<PERSON><PERSON><PERSON><PERSON>"}, "ceilingOrRoofInsulated": "Geschossdecke oder das Dach gedämmt", "insulationYear": "Dämmungsjahr", "hasSolarPlant": "Installierte PV-Anlage", "solarPlantInstallationYear": "Installationsjahr der PV-Anlage", "solarPlantPower": "Leistung der PV-Anlage", "eligibleForSolar": "Dach für PV-<PERSON>lage geeignet"}, "windows": {"title": "<PERSON><PERSON>", "windowsToWallRatio": "Fensterflächenanteil", "windowsToWallRatioOptions": {"LOW": "Wenige Fensterflächen", "MEDIUM": "Durchschnittlich große Fensterflächen", "HIGH": "Viele Fenster oder bodentiefe Fenster, Fassade mit großen Fensterflächen"}, "glazing": "Verglasung", "installationYear": "Einbaujahr", "shutters": "Art der Rollläden"}, "electricalEquipment": {"title": "Elektrische Ausstattung", "type": "Ausstattungstyp", "acInstallationYear": "Installationsjahr der Klimaanlage"}}, "cta": {"recalculate": "Berechnung erneut durch<PERSON>hren", "exportToPdf": "Als PDF exportieren"}}}}