import { CheckIcon, Radio, RadioProps } from "@mantine/core";

import { CommonProps } from "@shared/types/react-utils.ts";

type Props = CommonProps &
  RadioProps & {
    isNoneTypeInput?: boolean;
  };

export const EpcCategoryRadio = ({
  className,
  isNoneTypeInput,
  ...radioProps
}: Props) => {
  const noneTypeStyleProps = { iconColor: "black", color: "gray.3" };
  const regularTypeStyleProps = { iconColor: "white", color: "indigo" };

  return (
    <Radio
      icon={CheckIcon}
      {...(isNoneTypeInput ? noneTypeStyleProps : regularTypeStyleProps)}
      {...radioProps}
      className={className}
    />
  );
};
