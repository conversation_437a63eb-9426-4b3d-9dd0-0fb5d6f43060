import { Accordion, Avatar, Group, Text } from "@mantine/core";
import { ReactNode } from "react";

import { CommonProps } from "../../types/react-utils.ts";

type Props = CommonProps & {
  item: {
    id: string;
    label: string;

    description: string;
    Icon: ReactNode;
    element: ReactNode;

    cost?: string;
  };
};

export const EpcCategoryAccordionItem = ({
  item: { label, Icon, description, element, id },
}: Props) => {
  return (
    <Accordion.Item value={id} key={label}>
      <Accordion.Control>
        <Group wrap="nowrap">
          <Avatar color="indigo" radius="sm">
            {Icon}
          </Avatar>

          <div>
            <Text>{label}</Text>
            <Text size="sm" c="dimmed" fw={400}>
              {description}
            </Text>
          </div>
        </Group>
      </Accordion.Control>
      <Accordion.Panel>{element}</Accordion.Panel>
    </Accordion.Item>
  );
};
