import { Paper, Stack } from "@mantine/core";
import { ReactNode } from "react";

import { EpcCategorySeparator } from "@shared/components/epc-category/epc-category-separator.tsx";

import classes from "./epc-category-before-after-layout.module.css";

type Props = {
  before: ReactNode;
  after: ReactNode;
};

export const EpcCategoryBeforeAfterLayout = ({ before, after }: Props) => {
  return (
    <Paper p="xl" radius="md">
      <div className={classes.epcCategoryBeforeAfterLayout}>
        <Stack
          gap="xs"
          mt="md"
          classNames={{
            root: classes.epcCategoryBeforeAfterLayoutColumn,
          }}
        >
          {before}
        </Stack>
        <EpcCategorySeparator />
        <Stack
          gap="xs"
          mt="md"
          classNames={{
            root: classes.epcCategoryBeforeAfterLayoutColumn,
          }}
        >
          {after}
        </Stack>
      </div>
    </Paper>
  );
};
