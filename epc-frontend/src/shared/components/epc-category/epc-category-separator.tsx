import { IconChevronsRight } from "@tabler/icons-react";
import { ReactNode } from "react";

import classes from "./epc-category-separator.module.css";

type Props = {
  icon?: ReactNode;
};

export const EpcCategorySeparator = ({ icon }: Props) => {
  const Icon = icon || (
    <IconChevronsRight className={classes.epcCategorySeparatorIcon} />
  );
  return (
    <div className={classes.epcCategorySeparator}>
      <div className={classes.epcCategorySeparatorIconWrapper}>{Icon}</div>
    </div>
  );
};
