export type ObjectPaths<T> = {
  [K in keyof T]: T[K] extends Record<string, unknown> // Check if it's an object
    ? `${K & string}` | `${K & string}.${ObjectPaths<T[K]>}` // If so, add paths recursively
    : `${K & string}`; // Otherwise, use the key as-is
}[keyof T];

export type PathValue<
  T,
  P extends string,
> = P extends `${infer K}.${infer Rest}` // Split P into two parts: K and Rest
  ? K extends keyof T // Ensure K is a key in T
    ? PathValue<T[K], Rest> // Recurse into the type with T[K] and the remaining path
    : never // Invalid path
  : P extends keyof T // If no more dots in the path, check if P is a key in T
    ? T[P] // Return the type of the key
    : never; // Invalid path

export type ToNoneOptionalProperties<T> = {
  [K in keyof T]-?: T[K];
};

export type DeepPartial<T> = {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  [K in keyof T]?: T[K] extends Function
    ? T[K]
    : T[K] extends Array<infer U>
      ? Array<DeepPartial<U>>
      : T[K] extends object
        ? DeepPartial<T[K]>
        : T[K];
};
