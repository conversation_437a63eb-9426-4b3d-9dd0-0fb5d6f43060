import { create<PERSON>rowser<PERSON>outer } from "react-router-dom";

import { OidcSingOutCallbackRoute } from "@providers/auth/oidc-sign-out-callback-route.tsx";
import { FinPageError } from "@shared/components/page/fin-page-error.tsx";

import { CalculationListPage } from "../../pages/calculation-list/calculation-list-page.tsx";
import { CalculationBuildingPage } from "../../pages/calculation/building/calculation-building-page.tsx";
import { CalculationResultPage } from "../../pages/calculation/result/calculation-result-page.tsx";
import { CalculationReviewPage } from "../../pages/calculation/review/calculation-review-page.tsx";
import { HomePage } from "../../pages/home/<USER>";
import { OidcSingInCallbackRoute } from "../auth/oidc-sing-in-callback-route.tsx";

export const routes = {
  home: {
    parse: () => "/",
    def: "/",
  },
  oidcCallback: {
    parse: () => "/oidc-callback",
    def: "/oidc-callback",
  },
  logout: {
    parse: () => "/logout",
    def: "logout",
  },
  calculation: {
    building: {
      parse: (prototypeBuildingId?: string) =>
        `/calculation/building${prototypeBuildingId ? `/${prototypeBuildingId}` : ""}`,
      def: "calculation/building/:prototypeBuildingId?",
    },
    result: {
      parse: (buildingId: string) => `/calculation/result/${buildingId}`,
      def: "calculation/result/:buildingId",
    },
    review: {
      parse: (buildingId: string) => `/calculation/review/${buildingId}`,
      def: "calculation/review/:buildingId",
    },
  },
};

export const router = createBrowserRouter([
  {
    path: routes.oidcCallback.def,
    element: <OidcSingInCallbackRoute />,
  },
  {
    path: routes.logout.def,
    element: <OidcSingOutCallbackRoute />,
  },
  {
    path: routes.home.def,
    element: <HomePage />,
    children: [
      {
        index: true,
        element: <CalculationListPage />,
        errorElement: <FinPageError />,
      },
      {
        path: routes.calculation.building.def,
        element: <CalculationBuildingPage />,
        errorElement: <FinPageError goBackRoute={"/"} />,
      },
      {
        path: routes.calculation.result.def,
        element: <CalculationResultPage />,
        errorElement: <FinPageError goBackRoute={"/"} />,
      },
      {
        path: routes.calculation.review.def,
        element: <CalculationReviewPage />,
        errorElement: <FinPageError goBackRoute={"/"} />,
      },
    ],
  },
]);
