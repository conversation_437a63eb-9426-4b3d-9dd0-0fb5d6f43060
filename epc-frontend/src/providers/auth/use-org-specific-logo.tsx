import { useMemo } from "react";

import DeutscheBankRawLogo from "@shared/icons/logos/DeutscheBankLogo.svg?raw";
import DeutscheBankLogo from "@shared/icons/logos/DeutscheBankLogo.svg?react";
import VBRawLogo from "@shared/icons/logos/VBLogo.svg?raw";
import VBL<PERSON> from "@shared/icons/logos/VBLogo.svg?react";
import VBSWWRawLogo from "@shared/icons/logos/VBSWWLogo.svg?raw";
import VBSWWLogo from "@shared/icons/logos/VBSWWLogo.svg?react";
import VRBankLausitzRawLogo from "@shared/icons/logos/VRBankLausitzLogo.svg?raw";
import VRBank<PERSON>ausitzLogo from "@shared/icons/logos/VRBankLausitzLogo.svg?react";

const logoPerOrg = new Map([
  [
    "DeutscheBank",
    {
      LogoComponent: DeutscheBankLogo,
      raw: {
        logo: DeutscheBankRawLogo,
        height: 35,
        width: 169,
      },
    },
  ],
  [
    "Finacte",
    {
      LogoComponent: VBLogo,
      raw: {
        logo: VBRawLogo,
        height: 417,
        width: 608,
      },
    },
  ],
  [
    "VRBankSWW",
    {
      LogoComponent: VBSWWLogo,
      raw: {
        logo: VBSWWRawLogo,
        height: 50,
        width: 315,
      },
    },
  ],
  [
    "VRBankLausitz",
    {
      LogoComponent: VRBankLausitzLogo,
      raw: {
        logo: VRBankLausitzRawLogo,
        height: 50,
        width: 315,
      },
    },
  ],
]);

const DEFAULT_LOGO_SETUP = {
  LogoComponent: VBLogo,
  raw: {
    logo: VBRawLogo,
    height: 417,
    width: 608,
  },
};

export const useOrgSpecificLogo = (org?: string) => {
  return useMemo(() => {
    if (!org) return { LogoComponent: undefined, raw: undefined };

    const logoConfig = logoPerOrg.get(org);

    return logoConfig ? logoConfig : DEFAULT_LOGO_SETUP;
  }, [org]);
};
