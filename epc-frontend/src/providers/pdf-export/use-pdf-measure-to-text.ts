import { useCallback } from "react";
import { useTranslation } from "react-i18next";

import { RenovationDto } from "@data-access/renovation/renovation-dto.ts";
import {
  RenovationMeasureType,
  RenovationMeasureVariant,
} from "@data-access/renovation/renovation-types";

export enum PdfTextMode {
  VARIANT = "VARIANT",
  TYPE = "TYPE",
  VALUE = "VALUE",
}

const i18nKeyStrategy = {
  [PdfTextMode.VARIANT]: (measure: RenovationDto) =>
    `dataAccess.renovationMeasureVariant.${measure.renovationMeasureVariant}`,
  [PdfTextMode.TYPE]: (measure: RenovationDto) =>
    `pdf.measures.${measure.renovationMeasureType}.label`,
  [PdfTextMode.VALUE]: (measure: RenovationDto) =>
    `pdf.measures.${measure.renovationMeasureType}.values.${measure.renovationMeasureValue}`,
};

const measureTypesWithValueText = new Set([
  RenovationMeasureType.HEATING_PRIMARY_SOURCE,
  RenovationMeasureType.IMMERSION_HEATER,
]);

export const usePdfMeasureToText = () => {
  const { t } = useTranslation();

  return useCallback(
    (measure: RenovationDto) => {
      const categoryLabel = t(
        `pdf.categories.${measure.renovationMeasureCategory}`
      );
      const baseLabel = t(i18nKeyStrategy[PdfTextMode.TYPE](measure));
      const valueLabel = t(i18nKeyStrategy[PdfTextMode.VALUE](measure));
      const variantLabel = t(i18nKeyStrategy[PdfTextMode.VARIANT](measure));

      if (
        measure.renovationMeasureVariant !== RenovationMeasureVariant.DEFAULT
      ) {
        return `${categoryLabel} - ${baseLabel} (${variantLabel})`;
      }

      if (measureTypesWithValueText.has(measure.renovationMeasureType)) {
        return `${categoryLabel} - ${baseLabel} (${valueLabel})`;
      }

      return `${categoryLabel} - ${baseLabel}`;
    },
    [t]
  );
};
