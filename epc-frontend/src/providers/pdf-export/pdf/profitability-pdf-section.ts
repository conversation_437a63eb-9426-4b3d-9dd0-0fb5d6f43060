import { jsPDF } from "jspdf";
import autoTable, { CellInput, Styles } from "jspdf-autotable";

import { CalculationResultDto } from "@data-access/calculation/calculation-dto.ts";
import { CalculationProfitabilityDto } from "@data-access/renovation/renovation-dto.ts";
import { PdfCursor } from "@providers/pdf-export/pdf/pdf-utils/pdf-cursor.ts";
import { formatDecimal } from "@shared/utils/value-formatters.ts";

import { PAGE_CONTENT_WIDTH, PAGE_X_MARGIN } from "./pdf-utils/global-sizes.ts";
import { getGlobalStyles } from "./pdf-utils/global-styles.ts";

const MARGIN_TOP = 7;

type ProfitabilityLabels = {
  totalProfit: string;
  propertyValueIncrease: string;
  energyCostSaved: string;
  cost: string;
  energyEfficiency: string;
  finalEnergyDemand: string;
  co2emission: string;
  solarProduction: string;

  demandUnit: string;
  co2emissionUnit: string;
  solarProductionUnit: string;
};

export const preparePaintProfitabilitySection =
  (doc: jsPDF, cursor: PdfCursor, ln: string) =>
  (
    result: CalculationResultDto,
    profitability: CalculationProfitabilityDto,
    t: ProfitabilityLabels
  ) => {
    const y = cursor.getY();
    const {
      colors: { textGray, basicText, greenText },
    } = getGlobalStyles();

    const headerCellConfig: CellInput = {
      colSpan: 2,
      styles: {
        fontSize: 10,
        textColor: textGray,
        cellPadding: { left: 0, right: 0, bottom: 3 },
      } as Styles,
    };

    const valueCellConfig: CellInput = {
      styles: {
        textColor: basicText,
        fontSize: 11,
        cellWidth: 22,
        cellPadding: { bottom: 5, right: 2 },
      },
    };

    const unitCellConfig: CellInput = {
      styles: {
        textColor: textGray,
        fontSize: 9,
        cellPadding: { bottom: 5.5 },
        valign: "bottom",
        cellWidth: 25.5,
      },
    };

    autoTable(doc, {
      useCss: true,
      theme: "plain",
      body: [
        [
          {
            content: t.totalProfit,
            ...headerCellConfig,
          },
          { content: t.propertyValueIncrease, ...headerCellConfig },
          { content: t.energyEfficiency, ...headerCellConfig },
          { content: t.finalEnergyDemand, ...headerCellConfig },
        ],
        [
          {
            content: formatDecimal(ln, profitability.totalProfit),
            styles: {
              textColor: greenText,
              fontStyle: "bold",
              valign: "bottom",
              fontSize: 11,
              cellWidth: 22,
              cellPadding: { bottom: 5, right: 2 },
            },
          },
          { content: "EUR", ...unitCellConfig },
          {
            content: formatDecimal(ln, profitability.propertyValueIncrease),
            ...valueCellConfig,
          },
          { content: "EUR", ...unitCellConfig },
          {
            content: formatDecimal(ln, result.energyEfficiency),
            ...valueCellConfig,
          },
          { content: t.demandUnit, ...unitCellConfig },
          {
            content: formatDecimal(ln, result.finalEnergyDemandPerArea),
            ...valueCellConfig,
          },
          { content: t.demandUnit, ...unitCellConfig },
        ],
        [
          { content: t.energyCostSaved, ...headerCellConfig },
          { content: t.cost, ...headerCellConfig },
          { content: t.co2emission, ...headerCellConfig },
          { content: t.solarProduction, ...headerCellConfig },
        ],
        [
          {
            content: formatDecimal(ln, profitability.energyCostSaved),
            ...valueCellConfig,
          },
          { content: "EUR", ...unitCellConfig },
          {
            content: formatDecimal(ln, profitability.cost),
            ...valueCellConfig,
          },
          { content: "EUR", ...unitCellConfig },
          {
            content: formatDecimal(ln, result.co2Emissions),
            ...valueCellConfig,
          },
          { content: t.co2emissionUnit, ...unitCellConfig },
          {
            content: formatDecimal(ln, result.solarEnergyProduction),
            ...valueCellConfig,
          },
          { content: t.solarProductionUnit, ...unitCellConfig },
        ],
      ],
      startY: y,
      margin: {
        left: PAGE_X_MARGIN,
        right: PAGE_X_MARGIN,
        top: MARGIN_TOP,
        bottom: 0,
      },
      tableWidth: PAGE_CONTENT_WIDTH,
      didDrawPage: (hookData) => {
        cursor.replaceY(Number(hookData.cursor?.y));
      },
    });
  };
