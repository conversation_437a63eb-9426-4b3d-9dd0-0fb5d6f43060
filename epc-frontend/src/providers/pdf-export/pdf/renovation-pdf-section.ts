import { jsPDF } from "jspdf";
import autoTable, { Cell, CellHookData } from "jspdf-autotable";

import { PdfCursor } from "@providers/pdf-export/pdf/pdf-utils/pdf-cursor.ts";

import {
  PAGE_CONTENT_WIDTH,
  PAGE_X_MARGIN,
  withPageXMargin,
} from "./pdf-utils/global-sizes.ts";
import {
  getGlobalStyles,
  resetGlobalStyles,
} from "./pdf-utils/global-styles.ts";
import { PdfMeasuresData } from "./types.ts";

const MARGIN_TOP = 7;
const CELL_PADDING = 2;
const MAX_LINES_PER_CEL = 4;
const LINE_GAP = 1;
const EMPTY_SECTION_HEIGHT = 20;
const MORE_RENOVATIONS_SECTION_HEIGHT = 18;

type RenovationLabels = {
  measure: string;
  noMeasuresSelected: string;
  moreRenovations: string;
  grant: string;
  price: string;
};

export const preparePaintRenovationSection = (
  doc: jsPDF,
  cursor: PdfCursor,
) => {
  const paintDoubleText = preparePaintDoubleTextCell(doc);
  const paintSingleText = preparePaintSingleTextCell(doc);
  const paintEmptySection = preparePaintEmptyTableSection(doc);
  const paintRemainingTransactions = preparePaintRemainingTransactions(doc);

  return (
    measures: PdfMeasuresData[],
    {
      price,
      measure,
      noMeasuresSelected,
      moreRenovations,
      grant,
    }: RenovationLabels,
    maxRenovationMeasures?: number,
  ) => {
    const y = cursor.getY();

    if (measures.length === 0) {
      const endY = paintEmptySection(noMeasuresSelected, y);
      cursor.replaceY(endY);
    } else {
      const displayedMeasures = measures.slice(0, maxRenovationMeasures);

      autoTable(doc, {
        theme: "plain",
        head: [[measure, grant, price]],
        body: displayedMeasures.map(({ renovation, grant, price }) => {
          return [
            [renovation.text, renovation.dsc],
            [grant.text, grant.dsc],
            [price],
          ];
        }),

        startY: y,
        margin: {
          left: PAGE_X_MARGIN,
          right: PAGE_X_MARGIN,
          top: MARGIN_TOP,
          bottom: 0,
        },
        tableWidth: PAGE_CONTENT_WIDTH,
        columnStyles: {
          0: {
            cellWidth: PAGE_CONTENT_WIDTH * 0.35,
          },
          1: {
            cellWidth: PAGE_CONTENT_WIDTH * 0.4,
          },
          2: {
            cellWidth: PAGE_CONTENT_WIDTH * 0.25,
          },
        },
        willDrawCell: (cellHookData) => {
          cellHookData.cell.text = [];
        },
        didParseCell: (cellHookData) => {
          if (cellHookData.row.section === "body") {
            cellHookData.row.height = 18;
          }
        },
        didDrawCell: (cellHookData) => {
          const { row, cell } = cellHookData;

          if (row.section === "head") {
            paintSingleText(cellHookData);
          } else if (row.section === "body") {
            if ([0, 1].includes(cellHookData.column.index)) {
              paintDoubleText(cellHookData);
            } else {
              paintSingleText(cellHookData);
            }
          }

          const rowHeight = row.height;
          const cellWidth = cell.width;
          doc.line(
            cell.x,
            cell.y + rowHeight,
            cell.x + cellWidth,
            cell.y + rowHeight,
          );
        },
        didDrawPage: (hookData) => {
          let endY = Number(hookData.cursor?.y);
          if (measures.length > displayedMeasures.length) {
            const remainingMeasures = measures.slice(maxRenovationMeasures);

            endY = paintRemainingTransactions(
              moreRenovations,
              remainingMeasures,
              endY,
            );
          }
          cursor.replaceY(endY);
        },
      });
    }

    resetGlobalStyles(doc);
  };
};

const preparePaintEmptyTableSection = (doc: jsPDF) => {
  const findCenteredTextPosition = prepareFindCenteredTextPosition(doc);
  return (emptyTableLabel: string, y: number) => {
    const { font } = getGlobalStyles();
    doc.setFont(font, "italic");
    doc.setFontSize(11);

    const textPos = findCenteredTextPosition(
      emptyTableLabel,
      y,
      EMPTY_SECTION_HEIGHT,
    );

    doc.text(emptyTableLabel, textPos.x, textPos.y, { align: "center" });

    return y + EMPTY_SECTION_HEIGHT;
  };
};

const preparePaintRemainingTransactions = (doc: jsPDF) => {
  const findCenteredTextPosition = prepareFindCenteredTextPosition(doc);
  return (
    moreRenovationsLabel: string,
    remainingMeasures: PdfMeasuresData[],
    y: number,
  ) => {
    const { font } = getGlobalStyles();
    doc.setFont(font, "italic");
    doc.setFontSize(11);

    const measures = remainingMeasures.map(({ renovation: { text } }) => text);

    const line1 = `+${remainingMeasures.length} ${moreRenovationsLabel}`;
    const line2 = measures.join(", ");
    const line1Pos = findCenteredTextPosition(
      line1,
      y,
      MORE_RENOVATIONS_SECTION_HEIGHT,
    );

    const line2Pos = findCenteredTextPosition(
      line2,
      y + line1Pos.lineHeight * 1.1,
      MORE_RENOVATIONS_SECTION_HEIGHT,
    );

    doc.text(line1, withPageXMargin(PAGE_CONTENT_WIDTH / 2), line1Pos.y, {
      align: "center",
    });

    doc.text(line2, line2Pos.x, line2Pos.y, {
      align: "center",
      maxWidth: PAGE_CONTENT_WIDTH,
    });

    return y + MORE_RENOVATIONS_SECTION_HEIGHT;
  };
};

const prepareFindCenteredTextPosition =
  (doc: jsPDF) => (text: string, y: number, displayAreaHeight: number) => {
    const { h: lineHeight } = doc.getTextDimensions(text);
    const textY = y + displayAreaHeight / 2 - lineHeight / 2;
    return {
      x: withPageXMargin(PAGE_CONTENT_WIDTH / 2),
      y: textY,
      lineHeight: lineHeight,
    };
  };

const preparePaintDoubleTextCell = (doc: jsPDF) => (cellData: CellHookData) => {
  const { font } = getGlobalStyles();
  const { cell } = cellData;
  const [mainText, description] = cell.raw as string[];

  // TODO: we might want to adjust the font size
  const mainTextLines = doc.splitTextToSize(
    mainText,
    cell.width - CELL_PADDING,
  ) as string[];
  const descriptionLines = doc.splitTextToSize(
    description,
    cell.width - CELL_PADDING,
  ) as string[];

  // Counting all lines
  const allLinesCount = mainTextLines.length + descriptionLines.length;
  const acceptedLines =
    allLinesCount > MAX_LINES_PER_CEL ? MAX_LINES_PER_CEL : allLinesCount;
  const { h: lineH } = doc.getTextDimensions(mainTextLines[0]);

  const firstLineOffset = calculateFirstLineOffset(cell, lineH, acceptedLines);

  doc.setFont(font, "bold");
  doc.text(mainTextLines.slice(0, 2), getXWithPadding(cell.x), firstLineOffset);

  doc.setFont(font, "normal");
  doc.setFontSize(9);
  doc.text(
    descriptionLines.slice(0, 2),
    getXWithPadding(cell.x),
    firstLineOffset + getTextLinesHeight(lineH, mainTextLines.length),
  );
};

const getTextLinesHeight = (lineHeight: number, lines: number) => {
  return lines * lineHeight + lines * LINE_GAP;
};

const preparePaintSingleTextCell = (doc: jsPDF) => (cellData: CellHookData) => {
  const { cell } = cellData;
  const mainText = cell.raw as string;

  const mainTextLines = doc.splitTextToSize(
    mainText,
    cell.width - CELL_PADDING,
  ) as string[];

  const acceptedLines =
    mainTextLines.length > MAX_LINES_PER_CEL
      ? MAX_LINES_PER_CEL
      : mainTextLines.length;

  const { h: lineH } = doc.getTextDimensions(mainTextLines[0]);

  const firstLineOffset = calculateFirstLineOffset(cell, lineH, acceptedLines);

  doc.text(mainTextLines.slice(0, 2), getXWithPadding(cell.x), firstLineOffset);
};

const calculateFirstLineOffset = (
  cell: Cell,
  lineHeight: number,
  acceptedLines: number,
) => {
  const allLinesHeight = getTextLinesHeight(lineHeight, acceptedLines);

  const remainingCellHeight = cell.height - allLinesHeight;
  const cellYPadding = remainingCellHeight / 2;

  // Offset needs to include line height -> text is printed above (x,y)
  return cell.y + cellYPadding + lineHeight;
};

const getXWithPadding = (x: number) => x + CELL_PADDING;
