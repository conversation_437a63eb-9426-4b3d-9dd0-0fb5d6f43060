import { BuildingDto } from "@data-access/building/building-dto.ts";
import { CalculationResultDto } from "@data-access/calculation/calculation-dto.ts";
import { CalculationProfitabilityDto } from "@data-access/renovation/renovation-dto.ts";

export type ImgPdfData = {
  imgUrl: string;
  widthToHeight: number;
};

export type PdfMeasuresData = {
  renovation: {
    text: string;
    dsc: string;
  };
  grant: {
    text: string;
    dsc: string;
  };
  price: string;
};

export type PdfData = {
  address: string;
  building: BuildingDto;
  currentResult: CalculationResultDto;
  newResult?: CalculationResultDto;
  profitability: CalculationProfitabilityDto;
  measures: PdfMeasuresData[];
};
