import { useTranslation } from "react-i18next";

import { globalApi } from "@data-access/api.ts";
import { Language } from "@data-access/calculation/calculation-dto.ts";
import { CalculationResultCategory } from "@data-access/calculation/calculation-types.ts";
import { formatDecimal } from "@shared/utils/value-formatters.ts";

import { PdfData } from "./pdf/types.ts";

export const useFetchPdfData = () => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language.toUpperCase() as Language;

  return async (buildingId: string): Promise<PdfData> => {
    const [building, calculationResults, measures, profitability] =
      await Promise.all([
        globalApi.building.getBuilding.req(buildingId),
        globalApi.calculation.getCalculationResults.req(buildingId),
        globalApi.renovation.getRenovationMeasures.req(buildingId),
        globalApi.renovation.getProfitability.req(buildingId),
      ]);

    const newResult = calculationResults.find(
      ({ category }) => category === CalculationResultCategory.ALTERNATIVE
    );
    const currentResult = calculationResults.find(
      ({ category }) => category === CalculationResultCategory.CURRENT
    );

    if (!currentResult)
      throw new Error(`[useFetchPdfData] Cannot fetch data for: ${buildingId}`);

    return {
      address: `${building.street} ${building.no}, ${building.zipCode} ${building.city}`,
      building,
      newResult,
      currentResult,
      profitability,
      measures: measures
        .filter(({ selected }) => selected)
        .map(({ measure, measureDsc, grantPrograms, cost }) => {
          const minCost = formatDecimal(i18n.language, cost?.range.first);
          const maxCost = formatDecimal(i18n.language, cost?.range.second);
          return {
            renovation: {
              text: measure[currentLanguage],
              dsc: measureDsc[currentLanguage],
            },
            grant: {
              text: grantPrograms[0][currentLanguage].name,
              dsc: grantPrograms[0][currentLanguage].description,
            },
            price: `${minCost} - ${maxCost}`,
          };
        }),
    };
  };
};
