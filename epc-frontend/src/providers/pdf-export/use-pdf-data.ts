import { useTranslation } from "react-i18next";

import { globalApi } from "@data-access/api.ts";
import { CalculationResultCategory } from "@data-access/calculation/calculation-types.ts";
import { formatDecimal } from "@shared/utils/value-formatters.ts";

import { PdfData } from "./pdf/types.ts";
import { usePdfMeasureToText } from "./use-pdf-measure-to-text.ts";

export const useFetchPdfData = () => {
  const { t, i18n } = useTranslation();
  const pdfMeasureToText = usePdfMeasureToText();

  const na = t("pdf.notAvailable");

  return async (buildingId: string): Promise<PdfData> => {
    const [building, calculationResults, measures, profitability] =
      await Promise.all([
        globalApi.building.getBuilding.req(buildingId),
        globalApi.calculation.getCalculationResults.req(buildingId),
        globalApi.renovation.getRenovationMeasures.req(buildingId),
        globalApi.renovation.getProfitability.req(buildingId),
      ]);

    const newResult = calculationResults.find(
      ({ category }) => category === CalculationResultCategory.ALTERNATIVE
    );
    const currentResult = calculationResults.find(
      ({ category }) => category === CalculationResultCategory.CURRENT
    );

    if (!currentResult)
      throw new Error(`[useFetchPdfData] Cannot fetch data for: ${buildingId}`);

    return {
      address: `${building.street} ${building.no}, ${building.zipCode} ${building.city}`,
      building,
      newResult,
      currentResult,
      profitability,
      measures: measures
        .filter(({ selected }) => selected)
        .map((measure) => {
          const grantText = measure.grantPrograms
            .map((grant) => {
              return t(`dataAccess.grants.${grant.type}.name`);
            })
            .join(" ");

          const estimatedCost = formatDecimal(
            i18n.language,
            measure.cost?.mostProbableValue
          );

          return {
            renovation: {
              text: pdfMeasureToText(measure),
            },
            grant: {
              text: grantText || na,
            },
            price: estimatedCost || na,
          };
        }),
    };
  };
};
