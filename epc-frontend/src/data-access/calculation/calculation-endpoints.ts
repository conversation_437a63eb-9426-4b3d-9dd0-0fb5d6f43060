import { Axios } from "axios";

import { RenovationDto } from "@data-access/renovation/renovation-dto.ts";
import { Page, PaginatedResponse } from "@shared/types/data-utils.ts";

import {
  CalculationListResultResponse,
  CalculationResultDto,
} from "./calculation-dto.ts";

export const initialiseCalculationEndpoints = (api: Axios) => {
  const prefix = "/calculation";

  const inputQueryGroup = "inputQueryGroup";
  const resultQueryGroup = "resultQueryGroup";
  return {
    calculate: {
      req: async (buildingId: string) => {
        const { data } = await api.post<CalculationResultDto>(
          `${prefix}/${buildingId}`
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        inputQueryGroup,
        "calculate",
      ],
      invalidationKey: (buildingId: string) => [prefix, buildingId],
    },
    getCalculationResults: {
      req: async (buildingId: string) => {
        const { data } = await api.get<CalculationResultDto[]>(
          `${prefix}/${buildingId}`
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        resultQueryGroup,
        "getCalculationResults",
      ],
    },

    getAllResults: {
      req: async ({ page, size }: Page, search?: string) => {
        const { data } = await api.get<
          PaginatedResponse<CalculationListResultResponse[]>
        >(`${prefix}/result-list`, {
          params: {
            page,
            size,
            search
          },
        });
        return data
      },
      key: ({ page, size }: Page, search?: string) => [
        prefix,
        resultQueryGroup,
        "getAllResults",
        page,
        size,
        search
      ],
    },
    selectRenovations: {
      req: async (buildingId: string, selectedRenovations: string[]) => {
        const { data } = await api.patch<RenovationDto[]>(
          `${prefix}/${buildingId}/renovation`,
          selectedRenovations
        );
        return data;
      },
      key: (buildingId: string, selectedRenovations: Set<string>) => [
        prefix,
        buildingId,
        selectedRenovations,
        "selectRenovations",
      ],
      invalidationKey: (buildingId: string) => [
        prefix,
        buildingId,
        resultQueryGroup,
      ],
    },
  };
};
