import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { globalApi } from "@data-access/api.ts";
import { CalculationListResultDto } from "@data-access/calculation/calculation-dto.ts";
import { CalculationResultCategory } from "@data-access/calculation/calculation-types.ts";
import { Page, PaginatedResponse } from "@shared/types/data-utils.ts";

const calculationApi = globalApi.calculation;
const renovationApi = globalApi.renovation;
export const useCalculationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (buildingId: string) =>
      calculationApi.calculate.req(buildingId),
    onSuccess: (_, buildingId) => {
      return queryClient.invalidateQueries({
        queryKey: calculationApi.calculate.invalidationKey(buildingId),
      });
    },
  });
};

export const useGetCalculationResultsQuery = (page: Page, search?: string) => {
  return useQuery({
    queryFn: () => calculationApi.getAllResults.req(page, search),
    queryKey: calculationApi.getAllResults.key(page, search),
    select: (data) => {
      const content = data.content.map(({ creationDate, ...rest }) => ({
        ...rest,
        creationDate: new Date(creationDate),
      }));
      return { ...data, content } as PaginatedResponse<
        CalculationListResultDto[]
      >;
    },
  });
};

export const useGetCalculationResultQuery = (buildingId?: string) => {
  return useQuery({
    enabled: buildingId !== undefined,
    queryFn: () => calculationApi.getCalculationResults.req(buildingId!),
    queryKey: calculationApi.getCalculationResults.key(buildingId!),
    select: (data) => {
      return {
        current: data.find(
          ({ category }) => category === CalculationResultCategory.CURRENT
        ),
        future: data.find(
          ({ category }) => category === CalculationResultCategory.ALTERNATIVE
        ),
      };
    },
  });
};

export const useApplyRenovationsMutation = (
  buildingId?: string,
  onSuccess?: () => void
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (selectedRenovations: string[]) =>
      calculationApi.selectRenovations.req(buildingId!, selectedRenovations),
    onSuccess: () => {
      onSuccess?.();
      return Promise.all([
        queryClient.invalidateQueries({
          queryKey: calculationApi.selectRenovations.invalidationKey(
            buildingId!
          ),
        }),
        queryClient.invalidateQueries({
          queryKey: renovationApi.generateRenovations.invalidationKey(
            buildingId!
          ),
        }),
      ]);
    },
  });
};
