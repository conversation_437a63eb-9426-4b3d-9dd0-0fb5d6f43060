import {
  CalculationResultCategory,
  EnergyPerformanceGrade,
  Co2EmissionUnit,
  EnergyEfficiencyUnit,
  EnergyOverTimeUnit,
} from "@data-access/calculation/calculation-types.ts";

export interface CalculationListResultDto
  extends Omit<CalculationListResultResponse, "creationDate"> {
  creationDate: Date;
}

export type CalculationListResultResponse = {
  id: string;
  street: string;
  no: string;
  zipCode: string;
  city: string;
  creationDate: number;
  currentPrimaryEnergyDemand: number;
  currentPrimaryEnergyDemandUnit: EnergyEfficiencyUnit;
  currentPrimaryEnergyDemandScaleGrade: EnergyPerformanceGrade;
  alternativePrimaryEnergyDemand?: number;
  alternativePrimaryEnergyDemandUnit?: EnergyEfficiencyUnit;
  alternativePrimaryEnergyDemandScaleGrade?: EnergyPerformanceGrade;
};

export type CalculationResultDto = {
  id?: string;
  category: CalculationResultCategory;
  primaryEnergyDemand: number;
  primaryEnergyDemandUnit: EnergyEfficiencyUnit;
  finalEnergyDemandPerArea: number;
  finalEnergyDemandPerAreaUnit: EnergyEfficiencyUnit;
  co2Emissions: number;
  co2EmissionsUnit: Co2EmissionUnit;
  solarEnergyProduction: number;
  solarEnergyProductionUnit: EnergyOverTimeUnit;
};
