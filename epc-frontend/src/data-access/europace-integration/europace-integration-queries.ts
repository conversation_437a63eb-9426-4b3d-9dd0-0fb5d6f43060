import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";

import { globalApi } from "@data-access/api.ts";

const europaceIntegrationApi = globalApi.europaceIntegration;

export const useCreateEuropaceKundenangabenCaseMutation = (
  buildingId: string,
  config?: {
    onError?: (error: AxiosError) => void;
  },
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: { caseType: string }) => {
      return europaceIntegrationApi.createEuropaceKundenangabenCase.req(
        buildingId,
        params.caseType,
      );
    },
    mutationKey:
      europaceIntegrationApi.createEuropaceKundenangabenCase.key(buildingId),
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey:
          europaceIntegrationApi.getEuropaceKundenangabenCase.key(buildingId),
      });
      void queryClient.invalidateQueries({
        queryKey:
            europaceIntegrationApi.getKundenangabenCaseExist.key(buildingId),
      });
    },
    onError: (error: AxiosError) => {
      config?.onError?.(error);
    },
  });
};

export const useGetEuropaceKundenangabenCaseQuery = (isKundenangabenCaseExist?: boolean, buildingId?: string) => {
  return useQuery({
    enabled: !!buildingId && !!isKundenangabenCaseExist,
    queryFn: () =>
      europaceIntegrationApi.getEuropaceKundenangabenCase.req(buildingId!),
    queryKey: europaceIntegrationApi.getEuropaceKundenangabenCase.key(
      buildingId!,
    ),
    select: (data) => {
      return {
        ...data,
        creationDate: new Date(data.creationDate),
      };
    },
  });
};

export const useGetEuropaceKundenangabenCaseExistQuery = (buildingId?: string) => {
  return useQuery({
    enabled: !!buildingId,
    queryFn: () =>
        europaceIntegrationApi.getKundenangabenCaseExist.req(buildingId!),
    queryKey: europaceIntegrationApi.getKundenangabenCaseExist.key(
        buildingId!,
    )
  });
};
