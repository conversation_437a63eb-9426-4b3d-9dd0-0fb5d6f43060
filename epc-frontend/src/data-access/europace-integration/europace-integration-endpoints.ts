import { Axios } from "axios";

import { InterceptorBypass } from "@data-access/types.ts";

import { EuropaceCaseDto } from "./europace-integration-dto.ts";

export const initialiseEuropaceIntegrationEndpoints = (
  api: Axios,
  interceptorBypassList: InterceptorBypass[],
) => {
  const prefix = "/europace";

  interceptorBypassList.push({
    method: "GET",
    path: `${prefix}/kundenangaben/`,
    statusCode: 404,
  });

  interceptorBypassList.push({
    method: "POST",
    path: `${prefix}/kundenangaben/`,
    statusCode: 404,
  });

  return {
    createEuropaceKundenangabenCase: {
      req: async (buildingId: string, caseType: string) => {
        const { data } = await api.post<EuropaceCaseDto>(
          `${prefix}/kundenangaben/${buildingId}`,
          { caseType }
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        "createEuropaceKundenangabenCase",
      ],
    },
    getEuropaceKundenangabenCase: {
      req: async (buildingId: string) => {
        const { data } = await api.get<EuropaceCaseDto>(
          `${prefix}/kundenangaben/${buildingId}`,
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        "getEuropaceKundenangabenCase",
      ],
    },
    getKundenangabenCaseExist: {
      req: async (buildingId: string) => {
        const { data } = await api.get<boolean>(
            `${prefix}/kundenangaben/exist/${buildingId}`,
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        "getKundenangabenCaseExist",
      ],
    },
  };
};
