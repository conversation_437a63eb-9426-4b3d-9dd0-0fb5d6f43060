import axios from "axios";
import { get } from "lodash";

import { initialiseBuildingEndpoints } from "@data-access/building/building-endpoints.ts";
import { initialiseEuropaceIntegrationEndpoints } from "@data-access/europace-integration/europace-integration-endpoints.ts";
import { notificationInter<PERSON><PERSON><PERSON><PERSON> } from "@data-access/interceptors/errors.ts";
import { initialiseRenovationEndpoints } from "@data-access/renovation/renovation-endpoints.ts";
import { InterceptorBypass } from "@data-access/types.ts";

import { initialiseCalculationEndpoints } from "./calculation/calculation-endpoints.ts";

class Api {
  private token?: string;
  private readonly api;
  private readonly interceptorBypassList: InterceptorBypass[] = [];

  calculation: ReturnType<typeof initialiseCalculationEndpoints>;
  building: ReturnType<typeof initialiseBuildingEndpoints>;
  renovation: ReturnType<typeof initialiseRenovationEndpoints>;
  europaceIntegration: ReturnType<
    typeof initialiseEuropaceIntegrationEndpoints
  >;

  constructor() {
    this.api = this.createNewAxiosInstance();
    this.calculation = initialiseCalculationEndpoints(this.api);
    this.building = initialiseBuildingEndpoints(this.api);
    this.renovation = initialiseRenovationEndpoints(this.api);
    this.europaceIntegration = initialiseEuropaceIntegrationEndpoints(
      this.api,
      this.interceptorBypassList,
    );
  }

  public updateAuthToken = (token: string) => {
    this.token = token;
  };

  private createNewAxiosInstance = () => {
    const axiosApi = axios.create({
      baseURL: `${import.meta.env.VITE_APP_API_PREFIX || ""}/api`,
      timeout: 60000,
    });

    axiosApi.interceptors.request.use((config) => {
      if (this.token) {
        config.headers.Authorization = `Bearer ${this.token}`;
      }

      if (!config.headers["Content-Type"]) {
        config.headers["Content-Type"] = "application/json";
      }

      return config;
    });

    axiosApi.interceptors.response.use(
      (response) => response,
      (error: unknown) => {
        const shouldSkip = this.checkIfShouldSkip(error);

        if (!shouldSkip) notificationInterceptorHandler(error);

        return Promise.reject(error as Error);
      },
    );

    return axiosApi;
  };

  private checkIfShouldSkip(error: unknown) {
    const errMethod = String(get(error, "config.method")).toUpperCase();
    const errPath = String(get(error, "config.url"));
    const errStatusCode = get(error, "request.status");

    return !!this.interceptorBypassList.find(
      ({ path, method, statusCode }) =>
        errPath &&
        errPath.startsWith(path) &&
        method === errMethod.toUpperCase() &&
        statusCode === errStatusCode,
    );
  }
}

export const globalApi = new Api();
