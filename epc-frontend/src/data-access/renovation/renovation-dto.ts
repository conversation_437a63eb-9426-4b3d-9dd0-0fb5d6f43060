import {
  RenovationCategoryType,
  RenovationMeasureType,
  RenovationMeasureVariant,
} from "@data-access/renovation/renovation-types.ts";

export type RenovationGrantDto = {
  name: string;
  type: string;
  description: string;
  link?: string;
};

export type RenovationCostDto = {
  range: { first: number; second: number };
  mostProbableValue: number;
};

export type RenovationCostGenerationDto = {
  renovationId: string;
  cost?: RenovationCostDto;
};

export type RenovationDto = {
  id: string;
  recommended: boolean;
  selected: boolean;
  renovationMeasureCategory: RenovationCategoryType;
  renovationMeasureType: RenovationMeasureType;
  renovationMeasureVariant: RenovationMeasureVariant;
  renovationMeasureValue: string;
  grantPrograms: RenovationGrantDto[];
  cost?: RenovationCostDto;
};

export type RenovationMeasure = Omit<RenovationDto, "grantPrograms"> & {
  grantPrograms: RenovationMeaureGrant[];
};

export type RenovationMeaureGrant = RenovationGrantDto & {
  headline: string;
};

export type RenovationCategory = {
  key: RenovationCategoryType;
  measures: RenovationMeasure[];
};

export type CalculationProfitabilityDto = {
  id?: string;
  cost: number;
  energyCostSaved: number;
  propertyValueIncrease: number;
  totalProfit: number;
};
