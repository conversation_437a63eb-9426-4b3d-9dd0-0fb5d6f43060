import {MeasureQuality} from "@data-access/renovation/renovation-types.ts";

export type Language = "EN" | "DE";

export type RenovationGrantDto = {
  name: string;
  description: string;
  link?: string;
};

export type RenovationCostDto = {
  range: { first: number; second: number };
  mostProbableValue: number;
};

export type RenovationCostGenerationDto = {
  renovationId: string;
  cost?: RenovationCostDto;
};

export type RenovationDto = {
  id: string;
  measure: Record<Language, string>;
  measureDsc: Record<Language, string>;
  recommended: boolean;
  selected: boolean;
  grantPrograms: Record<Language, RenovationGrantDto>[];
  cost?: RenovationCostDto;
};

export type RenovationCost = {
  min: number;
  max: number;
  estimated: number;
};

export type Grant = {
  id: string;
  name: string;
  subtitle: string;
  description: string;
};

export type RenovationMeasure<Value> = {
  id: string;
  name: string;
  value: Value;
  active: boolean;
  recommended: boolean;
  cost?: RenovationCost;
  displayValue?: string;
};

export type RenovationCategory<
  Measures extends Record<string, RenovationMeasure<unknown>>,
> = {
  id: string;
  name: string;
  cost: RenovationCost;
  grants: Grant[];
  recommended: boolean;
  measuresById: Measures;
};

export type FacadeRenovationCategory = RenovationCategory<{
  insulation: RenovationMeasure<MeasureQuality>;
  cladding: RenovationMeasure<boolean>;
}>;

export type CalculationProfitabilityDto = {
  id?: string;
  cost: number;
  energyCostSaved: number;
  propertyValueIncrease: number;
  totalProfit: number;
};
