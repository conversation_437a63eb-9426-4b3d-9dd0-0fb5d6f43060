import { Axios } from "axios";

import {
  CalculationProfitabilityDto,
  RenovationCostGenerationDto,
  RenovationDto,
} from "./renovation-dto.ts";

export const initialiseRenovationEndpoints = (api: Axios) => {
  const prefix = "/renovation";
  const resultQueryGroup = "resultQueryGroup";

  return {
    generateRenovations: {
      req: async (buildingId: string) => {
        const { data } = await api.post<RenovationDto[]>(
          `${prefix}/${buildingId}`
        );
        return data;
      },
      key: (buildingId: string) => [prefix, buildingId, "generateRenovations"],
      invalidationKey: (buildingId: string) => [prefix, buildingId],
    },
    getRenovationMeasures: {
      req: async (buildingId: string) => {
        const { data } = await api.get<RenovationDto[]>(
          `${prefix}/${buildingId}`
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        resultQueryGroup,
        "getRenovationMeasures",
      ],
    },
    getProfitability: {
      req: async (buildingId: string) => {
        const { data } = await api.get<CalculationProfitabilityDto>(
          `${prefix}/${buildingId}/profitability`
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        resultQueryGroup,
        "getProfitability",
      ],
    },
    generateCosts: {
      req: async (buildingId: string) => {
        const { data } = await api.post<RenovationCostGenerationDto[]>(
          `${prefix}/${buildingId}/costs`
        );
        return data;
      },
      key: () => [prefix, "generateCosts"],
      invalidationKey: (buildingId: string) => [prefix, buildingId],
    },
    getCosts: {
      req: async (buildingId: string) => {
        const { data } = await api.get<RenovationCostGenerationDto[]>(
          `${prefix}/${buildingId}/costs`
        );
        return data;
      },
      key: (buildingId: string) => [
        prefix,
        buildingId,
        resultQueryGroup,
        "getCosts",
      ],
    },
  };
};
