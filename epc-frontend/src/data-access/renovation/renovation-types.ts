export enum MeasureQuality {
  ADVANCED = "Advanced",
  BASIC = "Basic",
}

export enum RenovationCategoryType {
  FACADE = "FACADE",
  ROOF = "ROOF",
  BASEMENT = "BASEMENT",
  WINDOWS = "WINDOWS",
  DOOR = "DOOR",
  HEATING_SYSTEM = "HEATING_SYSTEM",
  SOLAR_THERMAL = "SOLAR_THERMAL",
  SOLAR_PLANT = "SOLAR_PLANT",
  SURFACE_HEATING = "SURFACE_HEATING",
  RADIATOR_UPGRADE = "RADIATOR_UPGRADE",
  SOLAR_PANELS = "SOLAR_PANELS",
  SMART_HOME_ENERGY_MANAGEMENT = "SMART_HOME_ENERGY_MANAGEMENT",
  ELECTRIC_VEHICLE_CHARGING = "ELECTRIC_VEHICLE_CHARGING",
  AGE_APPROPRIATE_LIVING = "AGE_APPROPRIATE_LIVING",
}

export enum RenovationMeasureType {
  // Facade related
  FACADE_EXTERNAL_WALL_INSULATION = "FACADE_EXTERNAL_WALL_INSULATION",
  FACADE_EXTERIOR_CLADDING = "FACADE_EXTERIOR_CLADDING",

  // Roof related
  ROOF_INSULATION = "ROOF_INSULATION",
  ROOF_NEW_COVERAGE = "ROOF_NEW_COVERAGE",
  ROOF_RE_ENFORCEMENT = "ROOF_RE_ENFORCEMENT",

  // Ceiling related
  CEILING_INSULATION = "CEILING_INSULATION",

  // Windows related
  WINDOWS_GLAZING_TYPE = "WINDOWS_GLAZING_TYPE",
  WINDOWS_FRAME_MATERIAL_UPGRADE = "WINDOWS_FRAME_MATERIAL_UPGRADE",
  WINDOWS_SHUTTERS = "WINDOWS_SHUTTERS",
  WINDOWS_SECURITY_FEATURES = "WINDOWS_SECURITY_FEATURES",

  // Ground floor related
  GROUND_FLOOR_INSULATION = "GROUND_FLOOR_INSULATION",
  GROUND_FLOOR_MOISTURE_PROTECTION = "GROUND_FLOOR_MOISTURE_PROTECTION",

  // Basement related
  BASEMENT_INSULATION = "BASEMENT_INSULATION",
  BASEMENT_MOISTURE_PROTECTION = "BASEMENT_MOISTURE_PROTECTION",

  // Doors related
  DOORS_REPLACEMENT = "DOORS_REPLACEMENT",
  DOORS_SECURITY_FEATURES = "DOORS_SECURITY_FEATURES",

  // Heating related
  HEATING_PRIMARY_SOURCE = "HEATING_PRIMARY_SOURCE",
  HEATING_VENTILATION_HEAT_EXCHANGE = "HEATING_VENTILATION_HEAT_EXCHANGE",

  // Surface heating related
  UNDERFLOOR_HEATING = "UNDERFLOOR_HEATING",
  WALL_HEATING = "WALL_HEATING",
  CEILING_HEATING = "CEILING_HEATING",

  // Radiator related
  BASIC_EFFICIENCY = "BASIC_EFFICIENCY",
  HIGH_PERFORMANCE = "HIGH_PERFORMANCE",

  // Solar plant related
  SOLAR_PANELS = "SOLAR_PANELS",
  SOLAR_PLANT_PANELS = "SOLAR_PLANT_PANELS", // TODO old

  // Solar thermal related
  SOLAR_THERMAL = "SOLAR_THERMAL",

  // Solar panels related
  BATTERY_STORAGE = "BATTERY_STORAGE",
  IMMERSION_HEATER = "IMMERSION_HEATER",

  // Smart home energy management related
  INTELLIGENT_HEATING_CONTROLS = "INTELLIGENT_HEATING_CONTROLS",
  LIGHTING_AUTOMATION = "LIGHTING_AUTOMATION",
  SENSORS = "SENSORS",

  // Electric vehicle charging related
  WALLBOX_INSTALLATION = "WALLBOX_INSTALLATION",
  GRID_CONNECTION_UPGRADE = "GRID_CONNECTION_UPGRADE",

  // Age appropriate living related
  BARRIER_FREE_BATHROOM = "BARRIER_FREE_BATHROOM",
  STAIRLIFT = "STAIRLIFT",
  WIDENING_DOORS = "WIDENING_DOORS",
  EMERGENCY_SYSTEMS = "EMERGENCY_SYSTEMS",
}

export enum RenovationMeasureVariant {
  DEFAULT = "DEFAULT",
  BASIC = "BASIC",
  ADVANCED = "ADVANCED",
}
