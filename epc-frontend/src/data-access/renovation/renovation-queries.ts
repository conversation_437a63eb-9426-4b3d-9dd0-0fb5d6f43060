import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { groupBy } from "lodash";
import { useTranslation } from "react-i18next";

import { globalApi } from "@data-access/api.ts";
import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationCategoryType } from "@data-access/renovation/renovation-types.ts";

const renovationApi = globalApi.renovation;

export const useGenerateRenovationsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (buildingId: string) =>
      renovationApi.generateRenovations.req(buildingId),
    onSuccess: (_, buildingId) => {
      return queryClient.invalidateQueries({
        queryKey: renovationApi.generateRenovations.invalidationKey(buildingId),
      });
    },
  });
};

export const useGetProfitabilityQuery = (buildingId?: string) => {
  return useQuery({
    enabled: buildingId !== undefined,
    queryFn: () => renovationApi.getProfitability.req(buildingId!),
    queryKey: renovationApi.getProfitability.key(buildingId!),
  });
};

export const useGenerateCostsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (buildingId: string) =>
      renovationApi.generateCosts.req(buildingId),
    mutationKey: renovationApi.generateCosts.key(),
    onSuccess: (_, buildingId) => {
      return queryClient.invalidateQueries({
        queryKey: renovationApi.generateCosts.invalidationKey(buildingId),
      });
    },
  });
};

export const useGetCalculationGroupedRenovationsQuery = (
  buildingId?: string,
) => {
  const { t } = useTranslation();

  return useQuery({
    refetchOnWindowFocus: false,
    enabled: buildingId !== undefined,
    queryFn: () => renovationApi.getRenovationMeasures.req(buildingId!),
    queryKey: renovationApi.getRenovationMeasures.key(buildingId!),
    select: (data): RenovationCategory[] => {
      const groupedData = groupBy(data, "renovationMeasureCategory");

      return Object.entries(groupedData).map(([categoryKey, measures]) => ({
        key: categoryKey as RenovationCategoryType,
        measures: measures.map((measure) => {
          return {
            ...measure,
            grantPrograms: measure.grantPrograms.map((grant) => {
              try {
                return {
                  ...grant,
                  name: t(`dataAccess.grants.${grant.type}.name`),
                  headline: t(`dataAccess.grants.${grant.type}.headline`),
                  description: t(`dataAccess.grants.${grant.type}.description`),
                };
              } catch (error) {
                console.warn(
                  "Could not translate grant for",
                  measure.id,
                  measure.renovationMeasureCategory,
                  measure.renovationMeasureType,
                  measure.renovationMeasureVariant,
                );
                console.error(error);
              }

              return {
                name: "",
                headline: "",
                description: "",
                type: "",
              };
            }),
          };
        }),
      }));
    },
  });
};
