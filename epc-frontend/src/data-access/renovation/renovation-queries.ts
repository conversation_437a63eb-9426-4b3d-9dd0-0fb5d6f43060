import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { globalApi } from "@data-access/api.ts";

const renovationApi = globalApi.renovation;

export const useGetRenovationMeasuresQuery = (buildingId?: string) => {
  return useQuery({
    enabled: buildingId !== undefined,
    queryFn: () => renovationApi.getRenovationMeasures.req(buildingId!),
    queryKey: renovationApi.getRenovationMeasures.key(buildingId!),
  });
};

export const useGetRenovationMeasuresV2Query = (buildingId?: string) => {
  return useQuery({
    enabled: buildingId !== undefined,
    queryFn: () => renovationApi.getRenovationMeasuresV2.req(buildingId!),
    queryKey: renovationApi.getRenovationMeasuresV2.key(buildingId!),
  });
};

export const useHasSelectedRenovationMeasuresQuery = (buildingId?: string) => {
  return useQuery({
    enabled: buildingId !== undefined,
    queryFn: () => renovationApi.getRenovationMeasures.req(buildingId!),
    queryKey: renovationApi.getRenovationMeasures.key(buildingId!),
    select: (data) => {
      return data.some(({ selected }) => selected);
    },
  });
};

export const useGenerateRenovationsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn:  (buildingId: string) => renovationApi.generateRenovations.req(buildingId),
    onSuccess:  (_, buildingId) => {
      return queryClient.invalidateQueries({
        queryKey: renovationApi.generateRenovations.invalidationKey(buildingId),
      });
    },
  });
};


export const useGetProfitabilityQuery = (buildingId?: string) => {
  return useQuery({
    enabled: buildingId !== undefined,
    queryFn: () => renovationApi.getProfitability.req(buildingId!),
    queryKey: renovationApi.getProfitability.key(buildingId!),
  });
};

export const useGenerateCostsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (buildingId: string) => renovationApi.generateCosts.req(buildingId),
    onSuccess: (_, buildingId) => {
      return queryClient.invalidateQueries({
        queryKey: renovationApi.generateCosts.invalidationKey(buildingId),
      });
    },
  });
};

export const useGetCostsQuery = (buildingId?: string) => {
  return useQuery({
    enabled: buildingId !== undefined,
    queryFn: () => renovationApi.getCosts.req(buildingId!),
    queryKey: renovationApi.getCosts.key(buildingId!),
  });
};

