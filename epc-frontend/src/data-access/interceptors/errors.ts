import { notifications } from "@mantine/notifications";
import i18n from "i18next";
import { get } from "lodash";

export const notificationInterceptorHandler = (error: unknown) => {
  if (typeof error !== "object" || error === null) {
    notifications.show({
      id: `unknown-api-${Date.now()}`,
      title: `Unknown API error occurred`,
      message: `We could not process your HTTP request, check your network`,
    });
  } else if ("request" in error && error.request instanceof XMLHttpRequest) {
    const method = get(error, "config.method", "");
    const { status, responseURL, statusText } = error.request;

    if (status === 401) {
      notifications.show({
        id: `${status}-${statusText}`,
        title: i18n.t("interceptors.errors.401.title"),
        message: i18n.t("interceptors.errors.401.body"),
        color: "red",
        autoClose: false,
        position: "top-right",
      });
    } else {
      notifications.show({
        id: `${status}-${statusText}-${responseURL}`,
        title: `${status} ${statusText}`,
        message: `${method.toUpperCase()} ${responseURL}`,
        color: "red",
        autoClose: 10000,
        position: "top-right",
      });
    }
  }
};
