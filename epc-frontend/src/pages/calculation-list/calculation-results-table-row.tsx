import { Anchor, Group, Table, Text } from "@mantine/core";
import { IconArrowRight } from "@tabler/icons-react";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { CalculationListResultDto } from "@data-access/calculation/calculation-dto.ts";
import { routes } from "@providers/router/router.tsx";
import { NumberDisplay } from "@shared/components/formatters/number-display.tsx";

export const CalculationResultsTableRow = ({
  item: {
    id,
    creationDate,
    alternativePrimaryEnergyDemand,
    alternativePrimaryEnergyDemandScaleGrade: alternativeGrade,
    currentPrimaryEnergyDemand,
    currentPrimaryEnergyDemandScaleGrade: currentGrade,
    street,
    no,
    zipCode,
    city,
  },
}: {
  item: CalculationListResultDto;
}) => {
  const { t } = useTranslation();

  const currentScoreDisplayValue = t(`core.score.${currentGrade}`);
  const alternativeScoreDisplayValue = useMemo(() => {
    if (!alternativeGrade) return "-";

    return t(`core.score.${alternativeGrade}`);
  }, [t, alternativeGrade]);

  return (
    <Table.Tr>
      <Table.Td>
        <Link to={routes.calculation.review.parse(id)}>
          <Anchor>{street} {no}, {zipCode} {city} </Anchor>
        </Link>
      </Table.Td>
      <Table.Td>{creationDate.toLocaleString()}</Table.Td>
      <Table.Td>
        <Group gap="sm">
          <NumberDisplay value={currentPrimaryEnergyDemand} />
          <IconArrowRight size="20" stroke="1" color="grey" />
          {alternativePrimaryEnergyDemand ? (
            <NumberDisplay value={alternativePrimaryEnergyDemand} />
          ) : (
            "-"
          )}
          <Text c="gray.6">
            {t("pages.calculationList.table.efficiencyUnit")}
          </Text>
        </Group>
      </Table.Td>
      <Table.Td>
        <Group gap="sm">
          {currentScoreDisplayValue}
          <IconArrowRight size="20" stroke="1" color="grey" />
          {alternativeScoreDisplayValue}
        </Group>
      </Table.Td>
    </Table.Tr>
  );
};
