import { Group, NumberInput, Radio } from "@mantine/core";
import { useContext } from "react";
import { useTranslation } from "react-i18next";

import { BuildingBasement } from "@data-access/building/building-types.ts";
import { FinBooleanField } from "@shared/components/fields/fin-boolean-field.tsx";
import { FinRadioTextCard } from "@shared/components/fields/selectable-cards/fin-radio-text-card.tsx";
import { FnTitledFormSection } from "@shared/components/layout/fn-titled-form-section.tsx";
import BasementImg from "@shared/icons/basement.svg?react";

import { useBuildingFormContext } from "../building-form-context.ts";
import { BuildingFormRefsContext } from "../building-form-ref-context.tsx";

const basementOptions = [
  BuildingBasement.NO_BASEMENT,
  BuildingBasement.UNHEATED,
  BuildingBasement.HEATED,
];

export const BasementFormSection = () => {
  const {
    sectionRefs: { basementRef },
  } = useContext(BuildingFormRefsContext);
  const form = useBuildingFormContext();
  const { t } = useTranslation();
  const {
    basement: { basementType, basementOrGroundFloorInsulated },
  } = form.getValues();

  console.log(form.getValues().basement);

  const isBasementHeated =
    !basementType || basementType === BuildingBasement.HEATED;

  form.watch(
    "basement.basementOrGroundFloorInsulated",
    ({ previousValue, value }) => {
      if (previousValue && !value) {
        form.setFieldValue(
          "basement.basementOrGroundFloornsulationYear",
          undefined
        );
      }
    }
  );

  return (
    <FnTitledFormSection
      ref={basementRef}
      title={t("pages.buildingForm.basement.title")}
      icon={<BasementImg />}
    >
      <Radio.Group
        label={t("pages.buildingForm.basement.typeQue")}
        {...form.getInputProps("basement.basementType")}
      >
        <Group gap="md" py="sm">
          {basementOptions.map((basementType) => (
            <FinRadioTextCard
              key={basementType}
              value={basementType}
              label={t(`dataAccess.buildingBasement.${basementType}`)}
            />
          ))}
        </Group>
      </Radio.Group>
      {basementType !== undefined && (
        <>
          <FinBooleanField
            label={
              isBasementHeated
                ? t("pages.buildingForm.basement.basementInsulatedQue")
                : t("pages.buildingForm.basement.groundFloorInsulatedQue")
            }
            {...form.getInputProps("basement.basementOrGroundFloorInsulated")}
          />
          <NumberInput
            w="400"
            key={String(basementOrGroundFloorInsulated)}
            disabled={!basementOrGroundFloorInsulated}
            label={
              isBasementHeated
                ? t("pages.buildingForm.basement.basementInsulationYearQue")
                : t("pages.buildingForm.basement.groundFloorInsulationYearQue")
            }
            description={t("pages.buildingForm.basement.insulationYearDsc")}
            placeholder={t(
              "pages.buildingForm.basement.insulationYearPlaceholder"
            )}
            max={9999}
            allowDecimal={false}
            {...form.getInputProps(
              "basement.basementOrGroundFloornsulationYear"
            )}
          />
        </>
      )}
    </FnTitledFormSection>
  );
};
