import { useCallback } from "react";

import { useCreateBuildingMutation } from "@data-access/building/building-queries.ts";
import { useCalculationMutation } from "@data-access/calculation/calculation-queries.ts";
import {useGenerateCostsMutation, useGenerateRenovationsMutation} from "@data-access/renovation/renovation-queries.ts";

import { BuildingFormSubmitValues } from "./building-form-context.ts";
import { useBuildingFormToDtoTransformation } from "./data-transformation/use-building-form-to-dto-transformation.ts";

export const useBuildingFormOnSubmitMutations = (
  onSuccess: (buildingId: string) => void,
) => {
  const toDto = useBuildingFormToDtoTransformation();
  const { isPending: isBuildingPending, mutateAsync: saveBuilding } =
    useCreateBuildingMutation();
  const { isPending: isCalculationPending, mutateAsync: runCalculation } =
    useCalculationMutation();
  const { isPending: isRenovationGenerationPending, mutateAsync: generateRenovations } =
    useGenerateRenovationsMutation();
  const { mutateAsync: generateCosts } =
      useGenerateCostsMutation();


  return {
    runMutations: useCallback(
      async (values: BuildingFormSubmitValues) => {
        const building = await saveBuilding(toDto(values));
        await generateRenovations(building.id!)
        // Fire and forget - don't wait for this to complete
        generateCosts(building.id!).catch(error => {
            console.error('Error generating renovation costs:', error);
        });
        await runCalculation(building.id!);
        onSuccess(building.id!);
      },
      [saveBuilding, runCalculation, generateRenovations, onSuccess, toDto],
    ),
    isPending: isBuildingPending || isCalculationPending || isRenovationGenerationPending,
  };
};
