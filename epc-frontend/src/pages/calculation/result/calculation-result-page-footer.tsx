import { Button, Group } from "@mantine/core";
import { IconTools } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import { FinPageFooter } from "@shared/components/page/fin-page-footer.tsx";

type Props = {
  isRecalculationActive: boolean;
  isRecalculationPending: boolean;
  onRecalculate: () => void;
};

export const CalculationResultPageFooter = ({
  isRecalculationActive,
  isRecalculationPending,
  onRecalculate,
}: Props) => {
  const { t } = useTranslation();

  const APPLY_RENOVATIONS = t("pages.calculationResult.cta.applyRenovations");
  const CALCULATING = t("pages.calculationResult.cta.calculating");

  return (
    <FinPageFooter>
      <Group w="100%" justify="flex-start">
        <Button
          variant="outline"
          onClick={onRecalculate}
          disabled={!isRecalculationActive || isRecalculationPending}
          leftSection={<IconTools />}
        >
          {isRecalculationPending ? CALCULATING : APPLY_RENOVATIONS}
        </Button>
      </Group>
    </FinPageFooter>
  );
};
