import { Checkbox, Group } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

import { TextLabelMode } from "../utils/use-measure-text-value";
import { useMeasureTextSelectionValue } from "../utils/use-measure-text-value";
import { MeasurePriceAndGrant } from "./measure-price-and-grant";

type Props = {
  measure: RenovationMeasure;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  hideDescription?: boolean;
  description?: string;
  mode?: TextLabelMode;
};

export const MeasureCheckbox = ({
  measure,
  onChange,
  hideDescription = false,
  description,
  mode = TextLabelMode.VARIANT,
}: Props) => {
  const { t } = useTranslation();
  const label = useMeasureTextSelectionValue(measure, mode);

  const dsc = useMemo(() => {
    if (hideDescription) {
      return undefined;
    }

    if (description) {
      return description;
    }

    const {
      renovationMeasureValue: val,
      renovationMeasureCategory: category,
      renovationMeasureType: type,
    } = measure;

    return t(
      `pages.calculationResult.categories.texts.${category}.measures.${type}.values.${val}.desc`
    );
  }, [hideDescription, measure, t, description]);

  return (
    <Group justify="space-between">
      <Checkbox
        key={measure.id}
        size="sm"
        color="teal.7"
        value={measure.id}
        label={label}
        checked={measure.selected}
        onChange={onChange}
        description={dsc}
      />
      <MeasurePriceAndGrant measure={measure} />
    </Group>
  );
};
