import { Stack, Divider, Text, useMantineTheme } from "@mantine/core";
import { IconPaintOff } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

import { RenovationSummaryListItem } from "./renovation-summary-list-item";

type Props = {
  measures?: RenovationMeasure[];
  onRemoveMeasure?: (measure: RenovationMeasure) => void;
  isReviewMode?: boolean;
};

export const RenovationSummaryMeasureList = ({
  measures = [],
  onRemoveMeasure,
  isReviewMode = false,
}: Props) => {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  return (
    <Stack gap="xs">
      <Divider
        mt="xs"
        mb={4}
        label={t("pages.calculationResult.categories.common.selectedMeasures")}
      />
      {measures.map((measure) => (
        <RenovationSummaryListItem
          key={measure.id}
          measure={measure}
          onRemoveMeasure={onRemoveMeasure}
        />
      ))}
      {measures.length === 0 && (
        <Stack
          justify="center"
          align="center"
          py={isReviewMode ? "xl" : "xs"}
          gap="xs"
        >
          {isReviewMode && (
            <IconPaintOff size={34} color={theme.colors.gray[5]} />
          )}
          <Text size={isReviewMode ? "lg" : "sm"} c="dimmed">
            {t("pages.calculationResult.categories.common.noMeasuresSelected")}
          </Text>
        </Stack>
      )}
    </Stack>
  );
};
