import { Group, Paper, Stack, Text, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";
import { formatDecimal } from "@shared/utils/value-formatters";

import { RenovationSummaryMeasureList } from "./renovation-summary-measure-list";

type Props = {
  totalCost: number;
  selectedMeasures: RenovationMeasure[];
};

export const RenovationSummaryReviewPanel = ({
  totalCost,
  selectedMeasures,
}: Props) => {
  const { t, i18n } = useTranslation();

  return (
    <Paper p="md" radius="md">
      <Stack gap="md">
        <Group gap="md" justify="space-between">
          <Stack gap="xs"></Stack>
          <Stack gap={0} align="flex-end">
            <Text size="sm" c="dimmed">
              {t("pages.calculationResult.categories.summary.totalCost")}
            </Text>
            <Title order={3}>
              {formatDecimal(i18n.language, totalCost)} EUR
            </Title>
          </Stack>
        </Group>

        <RenovationSummaryMeasureList
          measures={selectedMeasures}
          isReviewMode
        />
      </Stack>
    </Paper>
  );
};
