import {
  Button,
  Collapse,
  Group,
  Paper,
  Popover,
  Stack,
  Text,
  Title,
  UnstyledButton,
  useMantineTheme,
} from "@mantine/core";
import {
  IconInfoCircle,
  IconCircleCheck,
  IconTools,
  IconChevronUp,
  IconChevronDown,
} from "@tabler/icons-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";
import { formatDecimal } from "@shared/utils/value-formatters";

import { RenovationSummaryMeasureList } from "./renovation-summary-measure-list";

type Props = {
  totalCost: number;
  hasUnsavedRenovations: boolean;
  onApplyRenovations: () => void;
  isApplyingRenovations?: boolean;
  selectedMeasures: RenovationMeasure[];
  onRemoveMeasure: (measure: RenovationMeasure) => void;
};

export const RenovationSummaryPanel = ({
  totalCost,
  hasUnsavedRenovations,
  onApplyRenovations,
  isApplyingRenovations = false,
  selectedMeasures,
  onRemoveMeasure,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const { t, i18n } = useTranslation();
  const theme = useMantineTheme();

  return (
    <Paper p="md" radius="md">
      <Stack gap="md">
        <Group gap="md" justify="space-between">
          <Stack>
            <Group gap="xs">
              {!hasUnsavedRenovations ? (
                <Popover width={200} position="bottom" withArrow shadow="md">
                  <Popover.Target>
                    <Button
                      flex="0 0 auto"
                      loading={isApplyingRenovations}
                      variant="default"
                      color="teal.6"
                      radius="xl"
                      leftSection={<IconTools size={20} stroke={1.5} />}
                    >
                      {t("pages.calculationResult.categories.summary.apply")}
                    </Button>
                  </Popover.Target>
                  <Popover.Dropdown>
                    <Text size="sm">
                      {t(
                        "pages.calculationResult.categories.summary.applyToSeeTheChanges"
                      )}
                    </Text>
                  </Popover.Dropdown>
                </Popover>
              ) : (
                <Button
                  flex="0 0 auto"
                  onClick={() => {
                    if (hasUnsavedRenovations) {
                      onApplyRenovations();
                    }
                  }}
                  loading={isApplyingRenovations}
                  variant="filled"
                  color="teal.6"
                  radius="xl"
                  leftSection={<IconTools size={20} stroke={1.5} />}
                >
                  {t("pages.calculationResult.categories.summary.apply")}
                </Button>
              )}
            </Group>
            <Group gap={4} align="flex-start">
              {hasUnsavedRenovations ? (
                <>
                  <IconInfoCircle size={18} color={theme.colors.blue[8]} />
                  <Text size="sm" fs="italic" maw={400}>
                    {t("pages.calculationResult.categories.summary.notApplied")}
                  </Text>
                </>
              ) : (
                <>
                  <IconCircleCheck size={20} color={theme.colors.teal[6]} />
                  <Text size="sm" fs="italic">
                    {t("pages.calculationResult.categories.summary.applied")}
                  </Text>
                </>
              )}
            </Group>
          </Stack>
          <Stack gap={0} align="flex-end">
            <Text size="sm" c="dimmed">
              {t("pages.calculationResult.categories.summary.totalCost")}
            </Text>
            <Title order={3}>
              {formatDecimal(i18n.language, totalCost)} EUR
            </Title>
            <UnstyledButton onClick={() => setIsOpen(!isOpen)} w="100%">
              <Group justify="flex-end" gap="xs">
                {isOpen ? (
                  <IconChevronUp size={16} />
                ) : (
                  <IconChevronDown size={16} />
                )}
                <Text fw={500} size="sm">
                  {isOpen
                    ? t(
                        "pages.calculationResult.categories.summary.hideSelectedMeasures"
                      )
                    : t(
                        "pages.calculationResult.categories.summary.showSelectedMeasures"
                      )}
                </Text>
              </Group>
            </UnstyledButton>
          </Stack>
        </Group>
        <Collapse in={isOpen}>
          <RenovationSummaryMeasureList
            measures={selectedMeasures}
            onRemoveMeasure={onRemoveMeasure}
          />
        </Collapse>
      </Stack>
    </Paper>
  );
};
