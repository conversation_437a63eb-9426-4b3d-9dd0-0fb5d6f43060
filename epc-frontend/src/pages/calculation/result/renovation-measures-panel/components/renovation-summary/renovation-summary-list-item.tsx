import { Text, Group, ActionIcon } from "@mantine/core";
import { IconTrash } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

import { useMeasureFullTextValue } from "../../utils/use-measure-text-value";
import { MeasurePriceAndGrant } from "../measure-price-and-grant";

type Props = {
  measure: RenovationMeasure;
  onRemoveMeasure?: (measure: RenovationMeasure) => void;
};

export const RenovationSummaryListItem = ({
  measure,
  onRemoveMeasure,
}: Props) => {
  const measureTextValue = useMeasureFullTextValue(measure);
  const { t } = useTranslation();
  const categoryLabel = t(
    `pages.calculationResult.categories.texts.${measure.renovationMeasureCategory}.label`
  );
  return (
    <Group justify="space-between">
      <Group gap={3}>
        <Text fz="sm" fw={500}>
          {categoryLabel}
        </Text>
        <Text fz="sm" fw={500}>
          -
        </Text>
        <Text fz="sm">{measureTextValue}</Text>
      </Group>
      <Group gap="md">
        <MeasurePriceAndGrant measure={measure} />
        {onRemoveMeasure && (
          <ActionIcon
            variant="default"
            onClick={() => onRemoveMeasure?.(measure)}
          >
            <IconTrash size={16} stroke={1.5} />
          </ActionIcon>
        )}
      </Group>
    </Group>
  );
};
