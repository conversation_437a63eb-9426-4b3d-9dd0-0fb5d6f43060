import { Skeleton, Stack, Text } from "@mantine/core";

type RenovationCategoryDataProps = {
  label?: string;
  value?: string;
  context?: string;
  minWidth?: number;
  isEmphasized?: boolean;
  isLoading?: boolean;
};

export const RenovationCategoryData = ({
  label,
  value,
  context,
  minWidth = 150,
  isEmphasized = true,
  isLoading = false,
}: RenovationCategoryDataProps) => {
  const emptyValue = "--";

  return (
    <Stack gap="0" miw={minWidth} flex="0 0 auto">
      <Text fz="sm">{label}</Text>
      {!isLoading && (
        <>
          <Text
            fw={isEmphasized ? 600 : 500}
            c={isEmphasized ? "black" : "gray.6"}
          >
            {value !== undefined ? value : emptyValue}
          </Text>
          <Text fz="xs" c="gray.6">
            {context}
          </Text>
        </>
      )}
      {isLoading && (
        <Stack gap="xs">
          <Skeleton height={16} radius={4} width={100} />
          <Skeleton height={12} radius={3} width={160} />
        </Stack>
      )}
    </Stack>
  );
};
