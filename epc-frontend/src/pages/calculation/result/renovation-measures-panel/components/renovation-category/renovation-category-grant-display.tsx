import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { RenovationCategoryData } from "./renovation-category-data";

type RenovationGrantDisplayProps = {
  grantTypes: string[];
};

export const RenovationCategoryGrantDisplay = ({
  grantTypes,
}: RenovationGrantDisplayProps) => {
  const { t } = useTranslation();
  const grantTypesText = useMemo(() => {
    return grantTypes.length > 0 ? grantTypes.join(", ") : " -- ";
  }, [grantTypes]);

  return (
    <RenovationCategoryData
      label={t("pages.calculationResult.categories.common.possibleGrants")}
      value={grantTypesText}
    />
  );
};
