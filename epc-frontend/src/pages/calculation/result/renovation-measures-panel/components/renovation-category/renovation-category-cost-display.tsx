import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { formatDecimal } from "@shared/utils/value-formatters";

import { CategoryCost } from "../../utils/use-category-cost";
import { useIsAnyCostMutationPending } from "../../utils/use-is-any-cost-mutation-pending";
import { RenovationCategoryData } from "./renovation-category-data";

type RenovationCostDisplayProps = {
  cost?: CategoryCost;
};

export const RenovationCategoryCostDisplay = ({
  cost,
}: RenovationCostDisplayProps) => {
  const { t, i18n } = useTranslation();
  const isCostMutationPending = useIsAnyCostMutationPending();

  const formattedCost = useMemo(() => {
    if (!cost || cost.currentCost === undefined)
      return t("pages.calculationResult.categories.common.na");

    return `${formatDecimal(i18n.language, cost.currentCost)} EUR`;
  }, [cost, i18n.language, t]);

  const rangeText = useMemo(() => {
    if (!cost || cost.range === undefined)
      return t("pages.calculationResult.categories.common.naRange");

    return t("pages.calculationResult.categories.common.range", {
      min: formatDecimal(i18n.language, cost.range.min),
      max: formatDecimal(i18n.language, cost.range.max),
    });
  }, [cost, i18n.language, t]);

  return (
    <RenovationCategoryData
      label={t("pages.calculationResult.categories.common.estimatedCost")}
      value={formattedCost}
      context={rangeText}
      isEmphasized={cost?.currentCost !== undefined && cost.currentCost > 0}
      isLoading={isCostMutationPending}
    />
  );
};
