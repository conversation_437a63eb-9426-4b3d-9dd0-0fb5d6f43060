import { Divider, Group, Paper, Stack, Text, Anchor } from "@mantine/core";
import { IconArrowLeft, IconArrowRight } from "@tabler/icons-react";
import { ReactNode, useState } from "react";
import { useTranslation } from "react-i18next";

import { RenovationMeaureGrant } from "@data-access/renovation/renovation-dto.ts";

import styles from "./renovation-category-body.module.css";

type Props = {
  controls: ReactNode;
  grants: RenovationMeaureGrant[];
};

const GrantName = ({ grant }: { grant: RenovationMeaureGrant }) => {
  if (!grant.link) {
    return <Text fw={600}>{grant.name}</Text>;
  }

  return (
    <Text fw={600}>
      <Anchor
        href={grant.link}
        target="_blank"
        rel="noopener noreferrer"
        className={styles.link}
        mb={2}
      >
        {grant.name}
      </Anchor>
    </Text>
  );
};

export const RenovationCategoryBody = ({ controls, grants }: Props) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { t } = useTranslation();

  const grantsLabel = isExpanded
    ? t("pages.calculationResult.categories.common.hideGrants")
    : t("pages.calculationResult.categories.common.showGrants");

  return (
    <Paper p="md" radius="md">
      <Group justify="flex-end">
        <Group align="center" gap={4}>
          {!isExpanded && (
            <IconArrowLeft
              size={16}
              onClick={() => setIsExpanded(!isExpanded)}
            />
          )}
          <Text
            component="a"
            onClick={() => setIsExpanded(!isExpanded)}
            size="sm"
            className={styles.link}
          >
            {grantsLabel}
          </Text>
          {isExpanded && (
            <IconArrowRight
              size={16}
              onClick={() => setIsExpanded(!isExpanded)}
            />
          )}
        </Group>
      </Group>
      <Group align="flex-start" gap="xl">
        <Stack flex={1}>{controls}</Stack>
        {isExpanded && (
          <>
            <Divider orientation="vertical" variant="dashed" />
            <Stack gap="md" w={290}>
              {grants.map((grant, index) => (
                <Stack key={index} gap={2} mt="xs">
                  <GrantName grant={grant} />
                  <Text size="sm" c="dimmed">
                    {grant.headline}
                  </Text>
                  <Text size="sm">{grant.description}</Text>
                </Stack>
              ))}
            </Stack>
          </>
        )}
      </Group>
    </Paper>
  );
};
