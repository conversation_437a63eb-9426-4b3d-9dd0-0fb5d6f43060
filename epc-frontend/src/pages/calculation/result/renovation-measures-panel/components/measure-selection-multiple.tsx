import { Checkbox, Stack, Text } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

import { TextLabelMode } from "../utils/use-measure-text-value";
import { MeasureCheckbox } from "./measure-checkbox";

type Props = {
  label?: string;
  measures: RenovationMeasure[];
  onChange: (value: RenovationMeasure[]) => void;
  mode?: TextLabelMode;
};

export const MeasureSelectionMultiple = ({
  label,
  measures,
  onChange,
  mode = TextLabelMode.VARIANT,
}: Props) => {
  const { t } = useTranslation();
  const selected = useMemo(() => {
    return measures.filter(({ selected }) => selected).map((m) => m.id);
  }, [measures]);

  const labelText = useMemo(() => {
    if (label) {
      return label;
    }

    if (measures.length > 0) {
      const [{ renovationMeasureType, renovationMeasureCategory }] = measures;
      return t(
        `pages.calculationResult.categories.texts.${renovationMeasureCategory}.measures.${renovationMeasureType}.label`
      );
    }

    return "";
  }, [label, measures, t]);

  return (
    <Stack gap="xs">
      <Stack gap={0}>
        <Text fw={500}>{labelText}</Text>
      </Stack>
      <Checkbox.Group
        value={selected}
        onChange={(values) => {
          const lastSelected = values[values.length - 1];
          onChange(
            measures.map((measure) => ({
              ...measure,
              selected: measure.id === lastSelected,
            }))
          );
        }}
      >
        <Stack>
          {measures.map((measure) => (
            <MeasureCheckbox key={measure.id} measure={measure} mode={mode} />
          ))}
        </Stack>
      </Checkbox.Group>
    </Stack>
  );
};
