import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

import { RenovationMeasureSelection } from "../state/use-renovation-state";
import { TextLabelMode } from "../utils/use-measure-text-value";
import { MeasureCheckbox } from "./measure-checkbox";

type Props = {
  measure: RenovationMeasure;
  onChange: (selection: RenovationMeasureSelection[]) => void;
  mode?: TextLabelMode;
};

export const MeasureSelectionSingle = ({
  measure,
  onChange,
  mode = TextLabelMode.TYPE,
}: Props) => {
  return (
    <MeasureCheckbox
      measure={measure}
      mode={mode}
      onChange={(event) => {
        const selection = [
          {
            category: measure.renovationMeasureCategory,
            measureId: measure.id,
            selected: event.target.checked,
          },
        ];
        onChange(selection);
      }}
    />
  );
};
