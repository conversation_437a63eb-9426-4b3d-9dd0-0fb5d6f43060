import { Badge, alpha, useMantineTheme } from "@mantine/core";
import { FC } from "react";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

import { useMeasureFullTextValue } from "../utils/use-measure-text-value";

type RenovationMeasureBadgeProps = {
  measure: RenovationMeasure;
};

export const RenovationMeasureBadge: FC<RenovationMeasureBadgeProps> = ({
  measure,
}) => {
  const theme = useMantineTheme();

  const label = useMeasureFullTextValue(measure, true);

  return (
    <Badge
      variant="light"
      radius="sm"
      color={theme.colors.teal[7]}
      bg={alpha(theme.colors.teal[7], 0.05)}
    >
      {label}
    </Badge>
  );
};
