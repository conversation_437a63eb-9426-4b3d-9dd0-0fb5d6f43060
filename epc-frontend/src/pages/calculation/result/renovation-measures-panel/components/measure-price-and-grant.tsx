import { Divider, Group, Skeleton, Text } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";
import { formatDecimal } from "@shared/utils/value-formatters";

import { useIsAnyCostMutationPending } from "../utils/use-is-any-cost-mutation-pending";

type Props = {
  measure: RenovationMeasure;
};

export const MeasurePriceAndGrant = ({ measure }: Props) => {
  const { i18n } = useTranslation();
  const isCostMutationPending = useIsAnyCostMutationPending();

  const grantNames = useMemo(
    () =>
      measure.grantPrograms.length > 0
        ? measure.grantPrograms.map((grant) => grant.name).join(", ")
        : " -- ",
    [measure.grantPrograms]
  );

  return (
    <Group>
      {isCostMutationPending ? (
        <Skeleton height={14} radius={4} width={150} />
      ) : (
        <Text fz="sm">
          ~{formatDecimal(i18n.language, measure.cost?.mostProbableValue)} EUR
        </Text>
      )}
      <Divider orientation="vertical" />
      <Text fz="xs" fs="italic" c="gray.6">
        {grantNames}
      </Text>
    </Group>
  );
};
