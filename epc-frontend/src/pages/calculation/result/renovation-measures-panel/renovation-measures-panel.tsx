import { Accordion, Paper, Stack, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";

import { RenovationCategoryType } from "@data-access/renovation/renovation-types";

import { RenovationSummaryPanel } from "./components/renovation-summary/renovation-summary-panel";
import { BasementCategory } from "./measures/building-renovation/basement-category";
import { DoorCategory } from "./measures/building-renovation/door-category";
import { FacadeCategory } from "./measures/building-renovation/facade-category";
import { RoofCategory } from "./measures/building-renovation/roof-category";
import { WindowsCategory } from "./measures/building-renovation/windows-category";
import { AgeAppropriateLivingCategory } from "./measures/energy-and-comfort/age-appropriate-living-category";
import { ElectricVehicleCategory } from "./measures/energy-and-comfort/electric-vehicle-categor";
import { SmartHomeCategory } from "./measures/energy-and-comfort/smart-home-category";
import { SolarPanelsCategory } from "./measures/energy-and-comfort/solar-panels-category";
import { HeatingSystemCategory } from "./measures/heating/heating-system-category";
import { RadiatorCategory } from "./measures/heating/radiator-category";
import { SurfaceHeatingCategory } from "./measures/heating/surface-heating-category.tsx";
import { useRemoteRenovationState } from "./state/use-renovation-state";

type Props = {
  buildingId: string;
};

export const RenovationMeasuresPanel = ({ buildingId }: Props) => {
  const { t } = useTranslation();

  const {
    currentCost,
    hasUnsavedChanges,
    renovationCategories,
    allSelectedMeasures,
    setMeasuresSelection: changeMeasuresSelection,
    submit,
    isSubmitting,
    isIdle,
    removeMeasure,
  } = useRemoteRenovationState(buildingId);

  const facadeCategory = renovationCategories[RenovationCategoryType.FACADE];
  const roofCategory = renovationCategories[RenovationCategoryType.ROOF];
  const windowsCategory = renovationCategories[RenovationCategoryType.WINDOWS];
  const basementCategory =
    renovationCategories[RenovationCategoryType.BASEMENT];
  const doorCategory = renovationCategories[RenovationCategoryType.DOOR];
  const heatingSystemCategory =
    renovationCategories[RenovationCategoryType.HEATING_SYSTEM];
  const surfaceHeatingCategory =
    renovationCategories[RenovationCategoryType.SURFACE_HEATING];
  const radiatorCategory =
    renovationCategories[RenovationCategoryType.RADIATOR_UPGRADE];
  const solarPanelsCategory =
    renovationCategories[RenovationCategoryType.SOLAR_PANELS];
  const smartHomeCategory =
    renovationCategories[RenovationCategoryType.SMART_HOME_ENERGY_MANAGEMENT];
  const electricVehicleCategory =
    renovationCategories[RenovationCategoryType.ELECTRIC_VEHICLE_CHARGING];
  const ageAppropriateLivingCategory =
    renovationCategories[RenovationCategoryType.AGE_APPROPRIATE_LIVING];

  return (
    <Stack>
      <RenovationSummaryPanel
        totalCost={currentCost}
        hasUnsavedRenovations={hasUnsavedChanges}
        onApplyRenovations={submit}
        isApplyingRenovations={isSubmitting}
        selectedMeasures={allSelectedMeasures}
        onRemoveMeasure={removeMeasure}
      />
      {!isIdle && (
        <Stack>
          <Stack gap={0}>
            <Title order={3} mb="md">
              {t("pages.calculationResult.categories.buildingRenovation.title")}
            </Title>
            <Paper radius="sm" p={8}>
              <Accordion chevronPosition="left" variant="contained" bg="white">
                {basementCategory && (
                  <BasementCategory
                    category={basementCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {facadeCategory && (
                  <FacadeCategory
                    category={facadeCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {roofCategory && (
                  <RoofCategory
                    category={roofCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {windowsCategory && (
                  <WindowsCategory
                    category={windowsCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {doorCategory && (
                  <DoorCategory
                    category={doorCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
              </Accordion>
            </Paper>
          </Stack>
          <Stack gap={0}>
            <Title order={3} mb="md">
              {t(
                "pages.calculationResult.categories.heatingAndVentilation.title",
              )}
            </Title>
            <Paper radius="sm" p={8}>
              <Accordion chevronPosition="left" variant="contained">
                {heatingSystemCategory && (
                  <HeatingSystemCategory
                    category={heatingSystemCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {surfaceHeatingCategory && (
                  <SurfaceHeatingCategory
                    category={surfaceHeatingCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {radiatorCategory && (
                  <RadiatorCategory
                    category={radiatorCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
              </Accordion>
            </Paper>
          </Stack>
          <Stack gap={0}>
            <Title order={3} mb="md">
              {t("pages.calculationResult.categories.energyAndComfort.title")}
            </Title>
            <Paper radius="sm" p={8}>
              <Accordion chevronPosition="left" variant="contained" bg="white">
                {solarPanelsCategory && (
                  <SolarPanelsCategory
                    category={solarPanelsCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {smartHomeCategory && (
                  <SmartHomeCategory
                    category={smartHomeCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {electricVehicleCategory && (
                  <ElectricVehicleCategory
                    category={electricVehicleCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
                {ageAppropriateLivingCategory && (
                  <AgeAppropriateLivingCategory
                    category={ageAppropriateLivingCategory}
                    onMeasureSelectionChange={changeMeasuresSelection}
                  />
                )}
              </Accordion>
            </Paper>
          </Stack>
        </Stack>
      )}
    </Stack>
  );
};
