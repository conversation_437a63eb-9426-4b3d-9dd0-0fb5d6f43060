import { keyBy, values } from "lodash";
import { use<PERSON>allback, useMemo, useReducer } from "react";

import { useApplyRenovationsMutation } from "@data-access/calculation/calculation-queries";
import { RenovationMeasure } from "@data-access/renovation/renovation-dto";
import { useGetCalculationGroupedRenovationsQuery } from "@data-access/renovation/renovation-queries.ts";
import { RenovationCategoryType } from "@data-access/renovation/renovation-types.ts";

export type RenovationMeasureSelection = {
  category: RenovationCategoryType;
  measureId: string;
  selected: boolean;
};

enum StateStatus {
  IDLE = "IDLE",
  SYNCHRONIZED = "SYNCHRONIZED",
  DIRTY = "DIRTY",
}

type State = {
  selectedRenovationIds: string[];
  selectedRenovationLastRemoteState: string[];
  status: StateStatus;
};

type Action =
  | { type: "SET_MEASURES"; payload: RenovationMeasureSelection[] }
  | { type: "REMOTE_UPDATE"; payload: string[] }
  | { type: "REMOVE_MEASURE"; payload: string }
  | { type: "CONFIRM_SUBMIT" };

const initialState: State = {
  selectedRenovationIds: [],
  selectedRenovationLastRemoteState: [],
  status: StateStatus.IDLE,
};

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case "SET_MEASURES": {
      const toRemove = action.payload.filter(({ selected }) => !selected);
      const toAdd = action.payload.filter(({ selected }) => selected);
      const workingSet = new Set(state.selectedRenovationIds);

      toRemove.forEach(({ measureId: id }) => workingSet.delete(id));
      toAdd.forEach(({ measureId: id }) => workingSet.add(id));

      return {
        ...state,
        selectedRenovationIds: Array.from(workingSet),
        status:
          state.status === StateStatus.DIRTY ? state.status : StateStatus.DIRTY,
      };
    }
    case "REMOTE_UPDATE": {
      return {
        selectedRenovationIds: action.payload,
        selectedRenovationLastRemoteState: [...action.payload],
        status: StateStatus.SYNCHRONIZED,
      };
    }
    case "CONFIRM_SUBMIT": {
      return {
        ...state,
        selectedRenovationLastRemoteState: [...state.selectedRenovationIds],
        status: StateStatus.SYNCHRONIZED,
      };
    }
    case "REMOVE_MEASURE": {
      return {
        ...state,
        selectedRenovationIds: state.selectedRenovationIds.filter(
          (id) => id !== action.payload
        ),
      };
    }
    default:
      return state;
  }
}

export const useRemoteRenovationState = (buildingId: string) => {
  const { data: categories } =
    useGetCalculationGroupedRenovationsQuery(buildingId);
  const { mutate: applyRenovations, isPending } = useApplyRenovationsMutation(
    buildingId,
    () => dispatch({ type: "CONFIRM_SUBMIT" })
  );
  const [state, dispatch] = useReducer(reducer, initialState);

  const setMeasuresSelection = useCallback(
    (measures: RenovationMeasureSelection[]) => {
      dispatch({ type: "SET_MEASURES", payload: measures });
    },
    []
  );

  const selectedIdsStr = String(state.selectedRenovationIds);

  const { renovationCategories, allSelectedMeasures } = useMemo(() => {
    let categoriesList = [];

    if (state.status === StateStatus.IDLE && categories) {
      //NOTE: If status is idle or synchronized, we take all the BE changes as the source of truth
      const measures = categories.flatMap(({ measures }) => measures);
      const selectedIds = measures
        .filter(({ selected }) => selected)
        .map(({ id }) => id);
      dispatch({ type: "REMOTE_UPDATE", payload: selectedIds });
      categoriesList = categories;
    } else {
      //NOTE: otherwise we use the local state to mark selected measures
      categoriesList =
        categories?.map((category) => {
          return {
            ...category,
            measures: category.measures.map((measure) => {
              return {
                ...measure,
                selected: state.selectedRenovationIds.includes(measure.id),
              };
            }),
          };
        }) || [];
    }

    const renovationCategories = keyBy(categoriesList, "key");
    const allSelectedMeasures = values(renovationCategories)
      .flatMap(({ measures }) => measures)
      .filter(({ selected }) => selected);

    return {
      renovationCategories,
      allSelectedMeasures,
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.status, categories, selectedIdsStr]);

  const currentCost = useMemo(() => {
    return values(renovationCategories)
      .map(({ measures }) => measures)
      .map((measures) => {
        return measures
          .filter(({ selected }) => selected)
          .reduce((categoryCost, measure) => {
            return categoryCost + (measure.cost?.mostProbableValue || 0);
          }, 0);
      })
      .reduce((totalCost, categoryCost) => {
        return totalCost + categoryCost;
      }, 0);
  }, [renovationCategories]);

  const hasUnsavedChanges = useMemo(() => {
    const sortedLocal = [...state.selectedRenovationIds].sort();
    const sortedRemote = [...state.selectedRenovationLastRemoteState].sort();
    return sortedLocal.toString() !== sortedRemote.toString();
  }, [state.selectedRenovationIds, state.selectedRenovationLastRemoteState]);

  return {
    hasUnsavedChanges,
    currentCost,
    renovationCategories,
    allSelectedMeasures,
    setMeasuresSelection,
    isSubmitting: isPending,
    isIdle: state.status === StateStatus.IDLE,
    submit: () => applyRenovations(state.selectedRenovationIds),
    removeMeasure: (measure: RenovationMeasure) => {
      dispatch({ type: "REMOVE_MEASURE", payload: measure.id });
    },
  };
};
