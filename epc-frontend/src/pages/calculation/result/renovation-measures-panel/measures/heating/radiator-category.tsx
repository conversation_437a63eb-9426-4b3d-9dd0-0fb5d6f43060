import { Accordion } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import { MeasureSelectionMultiple } from "../../components/measure-selection-multiple";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { TextLabelMode } from "../../utils/use-measure-text-value";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const RadiatorCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const { t } = useTranslation();
  const grants = useUniqCategoryGrants(category.measures);

  const systems = useMemo(() => {
    const basicEfficiency = category.measures.find(
      ({ renovationMeasureType }) =>
        renovationMeasureType === RenovationMeasureType.BASIC_EFFICIENCY
    );
    const highPerformance = category.measures.find(
      ({ renovationMeasureType }) =>
        renovationMeasureType === RenovationMeasureType.HIGH_PERFORMANCE
    );

    return [basicEfficiency, highPerformance].filter((i) => !!i);
  }, [category.measures]);

  return (
    <Accordion.Item value="radiator">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <MeasureSelectionMultiple
              label={t(
                `pages.calculationResult.categories.common.installationType`
              )}
              measures={systems}
              onChange={(value) => {
                const selection = value.map(
                  ({ id, renovationMeasureCategory, selected }) => ({
                    category: renovationMeasureCategory,
                    measureId: id,
                    selected,
                  })
                );
                onMeasureSelectionChange(selection);
              }}
              mode={TextLabelMode.TYPE}
            />
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
