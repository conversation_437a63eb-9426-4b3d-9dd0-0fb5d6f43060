import { Accordion } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import {
  RenovationMeasureType,
} from "@data-access/renovation/renovation-types";

import { MeasureSelectionMultiple } from "../../components/measure-selection-multiple";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import {TextLabelMode} from "../../utils/use-measure-text-value.ts";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const SurfaceHeatingCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

    const { underfloorHeatingMeasures, wallHeatingMeasures, ceilingHeatingMeasures,  } = useMemo(
        () => ({
            underfloorHeatingMeasures: category.measures.filter(
                ({ renovationMeasureType }) =>
                    renovationMeasureType === RenovationMeasureType.UNDERFLOOR_HEATING
            ),

            wallHeatingMeasures: category.measures.filter(
                ({ renovationMeasureType }) =>
                    renovationMeasureType ===
                    RenovationMeasureType.WALL_HEATING
            ),

            ceilingHeatingMeasures: category.measures.filter(
                ({ renovationMeasureType }) =>
                    renovationMeasureType ===
                    RenovationMeasureType.CEILING_HEATING
            ),
        }),
        [category.measures]
    );

  return (
    <Accordion.Item value="surface-heating">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <>
              <MeasureSelectionMultiple
                measures={underfloorHeatingMeasures}
                onChange={(value) => {
                  const selection = value.map(
                    ({ id, renovationMeasureCategory, selected }) => ({
                      category: renovationMeasureCategory,
                      measureId: id,
                      selected,
                    })
                  );
                  onMeasureSelectionChange(selection);
                }}
                mode={TextLabelMode.VALUE}
              />
              <MeasureSelectionMultiple
                measures={wallHeatingMeasures}
                onChange={(value) => {
                  const selection = value.map(
                    ({ id, renovationMeasureCategory, selected }) => ({
                      category: renovationMeasureCategory,
                      measureId: id,
                      selected,
                    })
                  );
                  onMeasureSelectionChange(selection);
                }}
                mode={TextLabelMode.VALUE}
              />
              <MeasureSelectionMultiple
                measures={ceilingHeatingMeasures}
                onChange={(value) => {
                  const selection = value.map(
                    ({ id, renovationMeasureCategory, selected }) => ({
                      category: renovationMeasureCategory,
                      measureId: id,
                      selected,
                    })
                  );
                  onMeasureSelectionChange(selection);
                }}
                mode={TextLabelMode.VALUE}
              />
            </>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
