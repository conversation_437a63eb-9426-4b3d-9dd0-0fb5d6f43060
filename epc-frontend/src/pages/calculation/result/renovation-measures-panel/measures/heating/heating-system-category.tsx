import { Accordion, <PERSON><PERSON><PERSON>, <PERSON>ack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import { MeasureSelectionMultiple } from "../../components/measure-selection-multiple";
import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { TextLabelMode } from "../../utils/use-measure-text-value";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const HeatingSystemCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const { primarySourceMeasures, heatExchangeMeasures } = useMemo(
    () => ({
      primarySourceMeasures: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.HEATING_PRIMARY_SOURCE
      ),

      heatExchangeMeasures: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.HEATING_VENTILATION_HEAT_EXCHANGE
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="heating-system">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <>
              <Stack>
                <MeasureSelectionMultiple
                  measures={primarySourceMeasures}
                  onChange={(value) => {
                    const selection = value.map(
                      ({ id, renovationMeasureCategory, selected }) => ({
                        category: renovationMeasureCategory,
                        measureId: id,
                        selected,
                      })
                    );
                    onMeasureSelectionChange(selection);
                  }}
                  mode={TextLabelMode.VALUE}
                />
              </Stack>
              <Divider variant="dashed" />
              {heatExchangeMeasures && (
                <MeasureSelectionSingle
                  measure={heatExchangeMeasures}
                  onChange={onMeasureSelectionChange}
                />
              )}
            </>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
