import { Accordion, <PERSON>ack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const AgeAppropriateLivingCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const { barrierFreeBathroom, stairlift, wideningDoors } = useMemo(
    () => ({
      barrierFreeBathroom: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.BARRIER_FREE_BATHROOM
      ),
      stairlift: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.STAIRLIFT
      ),
      wideningDoors: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.WIDENING_DOORS
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="age-appropriate-living">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <Stack mt="md" gap="sm">
              {barrierFreeBathroom && (
                <MeasureSelectionSingle
                  measure={barrierFreeBathroom}
                  onChange={onMeasureSelectionChange}
                />
              )}
              {stairlift && (
                <MeasureSelectionSingle
                  measure={stairlift}
                  onChange={onMeasureSelectionChange}
                />
              )}
              {wideningDoors && (
                <MeasureSelectionSingle
                  measure={wideningDoors}
                  onChange={onMeasureSelectionChange}
                />
              )}
            </Stack>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
