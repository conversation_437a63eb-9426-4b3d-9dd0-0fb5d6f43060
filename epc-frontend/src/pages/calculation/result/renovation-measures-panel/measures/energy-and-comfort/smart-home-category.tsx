import { Accordion, Stack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const SmartHomeCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const { heatingControls, lightingAutomation } = useMemo(
    () => ({
      heatingControls: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.INTELLIGENT_HEATING_CONTROLS
      ),
      lightingAutomation: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.LIGHTING_AUTOMATION
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="smart-home">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <Stack mt="md" gap="sm">
              {heatingControls && (
                <MeasureSelectionSingle
                  measure={heatingControls}
                  onChange={onMeasureSelectionChange}
                />
              )}
              {lightingAutomation && (
                <MeasureSelectionSingle
                  measure={lightingAutomation}
                  onChange={onMeasureSelectionChange}
                />
              )}
            </Stack>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
