import { Accordion, Divider, <PERSON>ack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import { MeasureSelectionMultiple } from "../../components/measure-selection-multiple";
import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const WindowsCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const {
    glazingTypeMeasures,
    windowsShuttersMeasures,
    securityFeaturesMeasure,
  } = useMemo(
    () => ({
      glazingTypeMeasures: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.WINDOWS_GLAZING_TYPE
      ),
      windowsShuttersMeasures: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.WINDOWS_SHUTTERS
      ),
      securityFeaturesMeasure: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.WINDOWS_SECURITY_FEATURES
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="windows">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <>
              <Stack>
                <MeasureSelectionMultiple
                  measures={glazingTypeMeasures}
                  onChange={(value) => {
                    const selection = value.map(
                      ({ id, renovationMeasureCategory, selected }) => ({
                        category: renovationMeasureCategory,
                        measureId: id,
                        selected,
                      })
                    );
                    onMeasureSelectionChange(selection);
                  }}
                />
                <MeasureSelectionMultiple
                  measures={windowsShuttersMeasures}
                  onChange={(value) => {
                    const selection = value.map(
                      ({ id, renovationMeasureCategory, selected }) => ({
                        category: renovationMeasureCategory,
                        measureId: id,
                        selected,
                      })
                    );
                    onMeasureSelectionChange(selection);
                  }}
                />
              </Stack>
              <Divider variant="dashed" />
              {securityFeaturesMeasure && (
                <MeasureSelectionSingle
                  measure={securityFeaturesMeasure}
                  onChange={onMeasureSelectionChange}
                />
              )}
            </>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
