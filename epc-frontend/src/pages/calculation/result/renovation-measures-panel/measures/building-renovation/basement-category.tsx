import { Accordion, Divider, Stack } from "@mantine/core";
import { isEmpty } from "lodash";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import { MeasureSelectionMultiple } from "../../components/measure-selection-multiple";
import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const BasementCategory = ({
  category,
  onMeasureSelectionChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const {
    insulationMeasures,
    groundFloorInsulationMeasures,
    moistureProtectionMeasures,
  } = useMemo(
    () => ({
      insulationMeasures: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.BASEMENT_INSULATION
      ),
      groundFloorInsulationMeasures: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.GROUND_FLOOR_INSULATION
      ),
      moistureProtectionMeasures: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.BASEMENT_MOISTURE_PROTECTION
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="basement">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <>
              <Stack>
                {!isEmpty(insulationMeasures) && (
                  <MeasureSelectionMultiple
                    measures={insulationMeasures}
                    onChange={(value) => {
                      const selection = value.map(
                        ({ id, renovationMeasureCategory, selected }) => ({
                          category: renovationMeasureCategory,
                          measureId: id,
                          selected,
                        })
                      );
                      onMeasureSelectionChange(selection);
                    }}
                  />
                )}
                {!isEmpty(groundFloorInsulationMeasures) && (
                  <MeasureSelectionMultiple
                    measures={groundFloorInsulationMeasures}
                    onChange={(value) => {
                      const selection = value.map(
                        ({ id, renovationMeasureCategory, selected }) => ({
                          category: renovationMeasureCategory,
                          measureId: id,
                          selected,
                        })
                      );
                      onMeasureSelectionChange(selection);
                    }}
                  />
                )}
              </Stack>
              <Divider variant="dashed" />
              {moistureProtectionMeasures && (
                <MeasureSelectionSingle
                  measure={moistureProtectionMeasures}
                  onChange={onMeasureSelectionChange}
                />
              )}
            </>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
