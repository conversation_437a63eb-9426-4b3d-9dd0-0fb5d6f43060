import { Accordion, Stack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types";

import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const DoorCategory = ({ category, onMeasureSelectionChange }: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const { doorReplacementMeasure, securityFeaturesMeasure } = useMemo(
    () => ({
      doorReplacementMeasure: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.DOORS_REPLACEMENT
      ),
      securityFeaturesMeasure: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.DOORS_SECURITY_FEATURES
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="door">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <Stack mt="md" gap="sm">
              {doorReplacementMeasure && (
                <MeasureSelectionSingle
                  measure={doorReplacementMeasure}
                  onChange={onMeasureSelectionChange}
                />
              )}
              {securityFeaturesMeasure && (
                <MeasureSelectionSingle
                  measure={securityFeaturesMeasure}
                  onChange={onMeasureSelectionChange}
                />
              )}
            </Stack>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
