import { Accordion, Di<PERSON>r, <PERSON>ack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types.ts";

import { MeasureSelectionMultiple } from "../../components/measure-selection-multiple";
import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const FacadeCategory = ({
  category,
  onMeasureSelectionChange: onCategoryChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const { wallInsulationMeasures, claddingMeasure } = useMemo(
    () => ({
      wallInsulationMeasures: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.FACADE_EXTERNAL_WALL_INSULATION
      ),
      claddingMeasure: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType ===
          RenovationMeasureType.FACADE_EXTERIOR_CLADDING
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="insulation">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <>
              <Stack>
                <MeasureSelectionMultiple
                  measures={wallInsulationMeasures}
                  onChange={(value) => {
                    const selection = value.map(
                      ({ id, renovationMeasureCategory, selected }) => ({
                        category: renovationMeasureCategory,
                        measureId: id,
                        selected,
                      })
                    );
                    onCategoryChange(selection);
                  }}
                />
              </Stack>
              <Divider variant="dashed" />
              {claddingMeasure && (
                <MeasureSelectionSingle
                  measure={claddingMeasure}
                  onChange={onCategoryChange}
                />
              )}
            </>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
