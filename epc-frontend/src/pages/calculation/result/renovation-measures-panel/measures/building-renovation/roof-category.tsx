import { Accordion, Divider, <PERSON>ack } from "@mantine/core";
import { useMemo } from "react";

import { RenovationCategory } from "@data-access/renovation/renovation-dto.ts";
import { RenovationMeasureType } from "@data-access/renovation/renovation-types.ts";

import { MeasureSelectionMultiple } from "../../components/measure-selection-multiple";
import { MeasureSelectionSingle } from "../../components/measure-selection-single";
import { RenovationCategoryBody } from "../../components/renovation-category/renovation-category-body";
import { RenovationCategoryHeader } from "../../components/renovation-category/renovation-category-header";
import { RenovationMeasureSelection } from "../../state/use-renovation-state";
import { useUniqCategoryGrants } from "../../utils/use-uniq-category-grants";

type Props = {
  category: RenovationCategory;
  onMeasureSelectionChange: (selection: RenovationMeasureSelection[]) => void;
};

export const RoofCategory = ({
  category,
  onMeasureSelectionChange: onCategoryChange,
}: Props) => {
  const grants = useUniqCategoryGrants(category.measures);

  const {
    roofInsulationMeasures,
    roofCoverageMeasure,
    roofReinforcementMeasure,
  } = useMemo(
    () => ({
      roofInsulationMeasures: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.ROOF_INSULATION
      ),
      roofCoverageMeasure: category.measures.filter(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.ROOF_NEW_COVERAGE
      ),
      roofReinforcementMeasure: category.measures.find(
        ({ renovationMeasureType }) =>
          renovationMeasureType === RenovationMeasureType.ROOF_RE_ENFORCEMENT
      ),
    }),
    [category.measures]
  );

  return (
    <Accordion.Item value="roof">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader category={category} />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <>
              <Stack>
                <MeasureSelectionMultiple
                  measures={roofInsulationMeasures}
                  onChange={(value) => {
                    const selection = value.map(
                      ({ id, renovationMeasureCategory, selected }) => ({
                        category: renovationMeasureCategory,
                        measureId: id,
                        selected,
                      })
                    );
                    onCategoryChange(selection);
                  }}
                />
              </Stack>
              <Stack>
                <MeasureSelectionMultiple
                  measures={roofCoverageMeasure}
                  onChange={(value) => {
                    const selection = value.map(
                      ({ id, renovationMeasureCategory, selected }) => ({
                        category: renovationMeasureCategory,
                        measureId: id,
                        selected,
                      })
                    );
                    onCategoryChange(selection);
                  }}
                />
              </Stack>
              <Divider variant="dashed" />
              {roofReinforcementMeasure && (
                <MeasureSelectionSingle
                  measure={roofReinforcementMeasure}
                  onChange={onCategoryChange}
                />
              )}
            </>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
