import { useMemo } from "react";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

import { useUniqCategoryGrants } from "./use-uniq-category-grants";

export const useUniqCategoryGrantNames = (measures: RenovationMeasure[]) => {
  const uniqGrants = useUniqCategoryGrants(measures);

  return useMemo(() => {
    return uniqGrants.map(({ name }) => name);
  }, [uniqGrants]);
};
