import { useTranslation } from "react-i18next";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";
import {
  RenovationMeasureType,
  RenovationMeasureVariant,
} from "@data-access/renovation/renovation-types";

export enum TextLabelMode {
  VARIANT = "VARIANT",
  TYPE = "TYPE",
  BADGE = "BADGE",
  VALUE = "VALUE",
  VALUE_BADGE = "VALUE_BADGE",
}

const i18nKeyStrategy = {
  [TextLabelMode.VARIANT]: (measure: RenovationMeasure) =>
    `dataAccess.renovationMeasureVariant.${measure.renovationMeasureVariant}`,
  [TextLabelMode.TYPE]: (measure: RenovationMeasure) =>
    `pages.calculationResult.categories.texts.${measure.renovationMeasureCategory}.measures.${measure.renovationMeasureType}.label`,
  [TextLabelMode.BADGE]: (measure: RenovationMeasure) =>
    `pages.calculationResult.categories.texts.${measure.renovationMeasureCategory}.measures.${measure.renovationMeasureType}.badge`,
  [TextLabelMode.VALUE]: (measure: RenovationMeasure) =>
    `pages.calculationResult.categories.texts.${measure.renovationMeasureCategory}.measures.${measure.renovationMeasureType}.values.${measure.renovationMeasureValue}.label`,
  [TextLabelMode.VALUE_BADGE]: (measure: RenovationMeasure) =>
    `pages.calculationResult.categories.texts.${measure.renovationMeasureCategory}.measures.${measure.renovationMeasureType}.values.${measure.renovationMeasureValue}.badge`,
};

export const useMeasureTextSelectionValue = (
  measure: RenovationMeasure,
  mode: TextLabelMode = TextLabelMode.VARIANT
) => {
  const { t } = useTranslation();
  return t(i18nKeyStrategy[mode](measure));
};


const measureTypesWithValueText = new Set([
  RenovationMeasureType.HEATING_PRIMARY_SOURCE,
  RenovationMeasureType.UNDERFLOOR_HEATING,
  RenovationMeasureType.WALL_HEATING,
  RenovationMeasureType.CEILING_HEATING,
  RenovationMeasureType.IMMERSION_HEATER,
]);

export const useMeasureFullTextValue = (
  measure: RenovationMeasure,
  isShortText: boolean = false
) => {
  const baseLabel = useMeasureTextSelectionValue(
    measure,
    isShortText ? TextLabelMode.BADGE : TextLabelMode.TYPE
  );
  const valueLabel = useMeasureTextSelectionValue(
    measure,
    isShortText ? TextLabelMode.VALUE_BADGE : TextLabelMode.VALUE
  );
  const variantLabel = useMeasureTextSelectionValue(
    measure,
    TextLabelMode.VARIANT
  );

  if (measure.renovationMeasureVariant !== RenovationMeasureVariant.DEFAULT) {
    return `${baseLabel}: ${variantLabel}`;
  }

  if (measureTypesWithValueText.has(measure.renovationMeasureType)) {
    return `${baseLabel}: ${valueLabel}`;
  }

  return baseLabel;
};
