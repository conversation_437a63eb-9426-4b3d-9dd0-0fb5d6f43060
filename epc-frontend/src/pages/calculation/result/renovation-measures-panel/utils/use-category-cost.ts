import { groupBy, map, max, min, sum, sumBy } from "lodash";
import { useMemo } from "react";

import { RenovationMeasure } from "@data-access/renovation/renovation-dto.ts";

export type CategoryCost = {
  currentCost?: number;
  range?: { min: number; max: number };
};

export const useCategoryCost = (measures: RenovationMeasure[]) => {
  return useMemo(() => {
    const selectedMeasures = measures.filter(({ selected }) => selected);
    const hasMissingProbableValue = selectedMeasures.some(
      ({ cost }) => !cost?.mostProbableValue,
    );
    const currentCost = sumBy(selectedMeasures, "cost.mostProbableValue") || 0;

    const normalizedCurrentCost =
      hasMissingProbableValue || Number.isNaN(currentCost)
        ? undefined
        : currentCost;

    const hasUndefinedRange = measures.some(
      (measure) => !measure.cost?.range.first || !measure.cost?.range.second,
    );

    if (hasUndefinedRange) {
      return {
        currentCost: normalizedCurrentCost,
        range: undefined,
      } as CategoryCost;
    }

    const minVal = min(map(measures, "cost.range.first") as number[]);

    const groupedMeasures = groupBy(measures, "renovationMeasureType");
    const maxVal = sum(
      Object.values(groupedMeasures).map(
        (measures) => max(map(measures, "cost.range.second")) as number,
      ),
    );

    return {
      currentCost: normalizedCurrentCost,
      range: { min: minVal!, max: maxVal },
    } as CategoryCost;
  }, [measures]);
};
