import { Stack } from "@mantine/core";

import { RenovationSummaryReviewPanel } from "./components/renovation-summary/renovation-summary-review-panel";
import { useRemoteRenovationState } from "./state/use-renovation-state";

type Props = {
  buildingId: string;
};

export const RenovationMeasuresReviewPanel = ({ buildingId }: Props) => {
  const { currentCost, allSelectedMeasures } =
    useRemoteRenovationState(buildingId);

  return (
    <Stack>
      <RenovationSummaryReviewPanel
        totalCost={currentCost}
        selectedMeasures={allSelectedMeasures}
      />
    </Stack>
  );
};
