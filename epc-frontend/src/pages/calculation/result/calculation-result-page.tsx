import { <PERSON><PERSON>, Group, ScrollArea, Stack } from "@mantine/core";
import { useInViewport } from "@mantine/hooks";
import {
  IconArrowUp,
  IconFileTypePdf,
  IconHomeEdit,
} from "@tabler/icons-react";
import { useContext, useRef } from "react";
import { createPortal } from "react-dom";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";

import { LayoutContext } from "@providers/layout/layout-context.ts";
import { useSummaryPdfExport } from "@providers/pdf-export/use-summary-pdf-export.ts";
import { routes } from "@providers/router/router.tsx";
import { FinPageLayoutColumn } from "@shared/components/page/fin-page-layout-column.tsx";
import { FinPage } from "@shared/components/page/fin-page.tsx";

import { OrgClosableHeader } from "../../routing-layout-elements/org-closable-header.tsx";
import { CalculationResultPanel } from "./calculation-result-panel/calculation-result-panel.tsx";
import { EnergyEfficiencyPanel } from "./energy-efficiency-panel/energy-efficiency-panel.tsx";
import { RenovationMeasuresPanel } from "./renovation-measures-panel/renovation-measures-panel.tsx";

import classes from "./calculation-result-page.module.css";

export const CalculationResultPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const scrollArea = useRef<HTMLDivElement>(null);

  const { ref, inViewport: isEnergyEfficiencyPanelVisible } = useInViewport();

  const { getHeaderPortalRef } = useContext(LayoutContext);
  const { buildingId } = useParams<{ buildingId: string }>();
  const { mutate: exportPdf, isPending: exportInProgress } =
    useSummaryPdfExport();

  if (!buildingId) return null;
  return (
    <FinPage>
      {createPortal(
        <OrgClosableHeader
          buttonsSection={
            <Group>
              <Button
                variant="outline"
                onClick={() =>
                  navigate(routes.calculation.building.parse(buildingId))
                }
                leftSection={<IconHomeEdit size={24} stroke={1.5} />}
              >
                {t("pages.calculationResult.cta.editBuildingData")}
              </Button>
              <Button
                variant="outline"
                leftSection={<IconFileTypePdf size={24} stroke={1.5} />}
                onClick={() => exportPdf(buildingId)}
                loading={exportInProgress}
              >
                {t("pages.calculationResult.cta.exportPdf")}
              </Button>
            </Group>
          }
        />,
        getHeaderPortalRef().current!,
      )}
      <ScrollArea h="100%" viewportRef={scrollArea}>
        <FinPageLayoutColumn>
          <div
            className={classes.calculationResultPageContent}
            ref={scrollArea}
          >
            <Stack
              gap="20"
              className={classes.calculationResultPageRenovations}
            >
              <EnergyEfficiencyPanel buildingId={buildingId} />
              <RenovationMeasuresPanel buildingId={buildingId} />
            </Stack>
            <Stack gap="20" h="100%">
              <CalculationResultPanel
                buildingId={buildingId}
                energyResultPanelRef={ref}
              />
              {!isEnergyEfficiencyPanelVisible && (
                <Button
                  leftSection={<IconArrowUp size={24} stroke={1.5} />}
                  variant="filled"
                  size="lg"
                  radius="xl"
                  color="teal.6"
                  pos="fixed"
                  bottom={60}
                  onClick={() => {
                    scrollArea.current?.scrollTo({
                      top: 0,
                      behavior: "smooth",
                    });
                  }}
                >
                  {t(
                    "pages.calculationResult.categories.common.reviewAndApplyRenovations",
                  )}
                </Button>
              )}
            </Stack>
          </div>
        </FinPageLayoutColumn>
      </ScrollArea>
    </FinPage>
  );
};
