import {
  Button,
  getThemeColor,
  Group,
  Paper,
  Select,
  Stack,
  Text,
  Title,
  useMantineTheme,
} from "@mantine/core";
import {
  IconAlertCircle,
  IconCircleCheck,
  IconSend,
} from "@tabler/icons-react";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  useCreateEuropaceKundenangabenCaseMutation,
  useGetEuropaceKundenangabenCaseQuery, useGetEuropaceKundenangabenCaseExistQuery,
} from "@data-access/europace-integration/europace-integration-queries.ts";

import { useMissingEuropaceIntegration } from "./use-missing-europace-integration-notification.tsx";

type Props = {
  buildingId: string;
  hasUpdateOption?: boolean;
};

export const EuropacePanel = ({
  buildingId,
  hasUpdateOption = true,
}: Props) => {
  const theme = useMantineTheme();
  const notifyMissingIntegration = useMissingEuropaceIntegration();
  const [caseType, setCaseType] = useState<string | null>(null);

  const { mutate: send, isPending: isSendPending } =
    useCreateEuropaceKundenangabenCaseMutation(buildingId, {
      onError: (error) => {
        if (error.response?.status === 404) {
          notifyMissingIntegration();
        }
      },
    });

  const { data: hasEuropaceKundenangabenCaseExist } = useGetEuropaceKundenangabenCaseExistQuery(buildingId);

  const { data } = useGetEuropaceKundenangabenCaseQuery(hasEuropaceKundenangabenCaseExist, buildingId);
  const { t } = useTranslation();

  const { isSent, isCtaActive } = useMemo(() => {
    const isSent = hasEuropaceKundenangabenCaseExist;
    return {
      isSent,
      isCtaActive: !isSent || !!hasUpdateOption || !caseType,
    };
  }, [hasEuropaceKundenangabenCaseExist, hasUpdateOption]);

  const handleSend = () => {
    if (caseType) {
      send({ caseType });
    }
  };

  return (
    <Paper px="30" py="20">
      <Stack gap={8}>
        <Group gap={4} justify="space-between">
          <Title order={4}>{t("integration.europace.BaufiSmart")}</Title>
          <Group gap={2}>
            {isSent ? (
              <>
                <IconCircleCheck
                  color={getThemeColor("teal.6", theme)}
                  stroke={2}
                  size={20}
                />
                <Text fz="lg">{t("integration.europace.sent")}</Text>
              </>
            ) : (
              <>
                <IconAlertCircle stroke={2} size={18} />
                <Text fz="lg">{t("integration.europace.notSent")}</Text>
              </>
            )}
          </Group>
        </Group>

        <Stack gap={2}>
          <Group gap={4}>
            <Text fz="sm">{t("integration.europace.caseId")}:</Text>
            <Text fz="sm" fw="600">
              {data?.caseNo || "--"}
            </Text>
          </Group>

          {isSent && (
            <Text fz="xs">
              {t("integration.europace.integrationTime", {
                date: data?.creationDate.toLocaleString(),
              })}
            </Text>
          )}
        </Stack>
        <Group justify="flex-end">
          {isCtaActive && (
            <Group gap="md">
              <Select
                size="xs"
                w={250}
                placeholder={t("integration.europace.caseType.placeholder")}
                value={caseType}
                onChange={setCaseType}
                data={[
                  { value: "PURCHASE", label: t("integration.europace.caseType.purchase") },
                  { value: "MODERNIZATION", label: t("integration.europace.caseType.modernization") },
                  { value: "PROLONGATION", label: t("integration.europace.caseType.prolongation") },
                ]}
              />
              {isCtaActive && (
                <Button
                  size="xs"
                  rightSection={<IconSend stroke={1.5} />}
                  variant="default"
                  onClick={handleSend}
                  loading={isSendPending}
                  disabled={!caseType}
                >
                  {isSent
                    ? t("integration.europace.cta.update")
                    : t("integration.europace.cta.send")}
                </Button>
              )}
            </Group>
          )}
        </Group>
      </Stack>
    </Paper>
  );
};
