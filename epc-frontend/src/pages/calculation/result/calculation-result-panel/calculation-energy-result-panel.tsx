import { Stack, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";

import { CalculationResultDto } from "@data-access/calculation/calculation-dto.ts";

import { CalculationResultCategory } from "./calculation-result-category.tsx";

type Props = {
  result: CalculationResultDto;
};

export const CalculationEnergyResultPanel = ({ result }: Props) => {
  const { t } = useTranslation();

  return (
    <Stack gap="12">
      <Title order={4}>
        {t("pages.calculationResult.results.energyIndex")}
      </Title>
      <CalculationResultCategory
        label={t("pages.calculationResult.results.primaryEnergyDemand")}
        value={result.primaryEnergyDemand}
        unit={t("core.units.energyDemand")}
      />
      <CalculationResultCategory
        label={t("pages.calculationResult.results.finalEnergyDemandPerArea")}
        value={result.finalEnergyDemandPerArea}
        unit={t("core.units.energyDemand")}
      />
      <CalculationResultCategory
        label={t("pages.calculationResult.results.co2Emission")}
        value={result.co2Emissions}
        unit={t(`core.units.emission.${result.co2EmissionsUnit}`)}
      />
      <CalculationResultCategory
        label={t("pages.calculationResult.results.solarEnergyProd")}
        value={result.solarEnergyProduction}
        unit={t("core.units.energyPerAnnum")}
      />
    </Stack>
  );
};
