import { LoadingOverlay, Paper, Stack } from "@mantine/core";

import {
  useGetCalculationResultQuery,
} from "@data-access/calculation/calculation-queries.ts";
import { useGetProfitabilityQuery } from "@data-access/renovation/renovation-queries.ts";

import { CalculationEnergyResultPanel } from "./calculation-energy-result-panel.tsx";
import { CalculationProfitabilityPanel } from "./calculation-profitability-panel.tsx";
import { EuropacePanel } from "./intergartion/europace/europace-panel.tsx";

import classes from "./calculation-result-panel.module.css";

type Props = {
  buildingId: string;
  isSummaryMode?: boolean;
};

export const CalculationResultPanel = ({
  buildingId,
  isSummaryMode = false,
}: Props) => {
  const { data: profitabilityResult, isPending: isPortabilityPending } =
    useGetProfitabilityQuery(buildingId);
  const { data: energyResult, isPending: isEnergyPending } =
    useGetCalculationResultQuery(buildingId);

  const latestEnergyResult =
    energyResult && (energyResult.future || energyResult.current);

  return (
    <Stack w="300" gap="24" className={classes.calculationResultPanel}>
      <EuropacePanel buildingId={buildingId} hasUpdateOption={!isSummaryMode} />
      <Paper h="330" p="30" pos="relative">
        <LoadingOverlay
          visible={isEnergyPending}
          overlayProps={{ radius: "sm", blur: 2 }}
        />
        {latestEnergyResult && (
          <CalculationEnergyResultPanel result={latestEnergyResult} />
        )}
      </Paper>
      <Paper p="30" pos="relative">
        <LoadingOverlay
          visible={isPortabilityPending}
          overlayProps={{ radius: "sm", blur: 2 }}
        />
        {profitabilityResult && (
          <CalculationProfitabilityPanel profitability={profitabilityResult} />
        )}
      </Paper>
    </Stack>
  );
};
