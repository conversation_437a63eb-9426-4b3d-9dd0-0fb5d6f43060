import { Accordion, Paper, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";

import { FacadeCategory } from "./measures/facade-category";
import { RoofCategory } from "./measures/roof-category";
import { WindowsCategory } from "./measures/windows-category";
import { facadeRenovationCategoryMock } from "./mock";

export const NewRenovationMeasuresPanel = () => {
  const { t } = useTranslation();

  return (
    <Paper p="md" radius="md">
      <Title order={3} mb="md">
        {t("pages.calculationResult.categories.buildingRenovation.title")}
      </Title>
      <Accordion chevronPosition="left" variant="contained">
        <FacadeCategory
          category={facadeRenovationCategoryMock}
          onCategoryChange={console.log}
        />
        <RoofCategory />
        <WindowsCategory />
      </Accordion>
    </Paper>
  );
};
