import { FacadeRenovationCategory } from "@data-access/renovation/renovation-dto";
import { MeasureQuality } from "@data-access/renovation/renovation-types";

export const facadeRenovationCategoryMock: FacadeRenovationCategory = {
  id: "facade",
  name: "Facade",
  cost: {
    min: 100000,
    max: 150000,
    estimated: 120000,
  },
  grants: [
    {
      id: "kfw-261",
      name: "KfW 261",
      subtitle: "Bundesförderung für effiziente Gebäude (BEG)",
      description: "Zinsgünstiger Kredit + Zuschuss bis zu 45%",
    },
    {
      id: "kfw-270",
      name: "KfW 270",
      subtitle: "Erneuerbare Energien – Standard",
      description: "Zinsgünstiger Kredit",
    },
  ],
  recommended: true,
  measuresById: {
    insulation: {
      id: "insulation",
      name: "Facade insulation",
      value: MeasureQuality.ADVANCED,
      displayValue: "Insulation: High quality",
      active: true,
      recommended: true,
      cost: {
        min: 80000,
        max: 120000,
        estimated: 100000,
      },
    },
    cladding: {
      id: "cladding",
      name: "External cladding",
      value: true,
      displayValue: "Cladding",
      active: true,
      recommended: true,
      cost: {
        min: 10000,
        max: 15000,
        estimated: 12000,
      },
    },
  },
};
