import {
  Badge,
  Group,
  Stack,
  Text,
  Title,
  useMantineTheme,
} from "@mantine/core";
import {
  IconCheck,
  IconCircleDashed,
  IconStar,
  IconX,
} from "@tabler/icons-react";
import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  RenovationMeasure,
  Grant,
} from "@data-access/renovation/renovation-dto";

import { RenovationCostDisplay } from "./renovation-cost-display";
import { RenovationGrantDisplay } from "./renovation-grant-display";
type RenovationCategoryLabelProps = {
  categoryName: string;
  selectedMeasures?: RenovationMeasure<unknown>[];
  estimatedCost?: number;
  minCost?: number;
  maxCost?: number;
  grants?: Grant[];
  isRecommended?: boolean;
};

export const RenovationCategoryHeader: FC<RenovationCategoryLabelProps> = ({
  categoryName,
  selectedMeasures,
  estimatedCost,
  minCost,
  maxCost,
  grants,
  isRecommended = false,
}) => {
  const theme = useMantineTheme();
  const { t } = useTranslation();

  const hasSelectedMeasures = selectedMeasures && selectedMeasures.length > 0;

  return (
    <>
      {isRecommended && (
        <Badge
          pos="absolute"
          top="0"
          right="0"
          radius="sm"
          variant="light"
          color="teal.5"
          leftSection={<IconStar size={12} />}
        >
          {t("pages.calculationResult.categories.common.recommended")}
        </Badge>
      )}
      <Stack pt="sm" px="md" gap="sm">
        <Group justify="space-between" align="center" wrap="nowrap">
          <Stack gap="xs">
            <Group gap="xs">
              {hasSelectedMeasures ? (
                <IconCheck size={20} stroke={3} color={theme.colors.teal[6]} />
              ) : (
                <IconCircleDashed
                  size={20}
                  stroke={2}
                  color={theme.colors.gray[6]}
                />
              )}
              <Title order={4}>{categoryName}</Title>
            </Group>
            <Group>
              {hasSelectedMeasures ? (
                selectedMeasures.map((measure) => (
                  <Badge
                    key={measure.id}
                    variant="light"
                    leftSection={<IconX size={16} />}
                    radius="sm"
                  >
                    {measure.displayValue}
                  </Badge>
                ))
              ) : (
                <Text size="sm" c={theme.colors.gray[6]}>
                  {t(
                    "pages.calculationResult.categories.common.noMeasuresSelected"
                  )}
                </Text>
              )}
            </Group>
          </Stack>
          <Group align="flex-start">
            <RenovationGrantDisplay
              grants={grants ? grants.map(({ name }) => name) : []}
            />
            <RenovationCostDisplay
              estimatedCost={estimatedCost}
              minCost={minCost}
              maxCost={maxCost}
            />
          </Group>
        </Group>
      </Stack>
    </>
  );
};
