import { Stack, Text } from "@mantine/core";

type RenovationCategoryDataProps = {
  label?: string;
  value?: string;
  context?: string;
  minWidth?: number;
};

export const RenovationCategoryData = ({
  label: topText,
  value: middleText,
  context: bottomText,
  minWidth = 150,
}: RenovationCategoryDataProps) => {
  const emptyValue = "--";

  return (
    <Stack gap="0" miw={minWidth} flex="0 0 auto">
      <Text fz="sm">{topText}</Text>
      <Text fw={600}>{middleText !== undefined ? middleText : emptyValue}</Text>
      <Text fz="xs" c="gray.6">
        {bottomText}
      </Text>
    </Stack>
  );
};
