import { useTranslation } from "react-i18next";

import { RenovationCategoryData } from "./renovation-category-data";

type RenovationGrantDisplayProps = {
  grants: string[];
};

export const RenovationGrantDisplay = ({
  grants,
}: RenovationGrantDisplayProps) => {
  const { t } = useTranslation();

  return (
    <RenovationCategoryData
      label={t("pages.calculationResult.categories.common.possibleGrants")}
      value={grants.join(", ")}
    />
  );
};
