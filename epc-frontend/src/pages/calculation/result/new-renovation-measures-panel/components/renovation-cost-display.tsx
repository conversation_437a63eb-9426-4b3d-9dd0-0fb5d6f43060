import { useTranslation } from "react-i18next";

import { formatDecimal } from "@shared/utils/value-formatters";

import { RenovationCategoryData } from "./renovation-category-data";

type RenovationCostDisplayProps = {
  estimatedCost?: number;
  minCost?: number;
  maxCost?: number;
  currency?: string;
};

export const RenovationCostDisplay = ({
  estimatedCost,
  minCost,
  maxCost,
}: RenovationCostDisplayProps) => {
  const { t, i18n } = useTranslation();

  const hasFullRange = minCost !== undefined && maxCost !== undefined;

  const formattedCost =
    estimatedCost !== undefined
      ? `${formatDecimal(i18n.language, estimatedCost)} EUR`
      : undefined;

  const rangeText = hasFullRange
    ? t("pages.calculationResult.categories.common.range", {
        min: formatDecimal(i18n.language, minCost),
        max: formatDecimal(i18n.language, maxCost),
      })
    : undefined;

  return (
    <RenovationCategoryData
      label={t("pages.calculationResult.categories.common.estimatedCost")}
      value={formattedCost}
      context={rangeText}
    />
  );
};
