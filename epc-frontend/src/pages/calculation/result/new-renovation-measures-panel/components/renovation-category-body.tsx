import { Divider, Group, Paper, Stack, Text } from "@mantine/core";
import { ReactNode } from "react";

import { Grant } from "@data-access/renovation/renovation-dto";

type Props = {
  controls: ReactNode;
  grants: Grant[];
};

export const RenovationCategoryBody = ({ controls, grants }: Props) => {
  return (
    <Paper p="md" radius="md">
      <Group align="flex-start" gap="xl">
        <Stack flex={1}>{controls}</Stack>
        <Divider orientation="vertical" variant="dashed" />
        <Stack w={300} gap="md">
          {grants.map((grant, index) => (
            <Stack key={index} gap="xs">
              <Text fw={600}>{grant.name}</Text>
              <Text size="sm" c="dimmed">
                {grant.subtitle}
              </Text>
              <Text size="sm">{grant.description}</Text>
            </Stack>
          ))}
        </Stack>
      </Group>
    </Paper>
  );
};
