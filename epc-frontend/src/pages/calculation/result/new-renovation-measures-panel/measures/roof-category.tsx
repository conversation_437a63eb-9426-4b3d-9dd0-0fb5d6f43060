import { Accordion } from "@mantine/core";

import { MeasureQuality } from "@data-access/renovation/renovation-types";

import { RenovationCategoryHeader } from "../components/renovation-category-header";

export const RoofCategory = () => {
  return (
    <Accordion.Item value="roof">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader
          categoryName="Roof"
          selectedMeasures={[
            {
              id: "insulation",
              name: "Insulation",
              value: MeasureQuality.ADVANCED,
              displayValue: "Insulation: High quality",
              active: true,
              recommended: true,
              cost: {
                min: 80000,
                max: 120000,
                estimated: 100000,
              },
            },
          ]}
          estimatedCost={120000}
          minCost={90000}
          maxCost={150000}
          grants={[
            {
              id: "kfw-261",
              name: "KfW 261",
              subtitle: "Bundesförderung für effiziente Gebäude (BEG)",
              description: "Zinsgünstiger Kredit + Zuschuss bis zu 45%",
            },
          ]}
        />
      </Accordion.Control>
      <Accordion.Panel>TODO</Accordion.Panel>
    </Accordion.Item>
  );
};
