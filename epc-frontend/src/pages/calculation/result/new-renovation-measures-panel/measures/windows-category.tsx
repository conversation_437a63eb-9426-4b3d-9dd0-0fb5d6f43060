import { Accordion } from "@mantine/core";

import { RenovationCategoryHeader } from "../components/renovation-category-header";

export const WindowsCategory = () => {
  return (
    <Accordion.Item value="windows">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader
          categoryName="Windows"
          selectedMeasures={[]}
          estimatedCost={undefined}
          minCost={90000}
          maxCost={150000}
          grants={[
            {
              id: "kfw-261",
              name: "KfW 261",
              subtitle: "Bundesförderung für effiziente Gebäude (BEG)",
              description: "Zinsgünstiger Kredit + Zuschuss bis zu 45%",
            },
          ]}
          isRecommended
        />
      </Accordion.Control>
      <Accordion.Panel>TODO</Accordion.Panel>
    </Accordion.Item>
  );
};
