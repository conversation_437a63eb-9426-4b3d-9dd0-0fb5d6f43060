import { Accordion, Checkbox, Radio, Stack, Text } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { FacadeRenovationCategory } from "@data-access/renovation/renovation-dto";
import { MeasureQuality } from "@data-access/renovation/renovation-types";

import { RenovationCategoryBody } from "../components/renovation-category-body";
import { RenovationCategoryHeader } from "../components/renovation-category-header";

type Props = {
  category: FacadeRenovationCategory;
  onCategoryChange: (category: FacadeRenovationCategory) => void;
};

export const FacadeCategory = ({ category }: Props) => {
  const { t } = useTranslation();
  const {
    cost,
    grants,
    measuresById: { insulation, cladding },
  } = category;

  const activeMeasures = useMemo(
    () => [insulation, cladding].filter((measure) => measure.active),
    [insulation, cladding]
  );

  return (
    <Accordion.Item value="insulation">
      <Accordion.Control pos="relative">
        <RenovationCategoryHeader
          categoryName={category.name}
          selectedMeasures={activeMeasures}
          estimatedCost={cost.estimated}
          minCost={cost.min}
          maxCost={cost.max}
          grants={grants}
          isRecommended
        />
      </Accordion.Control>
      <Accordion.Panel>
        <RenovationCategoryBody
          controls={
            <>
              <Stack>
                <Text fw={500}>{insulation.name}</Text>
                <Radio.Group
                  value={insulation.value}
                  onChange={(value) => {
                    console.log(value);
                    // TODO: update the category
                  }}
                >
                  <Stack>
                    <Radio
                      size="sm"
                      value={MeasureQuality.BASIC}
                      label={t("dataAccess.renovationMeasureQuality.BASIC")}
                    />
                    <Radio
                      size="sm"
                      value={MeasureQuality.ADVANCED}
                      label={t("dataAccess.renovationMeasureQuality.ADVANCED")}
                    />
                  </Stack>
                </Radio.Group>
              </Stack>
              <Stack>
                <Text fw={500}>{cladding.name}</Text>
                <Checkbox
                  label={t(
                    "pages.calculationResult.categories.buildingRenovation.facade.claddingLabel"
                  )}
                />
              </Stack>
            </>
          }
          grants={grants}
        />
      </Accordion.Panel>
    </Accordion.Item>
  );
};
