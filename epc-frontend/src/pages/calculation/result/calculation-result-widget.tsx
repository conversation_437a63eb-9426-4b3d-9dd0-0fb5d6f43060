import { Stack } from "@mantine/core";

import { CalculationResultPanel } from "./calculation-result-panel/calculation-result-panel.tsx";
import { EnergyEfficiencyPanel } from "./energy-efficiency-panel/energy-efficiency-panel.tsx";
import { RenovationsMeasuresReviewPanel } from "./renovations-measures-panel/renovations-measures-review-panel.tsx";

import classes from "./calculation-result-widget.module.css";

type Props = {
  buildingId: string;
};

export const CalculationResultWidget = ({ buildingId }: Props) => {
  return (
    <div className={classes.calculationResultWidgetContent}>
      <Stack gap="20" className={classes.calculationResultWidgetRenovations}>
        <EnergyEfficiencyPanel buildingId={buildingId} />
        <RenovationsMeasuresReviewPanel buildingId={buildingId} />
      </Stack>
      <CalculationResultPanel buildingId={buildingId} isSummaryMode />
    </div>
  );
};
