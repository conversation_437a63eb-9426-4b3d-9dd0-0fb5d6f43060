import { <PERSON><PERSON>, Group, Stack } from "@mantine/core";
import { IconFileTypePdf, IconHomeEdit } from "@tabler/icons-react";
import { useContext } from "react";
import { createPortal } from "react-dom";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";

import { LayoutContext } from "@providers/layout/layout-context.ts";
import { useSummaryPdfExport } from "@providers/pdf-export/use-summary-pdf-export.ts";
import { routes } from "@providers/router/router.tsx";
import { FinPageLayoutColumn } from "@shared/components/page/fin-page-layout-column.tsx";
import { FinPage } from "@shared/components/page/fin-page.tsx";

import { OrgClosableHeader } from "../../routing-layout-elements/org-closable-header.tsx";
import { CalculationResultPanel } from "./calculation-result-panel/calculation-result-panel.tsx";
import { EnergyEfficiencyPanel } from "./energy-efficiency-panel/energy-efficiency-panel.tsx";
import { NewRenovationMeasuresPanel } from "./new-renovation-measures-panel/new-renovation-measures-panel.tsx";

import classes from "./calculation-result-page.module.css";

export const HiddenCalculationResultPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { getHeaderPortalRef } = useContext(LayoutContext);
  const { buildingId } = useParams<{ buildingId: string }>();
  const { mutate: exportPdf, isPending: exportInProgress } =
    useSummaryPdfExport();

  if (!buildingId) return null;

  return (
    <FinPage>
      {createPortal(
        <OrgClosableHeader
          buttonsSection={
            <Group>
              <Button
                variant="outline"
                onClick={() =>
                  navigate(routes.calculation.building.parse(buildingId))
                }
                leftSection={<IconHomeEdit size={24} stroke={1.5} />}
              >
                {t("pages.calculationResult.cta.editBuildingData")}
              </Button>
              <Button
                variant="outline"
                leftSection={<IconFileTypePdf size={24} stroke={1.5} />}
                onClick={() => exportPdf(buildingId)}
                loading={exportInProgress}
              >
                {t("pages.calculationResult.cta.exportPdf")}
              </Button>
            </Group>
          }
        />,
        getHeaderPortalRef().current!
      )}
      <FinPageLayoutColumn>
        <div className={classes.calculationResultPageContent}>
          <Stack gap="20" className={classes.calculationResultPageRenovations}>
            <EnergyEfficiencyPanel buildingId={buildingId} />
            <NewRenovationMeasuresPanel />
          </Stack>
          <CalculationResultPanel buildingId={buildingId} />
        </div>
      </FinPageLayoutColumn>
    </FinPage>
  );
};
