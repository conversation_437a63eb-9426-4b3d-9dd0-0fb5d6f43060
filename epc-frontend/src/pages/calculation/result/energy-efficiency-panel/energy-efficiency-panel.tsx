import { LoadingOverlay, Paper, Stack } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { useGetCalculationResultQuery } from "@data-access/calculation/calculation-queries.ts";

import { EnergyPerformanceScale } from "./energy-performance-scale.tsx";

type Props = {
  buildingId: string;
};

export const EnergyEfficiencyPanel = ({ buildingId }: Props) => {
  const { data, isPending } = useGetCalculationResultQuery(buildingId);
  const { t } = useTranslation();

  const { current, future } = useMemo(() => {
    const currValue = data?.current?.energyEfficiency;
    const futureValue = data?.future?.energyEfficiency;
    return {
      current: currValue && Math.round(currValue),
      future: futureValue && Math.round(futureValue),
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.future?.energyEfficiency, data?.current?.energyEfficiency]);

  return (
    <Paper h="150" py="32" px="50" pos="relative">
      <Stack h="100%" w="100%" mt="30">
        <LoadingOverlay
          visible={isPending}
          zIndex={1000}
          overlayProps={{ radius: "sm", blur: 2 }}
        />
        <EnergyPerformanceScale
          current={current}
          alternative={future}
          getCurrentIndicatorLabel={(current) =>
            t("pages.calculationResult.energyEfficiency.current", {
              value: current,
            })
          }
          getAlternativeIndicatorLabel={(alternative) =>
            t("pages.calculationResult.energyEfficiency.new", {
              value: alternative,
            })
          }
          getGradeLabel={(grade) => t(`core.score.${grade}`)}
        />
      </Stack>
    </Paper>
  );
};
