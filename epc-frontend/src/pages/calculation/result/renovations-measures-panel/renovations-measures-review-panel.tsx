import { Paper, Stack, Title } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { useGetRenovationMeasuresQuery } from "@data-access/renovation/renovation-queries.ts";

import { RenovationsMeasuresTable } from "./renovations-measures-table/renovations-measures-table.tsx";

type Props = {
  buildingId: string;
};

export const RenovationsMeasuresReviewPanel = ({ buildingId }: Props) => {
  const { t } = useTranslation();
  const { data } = useGetRenovationMeasuresQuery(buildingId);

  const selectedMeasures = useMemo(
    () => (data ? data.filter(({ selected }) => selected) : []),
    [data],
  );

  const otherMeasures = useMemo(
    () => (data ? data.filter(({ selected }) => !selected) : []),
    [data],
  );

  return (
    <Stack gap="xl">
      {selectedMeasures.length > 0 && (
        <Stack>
          <Title order={3}>
            {t("pages.calculationResult.measures.summary.selected")}
          </Title>
          <Paper>
            <RenovationsMeasuresTable
              renovationMeasures={selectedMeasures}
              hasStickyHeader={false}
              isSelectionActive={false}
            />
          </Paper>
        </Stack>
      )}
      <Stack>
        <Title order={3}>
          {t("pages.calculationResult.measures.summary.possible")}
        </Title>
        <Paper>
          <RenovationsMeasuresTable
            renovationMeasures={otherMeasures}
            hasStickyHeader={false}
            isSelectionActive={false}
          />
        </Paper>
      </Stack>
    </Stack>
  );
};
