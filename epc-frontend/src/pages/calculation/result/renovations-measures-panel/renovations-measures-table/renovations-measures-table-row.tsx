import {
  Anchor,
  Checkbox,
  getThemeColor,
  Group,
  Stack,
  Table,
  Text,
  useMantineTheme,
} from "@mantine/core";
import { IconCircleCheckFilled, IconCircleXFilled } from "@tabler/icons-react";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";

import {
  Language,
} from "@data-access/calculation/calculation-dto.ts";
import {RenovationDto} from "@data-access/renovation/renovation-dto.ts";
import { NumberDisplay } from "@shared/components/formatters/number-display.tsx";

import { columnConfig } from "./column.config.ts";

type DoubleTextColumnProps = {
  title: string;
  dsc?: string;
  link?: string;
  emphasised?: boolean;
};

const DoubleTextColumn = ({
  dsc,
  title,
  link,
  emphasised,
}: DoubleTextColumnProps) => {
  return (
    <Stack gap="0">
      {link ? (
        <Anchor href={link} target="_blank" rel="noopener noreferrer">
          <Text fw={emphasised ? 600 : 400}>{title}</Text>
        </Anchor>
      ) : (
        <Text fw={emphasised ? 600 : 400}>{title}</Text>
      )}
      <Text fz="sm">{dsc}</Text>
    </Stack>
  );
};

type Props = {
  isSelectionActive: boolean;
  isSelected?: boolean;
  onSelectionChange?: (measure: string) => void;
  item: RenovationDto;
};

export const RenovationsMeasuresTableRow = ({
  isSelectionActive,
  isSelected,
  onSelectionChange,
  item: { id, measure, measureDsc, grantPrograms, cost, recommended },
}: Props) => {
  const { t, i18n } = useTranslation();
  const theme = useMantineTheme();
  const onChange = useCallback(
    () => onSelectionChange && onSelectionChange(id),
    [onSelectionChange, id],
  );

  const notAvailableTxt = t("pages.calculationResult.measures.table.na");
  const currentLanguage = i18n.language.toUpperCase() as Language;

  return (
    <Table.Tr>
      {isSelectionActive && (
        <Table.Td align="center" w={columnConfig.selection.width}>
          <Checkbox
            checked={isSelected}
            onChange={onChange}
            disabled={!recommended}
          />
        </Table.Td>
      )}
      <Table.Td w={columnConfig.measure.width}>
        <DoubleTextColumn
          title={measure[currentLanguage]}
          dsc={measureDsc[currentLanguage]}
          emphasised
        />
      </Table.Td>
      <Table.Td>
        {!Array.isArray(grantPrograms) || !grantPrograms.length ? (
          <Text fz="sm" c="gray.6">
            {notAvailableTxt}
          </Text>
        ) : (
          grantPrograms.map((grantProgram) => (
            <DoubleTextColumn
              title={grantProgram[currentLanguage]?.name}
              dsc={grantProgram[currentLanguage]?.description}
              link={grantProgram[currentLanguage]?.link}
              key={grantProgram[currentLanguage]?.name}
            />
          ))
        )}
      </Table.Td>
      <Table.Td w={columnConfig.cost.width}>
        <Stack gap={0} align="flex-end" miw="180">
          {cost ? (
            <>
              <Group gap={4}>
                <NumberDisplay value={cost.range.first} />
                -
                <NumberDisplay value={cost.range.second} />
              </Group>
              <Group gap={2} fw={600}>
                {t("pages.calculationResult.measures.table.expectedValue")}:
                <NumberDisplay value={cost.mostProbableValue} />
              </Group>
            </>
          ) : (
            <Text fz="sm" c="gray.6">
              {notAvailableTxt}
            </Text>
          )}
        </Stack>
      </Table.Td>
      <Table.Td w={columnConfig.recommended.width}>
        <Stack align="center" justify="center">
          {recommended ? (
            <IconCircleCheckFilled fill={getThemeColor("teal.6", theme)} />
          ) : (
            <IconCircleXFilled fill={getThemeColor("red.5", theme)} />
          )}
        </Stack>
      </Table.Td>
    </Table.Tr>
  );
};
