import { Group, Stack, Table, Text } from "@mantine/core";
import { useTranslation } from "react-i18next";

import { RenovationDto } from "@data-access/renovation/renovation-dto.ts";
import { CommonProps } from "@shared/types/react-utils.ts";

import { columnConfig } from "./column.config.ts";
import { RenovationsMeasuresTableRow } from "./renovations-measures-table-row.tsx";

type Props = {
  className?: CommonProps["className"];
  renovationMeasures: RenovationDto[];
  hasStickyHeader?: boolean;
  isSelectionActive?: boolean;
  selectedRows?: Set<string>;
  onRowSelectionChange?: (selected: string) => void;
};

export const RenovationsMeasuresTable = ({
  className,
  renovationMeasures,
  hasStickyHeader = true,
  isSelectionActive = true,
  selectedRows,
  onRowSelectionChange,
}: Props) => {
  const { t } = useTranslation();

  return (
    <Stack className={className}>
      <Table
        highlightOnHover
        bg="white"
        stickyHeader={hasStickyHeader}
        withColumnBorders
      >
        <Table.Thead>
          <Table.Tr>
            {isSelectionActive && <Table.Th w={columnConfig.selection.width} />}
            <Table.Th w={columnConfig.measure.width}>
              {t("pages.calculationResult.measures.table.measures")}
            </Table.Th>
            <Table.Th>
              {t("pages.calculationResult.measures.table.grant")}
            </Table.Th>
            <Table.Th w={columnConfig.cost.width}>
              <Group wrap="nowrap" gap={4}>
                {t("pages.calculationResult.measures.table.cost")}
                <Text c="gray.6" fz="sm">
                  ({t("core.units.euro")})
                </Text>
              </Group>
            </Table.Th>
            <Table.Th w={columnConfig.recommended.width}>
              {t("pages.calculationResult.measures.table.recommended")}
            </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {renovationMeasures.map((result) => (
            <RenovationsMeasuresTableRow
              isSelectionActive={isSelectionActive}
              key={result.id}
              item={result}
              isSelected={selectedRows && selectedRows.has(result.id)}
              onSelectionChange={onRowSelectionChange}
            />
          ))}
        </Table.Tbody>
      </Table>
    </Stack>
  );
};
