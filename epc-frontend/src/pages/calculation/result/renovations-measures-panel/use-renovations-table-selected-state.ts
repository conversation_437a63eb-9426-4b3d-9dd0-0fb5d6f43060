import { useSet } from "@mantine/hooks";
import { useCallback, useEffect } from "react";

import { RenovationDto } from "@data-access/renovation/renovation-dto.ts";

export const useRenovationsTableSelectedState = (data?: RenovationDto[]) => {
  const selected = useSet<string>([]);

  useEffect(() => {
    data
      ?.filter(({ selected }) => selected)
      .forEach(({ id }) => selected.add(id));
  }, [data, selected]);

  return {
    selected,
    onSelectionChange: useCallback(
      (id: string) => {
        if (selected.has(id)) {
          selected.delete(id);
        } else {
          selected.add(id);
        }
      },
      [selected],
    ),
  };
};
