import { LoadingOverlay, Paper, Stack } from "@mantine/core";
import { useMutationState, useQueryClient } from "@tanstack/react-query";
import _ from "lodash";
import { useContext } from "react";
import { createPortal } from "react-dom";

import { globalApi } from "@data-access/api.ts";
import { useApplyRenovationsMutation } from "@data-access/calculation/calculation-queries.ts";
import { useGetRenovationMeasuresQuery } from "@data-access/renovation/renovation-queries.ts";
import { LayoutContext } from "@providers/layout/layout-context.ts";

import { CalculationResultPageFooter } from "../calculation-result-page-footer.tsx";
import { RenovationsMeasuresTable } from "./renovations-measures-table/renovations-measures-table.tsx";
import { useRenovationsTableSelectedState } from "./use-renovations-table-selected-state.ts";

type Props = {
  buildingId: string;
};

export const RenovationsMeasuresPanel = ({ buildingId }: Props) => {
  const { getFooterPortalRef } = useContext(LayoutContext);
  const { data, isPending: isRenovationMeasuresPending } =
    useGetRenovationMeasuresQuery(buildingId);

  const { selected, onSelectionChange } =
    useRenovationsTableSelectedState(data);

  const queryClient = useQueryClient();
  const renovationApi = globalApi.renovation;

  const { mutate, isPending: isRecalculateWithRenovations } =
    useApplyRenovationsMutation(buildingId);

  const handleApplyRenovations = (selectedRenovations: string[]) => {
    mutate(selectedRenovations, {
      onSuccess: () => {
        void queryClient.invalidateQueries({
          queryKey: renovationApi.getProfitability.key(buildingId),
        });
      },
    });
  };

  const pendingMutations = useMutationState({
    filters: { status : "pending" },
  });

  const isCostGenerationInProgress = !_.isEmpty(pendingMutations)

  return (
    <Stack>
      <Paper>
        <RenovationsMeasuresTable
          renovationMeasures={data || []}
          selectedRows={selected}
          onRowSelectionChange={onSelectionChange}
        />
        <LoadingOverlay
          visible={isRenovationMeasuresPending || isRecalculateWithRenovations}
          zIndex={1}
          overlayProps={{ radius: "sm", blur: 1 }}
        />
        {createPortal(
          <CalculationResultPageFooter
            isRecalculationActive={selected !== undefined}
            isRecalculationPending={isRecalculateWithRenovations || isCostGenerationInProgress}
            onRecalculate={() => handleApplyRenovations([...selected])}
          />,
          getFooterPortalRef().current!,
        )}
      </Paper>
    </Stack>
  );
};
