#!/bin/bash

# This script renames all test files with "V2" postfix in the recommendationStrategy package
# and updates the class names and references inside the files

# Find all test files with "V2Test.kt" postfix
find /Users/<USER>/Projects/finacte-epc/epc-backend/src/test/kotlin/de/finacte/epc/service/calculation/renovation/recommendationStrategy -type f -name "*V2Test.kt" | while read file; do
  # Get the new file name without "V2"
  new_file=$(echo "$file" | sed 's/V2Test\.kt/Test\.kt/')
  
  # Get the class name from the file name
  class_name=$(basename "$file" .kt)
  
  # Get the new class name without "V2"
  new_class_name=$(echo "$class_name" | sed 's/V2Test/Test/')
  
  # Get the strategy class name (the class being tested)
  strategy_class=$(echo "$class_name" | sed 's/Test$//')
  
  # Get the new strategy class name without "V2"
  new_strategy_class=$(echo "$strategy_class" | sed 's/V2$//')
  
  echo "Renaming $file to $new_file"
  echo "Changing class name from $class_name to $new_class_name"
  echo "Changing strategy class reference from $strategy_class to $new_strategy_class"
  
  # Create a temporary file with the updated content
  sed -e "s/$class_name/$new_class_name/g" -e "s/$strategy_class/$new_strategy_class/g" "$file" > "$new_file"
  
  # Remove the old file
  rm "$file"
done

echo "Test file renaming complete!"
